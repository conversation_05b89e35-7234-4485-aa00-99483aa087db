import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AlertType } from '@core/enums/alert-type';

@Component({
  selector: 'app-alert',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './alert.component.html',
  styleUrl: './alert.component.scss',
})
export class AlertComponent {
  @Input() msg = '';
  @Input() alertType = AlertType.Success;
  showAlert: boolean = true;
  @Input() hasClose: boolean = true;
  @Input() isStaticPosition = false;
  close(){
    this.showAlert = false
  }

}
