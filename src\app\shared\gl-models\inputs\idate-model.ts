import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';

export class DateValues {
  formattedGregorian: string | undefined;
  formattedHijri: string | undefined;
  gregorian: NgbDateStruct | undefined;
  hijri: NgbDateStruct | undefined;

  // Convert date to string in the format DD-MM-YYYY
  formatDateToString(date: NgbDateStruct): string {
    if (
      !date ||
      date.day === 0 ||
      date.month === 0 ||
      date.year === 0 ||
      isNaN(date.day) ||
      isNaN(date.month) ||
      isNaN(date.year)
    ) {
      return '';
    }
    const month = String(date.month).padStart(2, '0');
    const day = String(date.day).padStart(2, '0');
    return `${date.year}-${month}-${day}`;
  }

  // Update formatted dates and return the formatted date
  updateFormattedDates(): { formattedGregorian: string | undefined, formattedHijri: string | undefined } {
    if (this.gregorian) {
      this.formattedGregorian = this.formatDateToString(this.gregorian);
    }
    if (this.hijri) {
      this.formattedHijri = this.formatDateToString(this.hijri);
    }
    // Return both formatted dates
    return {
      formattedGregorian: this.formattedGregorian,
      formattedHijri: this.formattedHijri
    };
  }

  // Helper method to map a string date to a selected date object and return it
  mapStringToSelectedDate(dateString: string):NgbDateStruct| undefined {
    const [day, month, year] = dateString.split('-').map(Number);
    if (isNaN(day) || isNaN(month) || isNaN(year)) {
      return undefined; // Return undefined if the date format is invalid
    }
    return { day, month, year };
  }

  // Method to set and get formattedGregorian
  setGregorianDate(date: NgbDateStruct): string {
    this.gregorian = date;
    this.updateFormattedDates(); // Update formattedGregorian
    return this.formattedGregorian ?? ''; // Return formattedGregorian
  }
}
