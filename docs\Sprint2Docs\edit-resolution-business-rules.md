# Edit Resolution Business Rules & Implementation Guide

## Overview
This document outlines the business rules and implementation requirements for the edit Resolution functionality based on user stories JDWA-509, JDWA-567, JDWA-566, JDWA-568, JDWA-506, JDWA-507, JDWA-505.

## API Structure Analysis

### EditResolutionCommand Fields
```typescript
export class EditResolutionCommand {
    id: number;                                    // Resolution ID
    code: string | undefined;                      // Auto-generated resolution code
    resolutionDate: DateTime;                      // Resolution date (required)
    description: string | undefined;               // Resolution description (optional)
    resolutionTypeId: number;                      // Resolution type ID (required)
    newType: string | undefined;                   // Custom type when "Other" selected
    attachmentId: number;                          // Primary attachment ID (required)
    votingType: VotingType;                        // Voting methodology (required)
    memberVotingResult: MemberVotingResult;        // Voting result calculation (required)
    status: ResolutionStatusEnum;                  // Resolution status
    fundId: number;                                // Fund ID
    parentResolutionId: number | undefined;       // For referral resolutions
    oldResolutionCode: string | undefined;        // For referral resolutions
    saveAsDraft: boolean;                          // Save as draft flag
    resolutionItems: ResolutionItemDto[] | undefined;     // Resolution items
    attachmentIds: number[] | undefined;          // Additional attachments
}
```

### ResolutionItemDto Structure
```typescript
export class ResolutionItemDto {
    id: number;                                    // Item ID
    resolutionId: number;                          // Parent resolution ID
    title: string | undefined;                     // Auto-generated (Item 1, Item 2, etc.)
    description: string | undefined;               // Item description (max 500 chars)
    hasConflict: boolean;                          // Conflict of interest flag
    displayOrder: number;                          // Item order
    conflictMembers: ResolutionItemConflictDto[] | undefined;  // Conflict members
    conflictMembersCount: number;                  // Count of conflict members
}
```

## Resolution Status Mapping
```typescript
enum ResolutionStatusEnum {
    _1 = 1,  // Draft
    _2 = 2,  // Pending
    _3 = 3,  // Approved
    _4 = 4,  // Waiting for confirmation
    _5 = 5,  // Confirmed
    _6 = 6,  // Not approved
    _7 = 7,  // Rejected
    _8 = 8,  // Voting in progress
    _9 = 9   // Completing data
}
```

## User Story Business Rules

### JDWA-509: Edit Draft/Pending Resolution (Fund Manager)
**Applicable Statuses**: Draft (_1), Pending (_2)

**Actions Available**:
- **Draft Status**: "Save as Draft" + "Send" buttons
- **Pending Status**: "Send" button only

**Business Logic**:
1. **Save as Draft** (Draft only):
   - Validates required fields (date, type)
   - Saves with status = Draft
   - Shows success message MSG003

2. **Send** (Both statuses):
   - Validates all required fields
   - Changes status to Pending
   - Logs action (resolution creation/edit)
   - Sends notification to legal council & board secretary (MSG002/MSG005)
   - Shows success message MSG003

### JDWA-567: Edit Basic Info (Legal Council/Board Secretary)
**Applicable Statuses**: Waiting for confirmation (_4), Confirmed (_5), Rejected (_7), Voting in progress (_8), Approved (_3), Not approved (_6)

**Actions Available**:
- **Waiting/Confirmed/Rejected**: "Send for confirmation" button
- **Voting in progress**: "Send for confirmation" with vote suspension warning
- **Approved/Not approved**: Creates new referral resolution

**Business Logic**:
1. **Standard Edit** (Waiting/Confirmed/Rejected):
   - Validates required fields
   - Validates items exist (MSG010 if none)
   - Saves with status = "Waiting for confirmation"
   - Logs action (resolution data update)
   - Sends notifications (MSG003)

2. **Voting in Progress Edit**:
   - Shows confirmation dialog (MSG006)
   - If confirmed: suspends voting, updates data, logs actions
   - Sends notifications (MSG007)

3. **Approved/Not Approved Edit**:
   - Shows confirmation dialog (MSG008)
   - If confirmed: generates new resolution code, relates to old resolution
   - Creates referral resolution with status = "Waiting for confirmation"
   - Logs action (referral resolution creation)
   - Sends notifications (MSG009)

### JDWA-566: Edit Resolution Items (Legal Council/Board Secretary)
**Functionality**: Add/Edit/Delete resolution items with conflict management

**Business Logic**:
1. **Add Item**:
   - Auto-generates title (Item 1, Item 2, etc.)
   - Optional description (max 500 chars)
   - Optional conflict of interest with member selection
   - Validates required fields

2. **Delete Item**:
   - Removes item and reorders remaining items
   - Updates item numbers sequentially

3. **View Conflict Members**:
   - Shows popup with conflict member details
   - Displays member names and roles

### JDWA-568: Edit Attachments (Legal Council/Board Secretary)
**Functionality**: Manage resolution attachments

**Business Logic**:
1. **Add Attachment**:
   - File selection dialog
   - Adds to attachments list
   - Increments counter
   - Max 10 files, PDF only, 10MB limit

2. **Delete Attachment**:
   - Removes from list
   - Decrements counter

### JDWA-506, JDWA-507, JDWA-505: Complete Resolution Data
**Applicable Status**: Pending/Completing data (_2/_9)

**Business Logic**: Similar to edit functionality but specifically for completing incomplete resolutions.

## Form Validation Rules

### Required Fields by Action:
- **Save as Draft**: resolutionDate, resolutionTypeId
- **Send/Send for confirmation**: All fields including attachmentId, votingType, memberVotingResult

### Field Constraints:
- **resolutionDate**: Min = fund initiation date, Max = today
- **description**: Optional, supports special characters and spaces
- **newType**: Required only when resolutionTypeId = "Other"
- **attachmentId**: Required for send actions, PDF only, max 10MB
- **resolutionItems**: Optional but triggers confirmation if empty (MSG006/MSG010)

## System Messages Reference
- **MSG001**: Required Field validation error
- **MSG002**: New resolution notification (draft to pending)
- **MSG003**: Success message / Update notification
- **MSG004**: System error
- **MSG005**: Exit confirmation / Edit notification
- **MSG006**: Vote suspension confirmation / No items confirmation
- **MSG007**: Vote suspension notification
- **MSG008**: Approved/Not approved edit confirmation
- **MSG009**: Referral resolution notification
- **MSG010**: No items confirmation for legal council

## Implementation Notes
- Use `this.formGroup.get('fieldName')?.value ?? ''` pattern for API calls
- Keep submit buttons enabled with validation on click
- Support both English and Arabic localization
- Integrate with existing SweetAlert2 patterns for confirmations
- Follow app-form-builder dynamic form patterns

## Implementation Patterns Discovered

### Component Architecture
- **Page-based Edit**: Use full page components for complex edit functionality rather than dialogs
- **Route Structure**: Follow `/admin/investment-funds/resolutions/edit` pattern with query parameters
- **Component Imports**: Include MatDialog for sub-dialogs (items, conflicts, attachments)

### Form Management
- **Pre-population**: Handle DateTime formatting with `toFormat('yyyy-MM-dd')` for form inputs
- **Conditional Fields**: Use `isHidden` property on form controls with dynamic validation
- **Custom Type Detection**: Check for 'other', 'أخرى', 'اخرى' in type names (multilingual support)

### Dialog Components
- **Resolution Item Dialog**: Reusable dialog for add/edit items with conflict management
- **Conflict Members Dialog**: View-only dialog for displaying conflict member details
- **Dialog Data Interfaces**: Use typed interfaces for dialog data passing

### Status-Specific Business Logic
- **Status Mapping**: Use ResolutionStatusEnum with proper business rule implementation
- **Confirmation Dialogs**: Different confirmation messages based on resolution status
- **Button Visibility**: Dynamic button display based on status and user permissions

### API Integration
- **Command Building**: Construct EditResolutionCommand with proper field mapping
- **Error Handling**: Comprehensive validation with user-friendly error messages
- **Loading States**: Visual feedback during API calls with spinner icons

### File Management
- **Attachment Validation**: PDF only, 10MB limit, maximum 10 attachments
- **File Upload Simulation**: Mock implementation ready for real API integration
- **Attachment Display**: Separate existing and new attachments with visual indicators

### Translation Keys
- **Comprehensive Coverage**: 50+ new translation keys for edit functionality
- **Bilingual Support**: Complete English and Arabic translations
- **Contextual Messages**: Status-specific and action-specific messages

### Styling Patterns
- **Status Badges**: Color-coded status indicators with proper contrast
- **Card Layouts**: Consistent card design for items and attachments
- **Responsive Design**: Mobile-friendly layouts with proper breakpoints
- **Loading States**: Consistent spinner and loading message patterns

### Integration Points
- **Navigation**: Proper back navigation with fund context preservation
- **Success Feedback**: SweetAlert2 integration with navigation on success
- **Error Handling**: Centralized error display with ErrorModalService

This implementation provides a comprehensive template for complex edit functionality in JadwaUI applications.
