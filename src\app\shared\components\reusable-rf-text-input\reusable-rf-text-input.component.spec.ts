import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ReusableRfTextInputComponent } from './reusable-rf-text-input.component';

describe('ReusableRfTextInputComponent', () => {
  let component: ReusableRfTextInputComponent;
  let fixture: ComponentFixture<ReusableRfTextInputComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReusableRfTextInputComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ReusableRfTextInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
