import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
} from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Import shared components and services
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// User interfaces
import { IUserFilters, IUserStatus } from '../../interfaces/user.interface';

// Services
import { UserManagementService } from '@shared/services/users/user-management.service';

@Component({
  selector: 'app-user-filter-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    TranslateModule,
    FormBuilderComponent,
    CustomButtonComponent,
  ],
  providers: [UserManagementService],
  templateUrl: './user-filter-dialog.component.html',
  styleUrls: ['./user-filter-dialog.component.scss'],
})
export class UserFilterDialogComponent implements OnInit {
  formGroup!: FormGroup;
  isFormSubmitted = false;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  formControls: IControlOption[] = [];

  constructor(
    public dialogRef: MatDialogRef<UserFilterDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: IUserFilters,
    private formBuilder: FormBuilder,
    private translateService: TranslateService,
    private userManagementService: UserManagementService
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.initializeFormControls();
    this.loadRoles();

    // Pre-populate form with existing filter data
    if (this.data) {
      this.formGroup.patchValue(this.data);
    }
  }

  private initializeFormControls(): void {
    this.formControls = [
      {
        type: InputType.Text,
        formControlName: 'name',
        id: 'name',
        name: 'name',
        label: 'USER_MANAGEMENT.FILTERS.NAME',
        placeholder: 'USER_MANAGEMENT.FILTERS.NAME_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-12',
      },
      {
        type: InputType.Dropdown,
        formControlName: 'status',
        id: 'status',
        name: 'status',
        label: 'USER_MANAGEMENT.FILTERS.STATUS',
        placeholder: 'USER_MANAGEMENT.FILTERS.ALL_STATUSES',
        isRequired: false,
        class: 'col-md-12',
        options: [
          {
            id: '',
            name: this.translateService.instant(
              'USER_MANAGEMENT.FILTERS.ALL_STATUSES'
            ),
          },
          {
            id: IUserStatus.Active,
            name: this.translateService.instant(
              'USER_MANAGEMENT.STATUS.ACTIVE'
            ),
          },
          {
            id: IUserStatus.Inactive,
            name: this.translateService.instant(
              'USER_MANAGEMENT.STATUS.INACTIVE'
            ),
          },
        ],
      },
      {
        type: InputType.Dropdown,
        formControlName: 'role',
        id: 'role',
        name: 'role',
        label: 'USER_MANAGEMENT.FILTERS.ROLE',
        placeholder: 'USER_MANAGEMENT.FILTERS.ALL_ROLES',
        isRequired: false,
        class: 'col-md-12',
        options: [
       
        ],
      },
    ];
  }

  private loadRoles(): void {
    this.userManagementService.getRolesList().subscribe({
      next: (response: any) => {
        if (response && response.data) {
          const rolesControl = this.formControls.find(
            (control) => control.formControlName === 'role'
          );

          if (rolesControl) {
      

            // Add roles from API response
            const apiRoles = response.data.map((role: any) => ({
              id: role.roleName,
              name: role.displayName,
              value: role.displayName,
            }));

            rolesControl.options = [ ...apiRoles];
          
          }
        }
      },
      error: (error) => {
        console.error('Error loading roles for filter:', error);
        // Keep the default "All Roles" option if API fails
      },
    });
  }

  private initForm(): void {
    this.formGroup = this.formBuilder.group({
      name: [''],
      status: [null],
      role: [null],
    });
  }

  dropdownChanged(event: any): void {
    // Handle dropdown changes if needed
    console.log('Dropdown changed:', event);
  }

  applyFilters(): void {
    this.isFormSubmitted = true;
    if (this.formGroup.valid) {
      const filters = this.formGroup.value;
      console.log('Raw form values:', filters);

      // Remove empty values
      const cleanFilters = Object.keys(filters).reduce((acc: any, key) => {
        const value = filters[key];
        if (value !== '' && value !== null && value !== undefined) {
          acc[key] = value;
        } else {
        }
        return acc;
      }, {});

      console.log('Clean filters being sent:', cleanFilters);
      this.dialogRef.close(cleanFilters);
    }
  }

  resetFilters(): void {
    this.formGroup.reset();
    this.isFormSubmitted = false;
      this.dialogRef.close(this.formGroup.value);
  }

  closeDialog(): void {
    this.dialogRef.close();
  }
}
