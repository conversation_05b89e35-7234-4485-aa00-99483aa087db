import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * Saudi Arabia Phone Number Validator
 * 
 * Saudi mobile numbers:
 * - Start with 5 (after country code +966)
 * - Are exactly 9 digits long
 * - Valid prefixes: 50, 51, 52, 53, 54, 55, 56, 57, 58, 59
 * 
 * Examples of valid numbers:
 * - 501234567
 * - 551234567
 * - 591234567
 */
export function saudiPhoneValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null; // Don't validate empty values, use required validator for that
    }

    const phoneNumber = control.value.toString().trim();
    
    // Remove any non-digit characters
    const digitsOnly = phoneNumber.replace(/\D/g, '');
    
    // Check if it's exactly 9 digits
    if (digitsOnly.length !== 9) {
      return {
        saudiPhone: {
          message: 'VALIDATION.SAUDI_PHONE_LENGTH',
          actualLength: digitsOnly.length,
          expectedLength: 9
        }
      };
    }
    
    // Check if it starts with 5
    if (!digitsOnly.startsWith('5')) {
      return {
        saudiPhone: {
          message: 'VALIDATION.SAUDI_PHONE_PREFIX',
          actualPrefix: digitsOnly.charAt(0),
          expectedPrefix: '5'
        }
      };
    }
    
    // Check if the second digit is valid (0-9 are all valid for Saudi numbers)
    const secondDigit = digitsOnly.charAt(1);
    const validSecondDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    if (!validSecondDigits.includes(secondDigit)) {
      return {
        saudiPhone: {
          message: 'VALIDATION.SAUDI_PHONE_INVALID_FORMAT',
          value: digitsOnly
        }
      };
    }
    
    return null; // Valid Saudi phone number
  };
}

/**
 * Formats a Saudi phone number for display
 * @param phoneNumber - The 9-digit phone number
 * @returns Formatted phone number string
 */
export function formatSaudiPhoneNumber(phoneNumber: string): string {
  if (!phoneNumber) return '';
  
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  
  if (digitsOnly.length === 9) {
    // Format as: +966 5X XXX XXXX
    return `+966 ${digitsOnly.substring(0, 2)} ${digitsOnly.substring(2, 5)} ${digitsOnly.substring(5)}`;
  }
  
  return phoneNumber;
}

/**
 * Validates and formats Saudi phone number input
 * @param input - Raw input string
 * @returns Object with validation result and formatted number
 */
export function validateAndFormatSaudiPhone(input: string): {
  isValid: boolean;
  formatted: string;
  errors?: string[];
} {
  if (!input) {
    return { isValid: false, formatted: '', errors: ['Phone number is required'] };
  }
  
  const digitsOnly = input.replace(/\D/g, '');
  const errors: string[] = [];
  
  if (digitsOnly.length !== 9) {
    errors.push(`Phone number must be exactly 9 digits, got ${digitsOnly.length}`);
  }
  
  if (!digitsOnly.startsWith('5')) {
    errors.push('Phone number must start with 5');
  }
  
  const isValid = errors.length === 0;
  const formatted = isValid ? formatSaudiPhoneNumber(digitsOnly) : input;
  
  return {
    isValid,
    formatted,
    errors: errors.length > 0 ? errors : undefined
  };
}

/**
 * Common Saudi mobile network prefixes for reference
 */
export const SAUDI_MOBILE_PREFIXES = {
  STC: ['50', '51', '52', '53', '54', '55'],
  MOBILY: ['56', '57'],
  ZAIN: ['58', '59'],
  // Note: These prefixes may change over time, but all start with 5
};

/**
 * Gets the network provider based on the phone number prefix
 * @param phoneNumber - The 9-digit phone number
 * @returns Network provider name or 'Unknown'
 */
export function getSaudiNetworkProvider(phoneNumber: string): string {
  if (!phoneNumber) return 'Unknown';
  
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  
  if (digitsOnly.length < 2) return 'Unknown';
  
  const prefix = digitsOnly.substring(0, 2);
  
  if (SAUDI_MOBILE_PREFIXES.STC.includes(prefix)) {
    return 'STC';
  } else if (SAUDI_MOBILE_PREFIXES.MOBILY.includes(prefix)) {
    return 'Mobily';
  } else if (SAUDI_MOBILE_PREFIXES.ZAIN.includes(prefix)) {
    return 'Zain';
  }
  
  return 'Unknown';
}
