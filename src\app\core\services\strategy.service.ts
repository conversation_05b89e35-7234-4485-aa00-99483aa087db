import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { SingleResolutionResponseBaseResponse } from '@core/api/api.generated';

export interface StrategyDto {
  id: number;
  nameAr: string;
  nameEn: string;
  updatedAt: string;
}

@Injectable({
  providedIn: 'root'
})
export class StrategyService {
  baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  createStrategy(strategy: StrategyDto): Observable<any> {
    const url = `${this.baseUrl}/api/Strategies/CreateStrategy`;
    return this.http.post(url, strategy);
  }

  updateStrategy(strategy: StrategyDto): Observable<any> {
    const url = `${this.baseUrl}/api/Strategies/UpdateStrategy`;
    return this.http.put(url, strategy);
  }

  getStrategyById(id: number): Observable<StrategyDto> {
    const url = `${this.baseUrl}/api/Strategies/GetStrategyById?id=${id}`;
    return this.http.get<StrategyDto>(url);
  }

  strategyList(pageNumber: number | undefined, pageSize: number | undefined, search: string | undefined, orderBy: string | undefined): Observable<StrategyDto[]> {
    const url = `${this.baseUrl}/api/Strategies/StrategyList?PageNumber=${pageNumber}&PageSize=${pageSize}&search=${search}&OrderBy=${orderBy}`;
    return this.http.get<StrategyDto[]>(url);
  }

  getResolutionById(id: number): Observable<SingleResolutionResponseBaseResponse> {
    const url = `${this.baseUrl}/api/Resolutions/GetResolutionById/${id}`;
    return this.http.get<SingleResolutionResponseBaseResponse>(url);
  }




}
