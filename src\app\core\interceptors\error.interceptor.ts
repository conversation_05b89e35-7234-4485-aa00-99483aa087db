import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError, throwError } from 'rxjs';
import { ErrorModalService } from '../services/error-modal.service';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const errorModalService = inject(ErrorModalService);


  return next(req).pipe(
    catchError(error => {
      let errorMessage = 'An error occurred';

      if (error.error?.StatusCode === 422 && Array.isArray(error.error.Errors)) {
        const messages: string[] = [];

        for (const errObj of error.error.Errors) {
          for (const key in errObj) {
            if (errObj[key]) {
              messages.push(`${key}: ${errObj[key]}`);
            }
          }
        }

        errorMessage = messages.join('\n');
      }

      else if (error.error?.message) {
        errorMessage = error.error.message;
      } else if (error.error?.detail) {
        errorMessage = error.error.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Don't show error modal for 401 errors as they are handled by token interceptor
      if (error.status !== 401) {
        errorModalService.showError(errorMessage);
      }

      return throwError(() => error);
    })
  );


};
