# Use specific Node.js version that works well with Angular 18
FROM node:22 as build

ARG environment

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json to the working directory
COPY package*.json .

RUN rm -rf node_modules package-lock.json
# Install the application dependencies
RUN npm install --force --legacy-peer-deps
# Copy the entire application source code to the working directory
COPY . .

# Build the Angular application
# ENV NODE_OPTIONS="--max-old-space-size=4096"
# RUN  npm list @angular/material @angular/cdknd
RUN  npm run build:${environment}

# Stage 2: Serve the Angular application with Nginx
FROM nginx:alpine
# Remove the default Nginx configuration
RUN rm -rf /usr/share/nginx/html/*

COPY nginx.conf /etc/nginx/conf.d/default.conf

# Create directory for SSL certificates

# Copy the built Angular app from the first stage to the Nginx web server directory
COPY --from=build --chown=nginx /app/dist/jadwa/browser /usr/share/nginx/html

# Copy SSL certificates
# Note: These files should be placed in the ./ssl directory in your project
# COPY cert.cert /etc/nginx/ssl/cert.crt
# COPY cert.key /etc/nginx/ssl/cert.key

# Expose ports that Nginx will listen on (80 for HTTP and 443 for HTTPS)
EXPOSE 80
