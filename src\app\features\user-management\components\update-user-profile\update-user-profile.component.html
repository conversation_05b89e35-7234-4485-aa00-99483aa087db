<!-- Main Content -->
<div class="update-user-profile-page">
  <!-- Breadcrumb -->
  <app-breadcrumb [breadcrumbs]="breadcrumbItems"></app-breadcrumb>

  <div class="mt-3">
    <!-- Page Header -->
    <app-page-header
      [title]="'USER_PROFILE.PAGE_TITLE' | translate">
    </app-page-header>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading && !currentUserData" class="loading-container text-center">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
    </div>
    <p class="mt-2">{{ 'USER_PROFILE.LOADING_PROFILE' | translate }}</p>
  </div>

  <!-- Form Container -->
  <div *ngIf="!isLoading || currentUserData" class="form-container mt-3">
      
      <!-- Profile Photo Section -->
      <div class="profile-photo-section text-center mb-4">
        <div class="photo-container">
          <div class="profile-photo-circle">
            <img
              [src]="getUserPhotoUrl()"
              [alt]="'USER_PROFILE.PERSONAL_PHOTO' | translate"
              class="profile-photo"
              (error)="onImageError($event)">
          </div>
          <!-- Upload overlay for edit mode -->
          <div class="photo-upload-overlay" (click)="triggerPhotoUpload()">
            <i class="fas fa-camera"></i>
          </div>
          <!-- Hidden file input -->
          <input
            #photoFileInput
            type="file"
            accept="image/jpeg,image/jpg,image/png"
            style="display: none;"
            (change)="onPhotoUpload($event)">
        </div>
        <p class="photo-label mt-2">{{ 'USER_PROFILE.PERSONAL_PHOTO' | translate }}</p>
        <button
          type="button"
          class="btn btn-outline-primary btn-sm mt-2"
          (click)="triggerPhotoUpload()">
          <i class="fas fa-upload me-1"></i>
          {{ 'USER_PROFILE.CHANGE_PHOTO' | translate }}
        </button>
      </div>

      <!-- Status and Roles Display Section -->
      <div class="status-roles-section mb-4">
        <div class="row">
          <!-- Status Display -->
          <div class="col-md-6 mb-3">
            <label class="form-label">{{ 'USER_PROFILE.STATUS' | translate }}</label>
            <div class="field-value">
              <span class="badge" [class]="currentUserData?.data?.isActive ? 'badge-success' : 'badge-secondary'">
                {{ (currentUserData?.data?.isActive ? 'USER_MANAGEMENT.STATUS.ACTIVE' : 'USER_MANAGEMENT.STATUS.INACTIVE') | translate }}
              </span>
            </div>
          </div>

          <!-- Roles Display -->
          <div class="col-md-6 mb-3">
            <label class="form-label">{{ 'USER_PROFILE.ROLE' | translate }}</label>
            <div class="field-value">
              <div *ngIf="userRoles && userRoles.length > 0; else noRoles" class="roles-container">
                <span *ngFor="let role of userRoles; let last = last" class="role-chip">
                  {{ role | translate }}
                  <span *ngIf="!last" class="role-separator">, </span>
                </span>
              </div>
              <ng-template #noRoles>
                <span class="no-data">-</span>
              </ng-template>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Fields -->
      <div class="form-fields-section">
        <app-form-builder
          [formGroup]="userProfileForm"
          [formControls]="formControls"
          [isFormSubmitted]="isFormSubmitted"
          (valueChanged)="onValueChange($event.event, $event.control)"
          (keyPressed)="onKeyPressed($event.event, $event.control)"
          (dropdownChanged)="onDropdownChange($event.event, $event.control)"
          (fileUploaded)="onFileUploaded($event)">
        </app-form-builder>
      </div>

      <!-- Change Password Section -->
      <div class="change-password-section mt-4 mb-4">
        <button 
          type="button" 
          class="btn btn-link change-password-link"
          (click)="onChangePassword()">
          <i class="fas fa-key me-2"></i>
          {{ 'USER_PROFILE.CHANGE_PASSWORD' | translate }}
        </button>
      </div>

      <!-- Action Buttons -->
      <div class="actions justify-content-end">
        <app-custom-button
          [buttonType]="ButtonTypeEnum.Secondary"
          [btnName]="'COMMON.CANCEL' | translate"
          (click)="onCancel()">
        </app-custom-button>

        <app-custom-button
          [buttonType]="ButtonTypeEnum.Primary"
          (click)="onSubmit()"
          [btnName]="'COMMON.SAVE_CHANGES' | translate"
         >
        </app-custom-button>
      </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !currentUserData" class="error-container text-center">
    <div class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{ 'USER_PROFILE.LOAD_ERROR' | translate }}
    </div>
    <app-custom-button
      [buttonType]="ButtonTypeEnum.Primary"
      [btnName]="'COMMON.RETRY' | translate"
      (click)="loadCurrentUserData()">
    </app-custom-button>
  </div>
</div>
