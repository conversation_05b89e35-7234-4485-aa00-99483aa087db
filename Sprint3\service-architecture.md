# ⚙️ Service Architecture
## Detailed Documentation

> **A comprehensive guide to the service layer architecture, API integration patterns, and business logic implementation in the Jadwa Investment Web Application**

---

## 📋 Overview

This document provides **detailed information** about the service layer architecture of the Jadwa Investment Web Application, including API services, business logic services, and utility services.

### 🎯 Service Layer Goals
- **🔌 Seamless API Integration**: Type-safe communication with backend services
- **🏗️ Business Logic Encapsulation**: Clean separation of concerns
- **🔄 Reactive Programming**: Observable-based data flow
- **🛡️ Error Handling**: Comprehensive error management and user feedback
- **⚡ Performance**: Optimized data fetching and caching strategies

---

## 📑 Table of Contents

| Section | Description | Key Topics |
|---------|-------------|------------|
| [🔌 API Service Layer](#-api-service-layer) | Auto-generated API clients and configuration | NSwag, HTTP clients, type safety |
| [🏗️ Business Logic Services](#️-business-logic-services) | Core business functionality | Authentication, funds, strategies |
| [🔧 Utility Services](#-utility-services) | Helper and support services | Language, file upload, date conversion |
| [🔄 Service Communication](#-service-communication-patterns) | Communication patterns and data flow | Observables, dependency injection |
| [⚠️ Error Handling](#️-error-handling-strategy) | Error management and user feedback | Global interceptors, user notifications |
| [📋 Data Models](#-data-models-and-interfaces) | Type definitions and interfaces | DTOs, response models, business entities |

---

## 🔌 API Service Layer

### 🤖 Generated API Services (NSwag)

The application uses **NSwag** to automatically generate TypeScript client services from the backend OpenAPI specification. This ensures **type safety** and **consistency** between frontend and backend.

#### 🎯 Key Advantages
- **🔒 Type Safety**: Compile-time error detection
- **🔄 Auto-Sync**: Automatic updates when backend changes
- **📝 Documentation**: Self-documenting API interfaces
- **⚡ Performance**: Optimized HTTP client generation

```mermaid
graph TB
    subgraph "Auto-Generated Services"
        A[AuthenticationServiceProxy]
        B[FundsServiceProxy]
        C[StrategiesServiceProxy]
        D[UsersServiceProxy]
        E[FileManagementServiceProxy]
        F[AuthorizationServiceProxy]
        G[SystemConfigurationServiceProxy]
    end
    
    subgraph "Configuration"
        H[API_BASE_URL Token]
        I[HTTP Client]
        J[Environment Config]
    end
    
    A --> H
    B --> H
    C --> H
    D --> H
    E --> H
    F --> H
    G --> H
    
    H --> I
    I --> J
```

### 🎯 API Service Responsibilities

#### 🔐 AuthenticationServiceProxy
**Purpose**: Handles user authentication and authorization

| Method | Parameters | Return Type | Description |
|--------|------------|-------------|-------------|
| `signIn` | `SignInCommand` | `Observable<IJwtAuthResponseBaseResponse>` | 🔑 User login with credentials |
| `refreshToken` | `RefreshTokenCommand` | `Observable<IJwtAuthResponseBaseResponse>` | 🔄 Token refresh for session extension |
| `updateFCMToken` | `UpdateFCMTokenCommand` | `Observable<BaseResponse>` | 🔔 Firebase messaging token update |

#### 💰 FundsServiceProxy
**Purpose**: Manages investment fund operations

| Method | Parameters | Return Type | Description |
|--------|------------|-------------|-------------|
| `addFund` | `AddFundCommand` | `Observable<BaseResponse>` | ➕ Create new investment fund |
| `editFund` | `AddFundCommand` | `Observable<BaseResponse>` | ✏️ Update existing fund details |
| `getFundDetails` | `id: number` | `Observable<FundDetailsBaseResponse>` | 📊 Retrieve comprehensive fund information |
| `editFundExitDate` | `EditExitDateCommand` | `Observable<BaseResponse>` | 📅 Update fund exit date |

#### 📈 StrategiesServiceProxy
**Purpose**: Handles fund strategy management

| Method | Parameters | Return Type | Description |
|--------|------------|-------------|-------------|
| `createStrategy` | `CreateStrategyCommand` | `Observable<BaseResponse>` | 🆕 Create new investment strategy |
| `updateStrategy` | `UpdateStrategyCommand` | `Observable<BaseResponse>` | 🔄 Update strategy configuration |
| `getStrategyList` | `pageNo, pageSize, search, orderBy` | `Observable<StrategyListResponse>` | 📋 Paginated strategy listing |

### ⚙️ API Configuration

```typescript
// 🔧 API Module Configuration - Centralized Service Registration
@NgModule({
  imports: [HttpClientModule],
  providers: [
    // 🌐 Base URL configuration from environment
    { provide: API_BASE_URL, useValue: environment.apiUrl },

    // 🔌 Auto-generated API service proxies
    AuthenticationServiceProxy,    // 🔐 Authentication & authorization
    FundsServiceProxy,            // 💰 Investment fund management
    StrategiesServiceProxy,       // 📈 Strategy management
    UsersServiceProxy,            // 👥 User management
    FileManagementServiceProxy,   // 📁 File upload & storage
    AuthorizationServiceProxy     // 🛡️ Role-based access control
  ]
})
export class ApiModule { }
```

#### 🔧 Configuration Benefits
- **🎯 Centralized Management**: Single point for API service configuration
- **🌍 Environment Flexibility**: Easy switching between dev/test/prod
- **🔌 Dependency Injection**: Automatic service resolution
- **🔄 Hot Swapping**: Easy service replacement for testing

---

## 🏗️ Business Logic Services

### Authentication Services

```mermaid
graph LR
    subgraph "Authentication Layer"
        A[AuthService]
        B[TokenService]
        C[AuthGuard]
    end
    
    subgraph "Storage"
        D[LocalStorage]
        E[Session State]
    end
    
    subgraph "API"
        F[AuthenticationServiceProxy]
    end
    
    A --> B
    A --> F
    B --> D
    C --> A
    A --> E
```

#### AuthService
```typescript
@Injectable({ providedIn: 'root' })
export class AuthService {
  private readonly ACCESS_TOKEN_KEY = 'auth_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  
  // Core Methods
  login(tokens: { accessToken: string; refreshToken: any }): void
  logout(): void
  isAuthenticated(): boolean
  getAccessToken(): string | undefined
  getRefreshToken(): any | undefined
}
```

#### TokenService
```typescript
@Injectable({ providedIn: 'root' })
export class TokenService {
  // Token Management
  setToken(token: string): void
  getToken(): string | null
  getDecodedToken(): DecodedToken | null
  getUserId(): string | undefined
  getUserRoles(): string[]
  isTokenExpired(): boolean
}
```

### Feature Services

#### FundsService
```typescript
@Injectable({ providedIn: 'root' })
export class FundsService {
  private baseUrl = environment.apiUrl + '/api/Funds';
  
  // CRUD Operations
  addFund(payload: AddFundCommand): Observable<any>
  editFund(payload: AddFundCommand): Observable<any>
  getFundDetailsById(id: number): Observable<any>
  editFundExitDate(exitDate: exitDate): Observable<any>
}
```

#### StrategyService
```typescript
@Injectable({ providedIn: 'root' })
export class StrategyService {
  // Strategy Management
  createStrategy(strategy: StrategyDto): Observable<any>
  updateStrategy(strategy: StrategyDto): Observable<any>
  getStrategyById(id: number): Observable<StrategyDto>
  strategyList(pageNo: number, pageSize: number, search: string, orderBy: string): Observable<any>
  deleteStrategy(id: number): Observable<any>
}
```

## Utility Services

### Language Service

```mermaid
graph TB
    subgraph "Language Management"
        A[LanguageService]
        B[TranslateService]
        C[LocalStorage]
    end
    
    subgraph "UI Updates"
        D[HTML Direction]
        E[Language Attribute]
        F[Component Updates]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    B --> F
```

```typescript
@Injectable({ providedIn: 'root' })
export class LanguageService {
  currentLanguageEvent = new EventEmitter<LanguageEnum>();
  
  // Language Management
  initLang(): void
  switchLang(language: LanguageEnum): void
  switchLangCallBack(language: LanguageEnum): void
}
```

### File Upload Service
```typescript
@Injectable({ providedIn: 'root' })
export class FileUploadService {
  private apiUrl = environment.apiUrl + '/api/Users/<USER>/UploadFile';
  
  uploadFile(file: File, moduleId: number): Observable<any>
}
```

### Date Conversion Service
```typescript
@Injectable({ providedIn: 'root' })
export class DateConversionService {
  convertGregorianToHijri(gregorian: NgbDateStruct): NgbDateStruct
  mapStringToSelectedDate(dateString: string): NgbDateStruct | undefined
}
```

## Service Communication Patterns

### Observable Pattern
```typescript
// Service Method Pattern
public getData(): Observable<DataType> {
  return this.http.get<ApiResponse<DataType>>(this.apiUrl).pipe(
    map(response => response.data),
    catchError(this.handleError),
    shareReplay(1)
  );
}

// Component Consumption
ngOnInit() {
  this.dataService.getData().subscribe({
    next: (data) => this.handleSuccess(data),
    error: (error) => this.handleError(error)
  });
}
```

### Service Injection Pattern
```typescript
// Constructor Injection
constructor(
  private authService: AuthService,
  private fundsService: FundsService,
  private translateService: TranslateService,
  private router: Router
) {}
```

### Error Handling Pattern
```typescript
private handleError(error: HttpErrorResponse): Observable<never> {
  let errorMessage = 'An error occurred';
  
  if (error.error?.message) {
    errorMessage = error.error.message;
  } else if (error.error?.detail) {
    errorMessage = error.error.detail;
  }
  
  this.errorModalService.showError(errorMessage);
  return throwError(() => error);
}
```

## Error Handling Strategy

### Global Error Interceptor
```typescript
export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const errorModalService = inject(ErrorModalService);
  
  return next(req).pipe(
    catchError(error => {
      if (error.status !== 401) {
        errorModalService.showError(getErrorMessage(error));
      }
      return throwError(() => error);
    })
  );
};
```

### Error Modal Service
```typescript
@Injectable({ providedIn: 'root' })
export class ErrorModalService {
  private alertSubject = new BehaviorSubject<any>(null);
  alert$ = this.alertSubject.asObservable();
  
  showError(message: string): void
  showSuccess(message: string): void
  showWarning(message: string): void
  clearAlert(): void
}
```

## Data Models and Interfaces

### Core Interfaces
```typescript
// API Response Pattern
export interface ApiResponse<T> {
  statusCode: number;
  successed: boolean;
  message: string;
  data: T;
  errors: any;
}

// Paginated Response
export interface PaginatedResponse<T> {
  statusCode: number;
  successed: boolean;
  message: string;
  data: T[];
  errors: string[];
  currentPage: number;
  totalCount: number;
  totalPages: number;
  pageSize: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}
```

### Business Models
```typescript
// Fund Models
export interface AddFundCommand {
  id: number;
  name: string;
  strategyId: number;
  strategyName: string;
  status: string;
  statusId: number;
  initiationDate: string;
  oldCode: number;
  propertiesNumber: number;
  attachmentId: number;
  votingTypeId: number;
  legalCouncilId: number;
  fundManagers: number[];
  fundBoardSecretaries: number[];
}

// Strategy Models
export interface StrategyDto {
  id: number;
  nameAr: string;
  nameEn: string;
  updatedAt: string;
}
```

### Authentication Models
```typescript
export interface DecodedToken {
  Id: string;
  email: string;
  roles: string[];
  exp: number;
  iat: number;
}

export interface SignInCommand {
  email: string;
  password: string;
}
```

---

## Best Practices

### Service Design Principles
1. **Single Responsibility**: Each service has a clear, focused purpose
2. **Dependency Injection**: Services are injected rather than instantiated
3. **Observable Patterns**: Consistent use of RxJS for asynchronous operations
4. **Error Handling**: Centralized error handling with user-friendly messages
5. **Type Safety**: Strong typing with TypeScript interfaces
6. **Caching**: Strategic use of shareReplay for performance optimization

### Performance Considerations
- **Lazy Loading**: Services loaded only when needed
- **Memory Management**: Proper subscription cleanup
- **HTTP Optimization**: Request/response interceptors for common operations
- **Caching Strategy**: Intelligent caching of frequently accessed data
