import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import {
  MatExpansionModule,
  MatAccordion,
  MatExpansionPanel,
  MatExpansionPanelHeader,
} from '@angular/material/expansion';
import { TranslateModule } from '@ngx-translate/core';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { Router } from '@angular/router';
import momentHijri from 'moment';
import 'moment/locale/ar';
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { FundGroupDto, FundsServiceProxy } from '@core/api/api.generated';
import moment from 'moment';
import { DateTime } from 'luxon';
import { IconEnum } from '@core/enums/icon-enum';
import { TokenService } from '../../../auth/services/token.service';
import { FilterDialogComponent } from './filter-dialog/filter-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { FundStatus } from '@shared/enum/fund-status';

export interface FundGroupDtoWithExpanded extends FundGroupDto {
  expanded?: boolean;
}

@Component({
  selector: 'app-investment-funds',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatFormFieldModule,
    MatInputModule,
    MatExpansionModule,
    TranslateModule,
    PageHeaderComponent,
    CustomButtonComponent,
    MatExpansionModule,
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelHeader,
    DateHijriConverterPipe,
  ],
  templateUrl: './investment-funds.component.html',
  styleUrls: ['./investment-funds.component.scss'],
})
export class InvestmentFundsComponent implements OnInit {
  fundGroups: FundGroupDtoWithExpanded[] | undefined;
  FundStatus = FundStatus;
  originalFundGroups: FundGroupDtoWithExpanded[] | undefined;
  isNoData: boolean = true;
  createButtonIcon = IconEnum;
  filter: any;
  isHasPermissionAdd: boolean = false;

  constructor(
    private router: Router,
    private FundsService: FundsServiceProxy,
    private dialog: MatDialog,
    private TokenService: TokenService
  ) {
    momentHijri.locale('ar');
  }
  getFundLabel(count: number): string {
    if (count === 1) {
      return 'FUND_GROUP.COUNT.ONE';
    } else if (count === 2) {
      return 'FUND_GROUP.COUNT.TWO';
    } else if (count >= 3 && count <= 10) {
      return 'FUND_GROUP.COUNT.FEW';
    } else {
      return 'FUND_GROUP.COUNT.MANY';
    }
  }
  ngOnInit(): void {
    // this.originalFundGroups = [...this.fundGroups]; // keep unfiltered copy
    this.getFunds();
    this.isHasPermissionAdd = this.TokenService.hasPermission('Fund.Create');
  }
  getFunds(filter?: any) {
    const creationDateFrom = filter?.creationDateFrom
      ? DateTime.fromObject({
        year: filter.creationDateFrom.year,
        month: filter.creationDateFrom.month,
        day: filter.creationDateFrom.day+1
      })
      : undefined;

    const creationDateTo = filter?.creationDateTo
    ? DateTime.fromObject({
      year: filter.creationDateTo.year,
      month: filter.creationDateTo.month,
      day: filter.creationDateTo.day +1
    })      : undefined;

    const strategyId = filter?.strategyId ?? undefined;
    const search = filter?.search ?? '';
    const orderBy = 'Count desc';
    const pageNumber = 0;
    const pageSize = 100;

    this.FundsService.fundsList(
      creationDateFrom,
      creationDateTo,
      strategyId,
      pageNumber,
      pageSize,
      search,
      orderBy
    ).subscribe((res) => {
      if (res.successed && res.data && res.data.length > 0) {
        this.fundGroups = res.data;
        this.originalFundGroups = this.fundGroups;
        this.fundGroups[0].expanded = true;
        this.isNoData = false;
      } else {
        this.isNoData = true;
      }
    });
  }

  onCreateNewFund() {
    this.router.navigate(['/admin/investment-funds/create']);
  }
  handlePanelOpen(openedIndex: number): void {
    this.fundGroups?.forEach((group, index) => {
      group.expanded = index === openedIndex;
    });
  }
  navigateToUpdate(fund: any): void {
    // let isHasPermission = this.TokenService.hasPermission('Fund.Edit');
    if (fund.statusId === 1 && this.TokenService.hasPermission('Fund.Complete')) {
      this.router.navigate([
        '/admin/investment-funds/complete-fund-info',
        fund.id,
      ]);
    } else if(this.TokenService.hasPermission('Fund.View')){
      this.router.navigate(['/admin/investment-funds/fund-details'], {
        queryParams: { id: fund.id },
      });
    }
  }

  onSearch(searchTerm: string): void {
    const term = searchTerm.toLowerCase();
    if (!this.originalFundGroups) return;

    this.fundGroups = this.originalFundGroups
      .map((group) => {
        const titleMatches = group.title?.toLowerCase().includes(term) ?? false;

        const filteredFunds =
          group.funds?.filter((fund) =>
            fund.name?.toLowerCase().includes(term)
          ) ?? [];

        if (titleMatches || filteredFunds.length > 0) {
          const updatedGroup = new FundGroupDto();
          updatedGroup.title = group.title;
          updatedGroup.hasNotification = group.hasNotification;
          updatedGroup.funds = titleMatches ? group.funds : filteredFunds;

          // If the `count` property is computed from funds:
          Object.defineProperty(updatedGroup, 'count', {
            get: () => updatedGroup.funds?.length ?? 0,
          });

          // If you need to call any methods like init(), do it here:
          // updatedGroup.init?.();

          return updatedGroup;
        }

        return null;
      })
      .filter((group): group is FundGroupDto => group !== null);
    this.fundGroups[0].expanded = true;
  }

  toggleExpanded(group: FundGroupDtoWithExpanded) {
    group.expanded = !group.expanded;
  }

  statusColors = {
    active: 'green',
    underCreation: 'blue',
    noMembers: 'orange',
    withdrawn: 'grey',
  };
  getStatusClass(status: FundStatus | undefined): string {
    switch (status) {
      case FundStatus.Active:
        return 'status-green';
      case FundStatus.UnderConstruction:
        return 'status-blue';
      case FundStatus.WaitingForAddingMembers:
        return 'status-orange';
      case FundStatus.Exited:
        return 'status-grey';
      default:
        return 'status-blue';
    }
  }
  formatDateToString(dateTime: DateTime): string {
    return moment(dateTime.toJSDate()).format('YYYY-MM-DD');
  }

  convertToHijri(gregorianDate: string): string {
    momentHijri.locale('ar');
    const hijriDate = momentHijri(gregorianDate).format('iD-iMMMM-iYYYY');
    return hijriDate; // This will return the date in Arabic by default
  }

  openFilter(): void {
    const dialogRef = this.dialog.open(FilterDialogComponent, {
      width: '390px',
      height: '599px',
      data: this.filter,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getFunds(result);
        this.filter = result;
      }
    });
  }
}
