<div class="page-header">
    <div class="container-fluid px-0">
        <div class="row align-items-center justify-content-between ">
            <div class="col-12" >
                <h2 [ngStyle]="{'margin-bottom':'8px'}" class="header">{{ title | translate }}</h2>
                <p *ngIf="showSubHeader" class="sub-header">{{subHeaderValue.split('+')[0] | translate}}: <span>{{subHeaderValue.split('+')[1]}}</span></p>
            </div>
            <div class="col-md-4" *ngIf="showSearch">
              <div class="search-filter mt-3">
                <mat-form-field appearance="outline" class="search-input" title="">
                  <input
                    matInput  title=""
                    [placeholder]="searchPlaceholder | translate"
                    [(ngModel)]="searchValue"
                    (keydown.enter)="onSearch()"
                    (keydown)="restrictToNumbers($event)" />
                  <span matPrefix class="search-icon">
                    <img src="assets/images/search.png" alt="" />
                  </span>
                </mat-form-field>

                <div *ngIf="showFilter" (click)="onFilterClick()" [title]="'COMMON.FILTER' | translate">
                  <img src="assets/images/filter.png" alt="" /> <!-- Disables img tooltip -->
                </div>
              </div>
            </div>
            <div class="col-md-5">
              <ng-content select="[slot='between']"></ng-content>
            </div>
            <div class="col-md-3 create-button-container" *ngIf="showCreateButton">
                <app-custom-button class="header-button" [disabled]="exceedMaxNumber" [btnName]="createButtonText | translate"
                    [iconName]="createButtonIcon" (click)="onCreateClick()"></app-custom-button>
            </div>
        </div>
    </div>

</div>
