# Sprint 3 Task Breakdown - Jadwa Fund Management System

## Executive Summary

This document provides a detailed task breakdown for implementing the remaining Sprint 3 features in the Jadwa Fund Management System. Based on analysis of the Sprint 3 documentation and existing codebase, this plan focuses on completing the user management features that have not yet been implemented.

## Current Implementation Status

### Completed Features
- **User List Component**: Basic implementation at `src/app/features/user-management/components/user-list/`
- **Create User Component**: Implementation at `src/app/features/user-management/create-user/`

### API Integration Status
The following API endpoints are already available in `src/app/core/api/api.generated.ts`:

#### Authentication Service Proxy
- `signIn(body: SignInCommand)` - `/api/Users/<USER>/Sign-In`
- `signOut(body: SignOutCommand)` - `/api/Users/<USER>/Sign-Out`
- `refreshToken(body: RefreshTokenCommand)` - `/api/Users/<USER>/Refresh-Token`
- `validateToken(body: AccessTokenQuery)` - `/api/Users/<USER>/ValidateToken`
- `isValid_Token(body: AccessTokenQuery)` - `/api/Users/<USER>/Is-Valid_Token`
- `updateFCMToken(body: UpdateFCMTokenCommand)` - `/api/Users/<USER>/UpdateFCMToken`

#### User Management Service Proxy
- `userList(role, isActive, name, pageNumber, pageSize, search, orderBy)` - `/api/Users/<USER>/UserList`
- `getUserById(id: number)` - `/api/Users/<USER>/GetUserById`
- `addUser(body: AddUserCommand)` - `/api/Users/<USER>/AddUser`
- `updateUser(body: EditUserCommand)` - `/api/Users/<USER>/UpdateUser`
- `updateUserLanguage(body: EditUserPreferredLanguageCommand)` - `/api/Users/<USER>/UpdateUserLanguage`
- `userRoles(id: number)` - `/api/Users/<USER>/UserRoles`
- `updateUserRoles(body: EditUserRolesCommand)` - `/api/Users/<USER>/UpdateUserRoles`
- `deleteUser(id: number)` - `/api/Users/<USER>/DeleteUser`
- `changePasswordForUser(body: ChangePasswordCommand)` - `/api/Users/<USER>/ChangePasswordForUser`
- `getFundManagerUsers()` - `/api/Users/<USER>/GetFundManagerUsers`
- `getBoardSecretaryUsers()` - `/api/Users/<USER>/GetBoardSecretaryUsers`
- `getLegalCouncilUsers()` - `/api/Users/<USER>/GetLegalCouncilUsers`
- `userListForBoardMembers(fundId, pageNumber, pageSize, search, orderBy)` - `/api/Users/<USER>/UserListForBoardMembers`
- `getCurrentCultureLanguage()` - `/api/Users/<USER>/GetCurrentCultureLanguage`
- `getUserProfile(userId?: number)` - `/api/Users/<USER>/GetUserProfile`
- `updateUserProfile(body: Body2)` - `/api/Users/<USER>/UpdateUserProfile`
- `activateUser(body: ActivateUserCommand)` - `/api/Users/<USER>/ActivateUser`
- `adminResetPassword(body: AdminResetPasswordCommand)` - `/api/Users/<USER>/AdminResetPassword`
- `resendRegistrationMessage(body: ResendRegistrationMessageCommand)` - `/api/Users/<USER>/ResendRegistrationMessage`

### API Integration Details for Implemented Components

#### User List Component
- **Endpoint**: `/api/Users/<USER>/UserList`
  - **Method**: GET
  - **Parameters**:
    ```typescript
    {
      role?: string; // Optional role filter
      isActive?: boolean; // Optional active status filter
      name?: string; // Optional name filter
      pageNumber?: number; // 1-based page number
      pageSize?: number; // Number of items per page
      search?: string; // Optional search term
      orderBy?: string; // Optional sort field
    }
    ```
  - **Response**: Paginated list of users (return type is `void` in current API - needs proper typing)
  - **Error Handling**: Displays empty state for no results, error message for API failures
  - **Pagination**: Server-side pagination with page size options (10, 25, 50)

#### Create User Component
- **Endpoint**: `/api/Users/<USER>/AddUser`
  - **Method**: POST
  - **Request Body**: `AddUserCommand`
    ```typescript
    {
      name: string;
      email: string;
      mobile: string; // Saudi mobile format (05XXXXXXXX)
      iban?: string;
      nationality?: string;
      passportNo?: string;
      status: string;
      role: string;
      password: string;
      isActive: boolean;
      registrationMessageSent: boolean;
      registrationCompleted: boolean;
    }
    ```
  - **Response**: `StringBaseResponse` with created user details
  - **Error Handling**:
    - Field validation errors displayed inline
    - Email uniqueness validation
    - Mobile number format validation
    - Role validation for single-holder roles

### Recently Implemented Features (July 15, 2025)

#### User Story #1213: View System Users List ✅ COMPLETED
- **Implementation Location**: `src/app/features/user-management/components/user-list/`
- **Key Features Implemented**:
  - Comprehensive user list display with pagination
  - Conditional action button visibility based on user status and registration flags
  - Sortable columns (Name, Email, Role, Last Update Date)
  - Switch component for active/inactive status toggle
  - Action buttons: View, Edit, Reset Password, Resend Message
  - Empty state and no results state handling
  - Proper integration with existing table component

#### User Story #1217: Filter System Users List ✅ COMPLETED
- **Implementation Location**: `src/app/features/user-management/components/user-filter-dialog/`
- **Key Features Implemented**:
  - Hybrid filtering approach as specified:
    - Inline mobile number search with real-time filtering
    - Advanced filter popup for Name, Status, and Role filters
  - Filter dialog component following established patterns
  - Clear filters functionality
  - Integration with user list component
  - Proper filter state management

#### Supporting Components Created
- **Search Input Component**: `src/app/shared/components/search-input/`
  - Reusable search input with debouncing
  - Configurable width (263px as per user preferences)
  - Clear button functionality
  - RTL support

#### Updated User Interface Model
- **Enhanced IUser Interface**: Added Sprint 3 required fields
  - `registrationMessageIsSent: boolean`
  - `registrationIsCompleted: boolean`
  - Additional profile fields (countryCode, iban, nationality, etc.)
- **Updated IUserFilters Interface**: Added filtering capabilities
- **Updated IUserActionEvent Interface**: Added new action types

### Implementation Decisions and Architecture

#### Component Architecture
- **Modular Design**: Separated user list functionality into dedicated components
- **Reusable Components**: Created shared `SearchInputComponent` for consistent search UI
- **Dialog Pattern**: Used Material Dialog for advanced filters following established patterns
- **Event-Driven Communication**: Implemented proper parent-child component communication

#### Action Button Visibility Logic
Implemented according to Sprint 3 specifications:
- **View & Edit**: Always visible for all users
- **Reset Password**: Visible only if:
  - User status = Active AND
  - Registration Is Completed = true AND
  - Registration Message Is Sent = true
- **Resend Message**: Visible only if:
  - User status = Active AND
  - Registration Is Completed = false AND
  - Registration Message Is Sent = true

#### Filtering Implementation
- **Hybrid Approach**: Inline mobile search + popup for other filters
- **Real-time Search**: Mobile number filtering with debouncing (300ms)
- **State Management**: Proper filter state preservation and clearing
- **Backend Ready**: Architecture supports both frontend and backend filtering

#### User Interface Enhancements
- **Registration Flags**: Added required boolean fields for registration tracking
- **Status Management**: Enhanced status handling with proper enum values
- **Profile Fields**: Added optional profile fields (IBAN, nationality, passport, etc.)
- **File Support**: Prepared interface for CV and photo file uploads

### Remaining Features to Implement

1. Edit User Component
2. User Details Component
3. User Profile Management (Self-Service)
4. User Login/Logout
5. User Password Management
6. User Activation/Deactivation
7. Password Reset Functionality
8. Registration Message Resending
9. Advanced User Filtering

## Detailed Task Breakdown

### Phase 1: Core User Management Components (Estimated: 8 days)

#### 1.1 Edit User Component (3 days)
**Description**: Implement the edit user component to allow administrators to modify existing user information.

**Tasks**:
- Create edit-user component structure (0.5 day)
  - Generate component files in `src/app/features/user-management/components/edit-user/`
  - Add route in `user-management.routes.ts`
- Implement form with pre-populated user data (1 day)
  - Reuse form structure from create-user component
  - Implement user data fetching by ID
  - Handle non-editable fields (Mobile No.)
- Implement role management logic (1 day)
  - Add validation for single-holder roles
  - Implement role update confirmation dialogs
- Add form submission and API integration (0.5 day)
  - Implement update user service method
  - Add success/error handling

**API Integration**:
- **Endpoint**: `/api/Users/<USER>/GetUserById`
  - **Method**: GET
  - **Parameters**: `id: number` - User ID to fetch
  - **Response**: User details including name, email, mobile, roles, etc.
  - **Error Handling**: Display appropriate error messages for 404 (user not found) and other errors

- **Endpoint**: `/api/Users/<USER>/UpdateUser`
  - **Method**: PUT
  - **Request Body**:
    ```typescript
    {
      id: number;
      name: string;
      email: string;
      iban?: string;
      nationality?: string;
      passportNo?: string;
      status: string;
      isActive: boolean;
    }
    ```
  - **Response**: `StringBaseResponse` with success message or validation errors
  - **Error Handling**: Display field-specific validation errors and general error messages

- **Endpoint**: `/api/Users/<USER>/UserRoles`
  - **Method**: GET
  - **Parameters**: `id: number` - User ID
  - **Response**: List of user roles
  - **Error Handling**: Display appropriate error messages

**Acceptance Criteria**:
- Form pre-populates with existing user data
- Mobile number is displayed but not editable
- Role changes follow business rules for single-holder roles
- Form validation works correctly
- Success/error messages are displayed appropriately

**Dependencies**:
- Existing create-user component structure
- User management service

**Complexity**: Medium

#### 1.2 User Details Component (2 days)
**Description**: Create a read-only view of user details with comprehensive information display.

**Tasks**:
- Create user-details component structure (0.5 day)
  - Generate component files in `src/app/features/user-management/components/user-details/`
  - Add route in `user-management.routes.ts`
- Implement user data fetching and display (1 day)
  - Create layout for displaying all user attributes
  - Implement user data fetching by ID
- Add action buttons for related operations (0.5 day)
  - Edit, Reset Password, Resend Message, etc.
  - Implement conditional display based on user status

**API Integration**:
- **Endpoint**: `/api/Users/<USER>/GetUserById`
  - **Method**: GET
  - **Parameters**: `id: number` - User ID to fetch
  - **Response**: Complete user details
  - **Error Handling**: Handle 404 for user not found, display loading states

- **Endpoint**: `/api/Users/<USER>/UserRoles`
  - **Method**: GET
  - **Parameters**: `id: number` - User ID
  - **Response**: List of user roles with details
  - **Error Handling**: Display appropriate error messages

**Acceptance Criteria**:
- All user attributes are displayed correctly
- Role assignments are clearly shown
- Registration status is visible
- Action buttons are conditionally displayed based on user status
- Navigation to edit user works correctly

**Dependencies**:
- User management service

**Complexity**: Low

#### 1.3 User Profile Management (3 days)
**Description**: Implement self-service profile management for logged-in users based on JDWA-1280.

**Tasks**:
- Create user-profile component structure (0.5 day)
  - Generate component files in `src/app/features/user-management/components/user-profile/`
  - Add route in app routing
- Implement profile form with current user data (1 day)
  - Create form with editable/non-editable fields as per requirements
  - Implement current user data fetching
- Add file upload functionality (1 day)
  - Implement CV upload (PDF/DOCX, max 10MB)
  - Implement Personal Photo upload (JPG/PNG, max 2MB)
  - Add file validation and preview
- Implement form submission and API integration (0.5 day)
  - Create profile update service method
  - Add success/error handling

**API Integration**:
- **Endpoint**: `/api/Users/<USER>/GetUserProfile`
  - **Method**: GET
  - **Parameters**: `userId?: number` (optional - if not provided, gets current user profile)
  - **Response**: `UserProfileResponseDtoBaseResponse` with current user profile data
  - **Error Handling**: Handle unauthorized access, redirect to login if session expired

- **Endpoint**: `/api/Users/<USER>/UpdateUserProfile`
  - **Method**: PUT
  - **Request Body**: `Body2` (multipart form data for profile update with file uploads)
  - **Response**: `StringBaseResponse` with success message
  - **Error Handling**: Display field validation errors, handle email uniqueness validation

**Note**: The API supports file uploads through the `updateUserProfile` method using multipart form data. CV and photo uploads are handled within the same endpoint rather than separate endpoints.

**Acceptance Criteria**:
- User can view and update their personal information
- Non-editable fields (User ID, Status, Role, Mobile No.) are displayed as labels
- CV and Personal Photo uploads work with proper validation
- Email uniqueness is validated
- Success/error messages are displayed appropriately

**Dependencies**:
- Authentication service
- File upload service

**Complexity**: High

### Phase 2: Authentication Features (Estimated: 7 days)

#### 2.1 User Login Implementation (3 days)
**Description**: Implement the login functionality based on JDWA-1267.

**Tasks**:
- Create login component structure (0.5 day)
  - Generate component files in `src/app/features/auth/login/`
  - Add route in app routing
- Implement login form (1 day)
  - Create form with mobile number and password fields
  - Add validation for Saudi mobile format
- Implement authentication service (1 day)
  - Create login method with JWT handling
  - Implement session storage
- Add conditional redirection logic (0.5 day)
  - Redirect to password reset if registration not completed
  - Redirect to dashboard if registration completed

**API Integration**:
- **Endpoint**: `/api/Users/<USER>/Sign-In`
  - **Method**: POST
  - **Request Body**: `SignInCommand`
    ```typescript
    {
      mobile: string; // Saudi mobile format (05XXXXXXXX)
      password: string;
    }
    ```
  - **Response**: `JwtAuthResponseBaseResponse` containing token, refresh token, and user info
  - **Error Handling**:
    - 400: Invalid credentials
    - 404: User not found
    - 401: Account locked/suspended

- **Endpoint**: `/api/Users/<USER>/Is-Valid_Token`
  - **Method**: POST
  - **Request Body**: `AccessTokenQuery`
    ```typescript
    {
      token: string;
    }
    ```
  - **Response**: `StringBaseResponse` with token validity status
  - **Error Handling**: Handle expired/invalid tokens

**Acceptance Criteria**:
- User can log in with mobile number and password
- Validation works for Saudi mobile format
- Appropriate error messages are displayed for invalid credentials
- User is redirected based on registration status
- JWT token is stored securely

**Dependencies**:
- Backend authentication API

**Complexity**: Medium

#### 2.2 User Password Management (3 days)
**Description**: Create the password change/reset functionality based on JDWA-1268.

**Tasks**:
- Create password-management component structure (0.5 day)
  - Generate component files in `src/app/features/auth/password-management/`
  - Add routes in app routing
- Implement password change form (1 day)
  - Create form with current password, new password, and confirm password fields
  - Add password complexity validation
- Implement first-login password reset (1 day)
  - Create conditional UI based on registration status
  - Implement forced password change on first login
- Add service methods and API integration (0.5 day)
  - Create password change/reset service methods
  - Update registration status flag on successful reset

**API Integration**:
- **Endpoint**: `/api/Users/<USER>/ChangePasswordForUser`
  - **Method**: PUT
  - **Request Body**: `ChangePasswordCommand`
    ```typescript
    {
      userId?: number; // Optional for self-service
      currentPassword?: string; // Required for profile change
      newPassword: string;
      confirmPassword: string;
    }
    ```
  - **Response**: `StringBaseResponse` with success message and updated registration status
  - **Error Handling**:
    - 400: Password complexity validation errors
    - 401: Current password incorrect
    - 422: Passwords don't match

**Note**: The API uses a single `changePasswordForUser` method for both self-service password changes and first-login password resets. The behavior is determined by the request parameters.

**Acceptance Criteria**:
- User can change password from profile
- First-time login forces password reset
- Password complexity rules are enforced
- Registration status is updated after successful reset
- User is redirected appropriately after password change/reset

**Dependencies**:
- Authentication service
- User profile component

**Complexity**: Medium

#### 2.3 User Logout Implementation (1 day)
**Description**: Implement secure logout functionality based on JDWA-1269.

**Tasks**:
- Create logout service method (0.5 day)
  - Implement token invalidation
  - Clear client-side session data
- Add logout UI elements (0.5 day)
  - Add logout button in header/menu
  - Implement confirmation dialog (optional)

**API Integration**:
- **Endpoint**: `/api/Users/<USER>/Sign-Out`
  - **Method**: POST
  - **Request Body**: `SignOutCommand`
    ```typescript
    {
      refreshToken: string; // Current refresh token to invalidate
    }
    ```
  - **Response**: `StringBaseResponse` with success message
  - **Error Handling**:
    - Always clear local storage/cookies regardless of API response
    - Handle network errors gracefully
    - Redirect to login even if API call fails

**Acceptance Criteria**:
- User session is terminated on logout
- Client-side session data is cleared
- User is redirected to login screen
- Logout works even with expired sessions

**Dependencies**:
- Authentication service

**Complexity**: Low

### Phase 3: Advanced User Management Features (Estimated: 6 days)

#### 3.1 User Activation/Deactivation (1.5 days)
**Description**: Implement the functionality to activate or deactivate user accounts based on JDWA-1253.

**Tasks**:
- Enhance user list switch component (0.5 day)
  - Implement status toggle functionality
  - Add confirmation dialogs
- Implement service methods (0.5 day)
  - Create activate/deactivate user methods
  - Add validation for single-holder roles
- Add success/error handling (0.5 day)
  - Implement status update notifications
  - Handle edge cases and errors

**API Integration**:
- **Endpoint**: `/api/Users/<USER>/ActivateUser`
  - **Method**: POST
  - **Request Body**: `ActivateUserCommand`
    ```typescript
    {
      userId: number;
    }
    ```
  - **Response**: `StringBaseResponse` with success message and updated user status
  - **Error Handling**: Handle business rule violations

**Note**: The API currently only has an `activateUser` method. User deactivation may be handled through the `updateUser` method by setting the `isActive` property to false, or a separate deactivation endpoint may need to be implemented on the backend.

**Acceptance Criteria**:
- Admin can activate/deactivate users via switch component
- Confirmation dialog is shown before status change
- Single-holder role validation prevents deactivation when needed
- Status is updated in the UI after successful change

**Dependencies**:
- User management service
- User list component

**Complexity**: Medium

#### 3.2 Password Reset Functionality (1.5 days)
**Description**: Create the admin-initiated password reset feature based on JDWA-1257.

**Tasks**:
- Implement reset password action in user list (0.5 day)
  - Add reset password button with confirmation dialog
  - Implement conditional display based on user status
- Create service methods (0.5 day)
  - Implement admin password reset API call
  - Add notification sending logic
- Add success/error handling (0.5 day)
  - Implement reset confirmation messages
  - Handle edge cases and errors

**API Integration**:
- **Endpoint**: `/api/Users/<USER>/AdminResetPassword`
  - **Method**: POST
  - **Request Body**: `AdminResetPasswordCommand`
    ```typescript
    {
      userId: number;
      sendNotification: boolean; // Whether to send notification to user
    }
    ```
  - **Response**: `StringBaseResponse` with success message and possibly a temporary password
  - **Error Handling**:
    - 400: User not eligible for password reset
    - 404: User not found
    - 500: Notification sending failure

**Acceptance Criteria**:
- Admin can reset password for eligible users
- Confirmation dialog is shown before reset
- Reset action is only available for active/pending users
- Success/error messages are displayed appropriately

**Dependencies**:
- User management service
- User list component

**Complexity**: Medium

#### 3.3 Registration Message Resending (1.5 days)
**Description**: Implement the functionality to resend registration messages based on JDWA-1225.

**Tasks**:
- Implement resend message action in user list (0.5 day)
  - Add resend message button with confirmation dialog
  - Implement conditional display based on user status
- Create service methods (0.5 day)
  - Implement resend message API call
  - Add eligibility validation
- Add success/error handling (0.5 day)
  - Implement resend confirmation messages
  - Handle edge cases and errors

**API Integration**:
- **Endpoint**: `/api/Users/<USER>/ResendRegistrationMessage`
  - **Method**: POST
  - **Request Body**: `ResendRegistrationMessageCommand`
    ```typescript
    {
      userId: number;
    }
    ```
  - **Response**: `StringBaseResponse` with success message and operation status
  - **Error Handling**:
    - 400: User not eligible for message resend
    - 404: User not found
    - 422: User has already completed registration
    - 500: Message sending failure

**Note**: Eligibility checking can be done by examining the user's registration status from the `getUserById` endpoint before attempting to resend the message.

**Acceptance Criteria**:
- Admin can resend registration messages to eligible users
- Confirmation dialog is shown before resending
- Resend action is only available for pending users
- Success/error messages are displayed appropriately

**Dependencies**:
- User management service
- User list component

**Complexity**: Medium

#### 3.4 Advanced User Filtering (1.5 days)
**Description**: Add comprehensive filtering and search capabilities to the user list based on JDWA-1217.

**Tasks**:
- Implement filter component (0.5 day)
  - Create filter form with role, status, and date filters
  - Add search input for name/email
- Enhance user list component (0.5 day)
  - Implement filter application logic
  - Add clear filters functionality
- Integrate with backend filtering (0.5 day)
  - Update service methods to support filtering
  - Implement pagination with filters

**API Integration**:
- **Endpoint**: `/api/Users/<USER>/UserList`
  - **Method**: GET
  - **Parameters**:
    ```typescript
    {
      role?: string; // Filter by role
      isActive?: boolean; // Filter by active status
      name?: string; // Filter by name
      pageNumber?: number;
      pageSize?: number;
      search?: string; // Search in name/email
      orderBy?: string; // Sort field
    }
    ```
  - **Response**: Paginated and filtered user list
  - **Error Handling**: Handle invalid filter parameters

**Note**: The current API supports basic filtering by role, active status, and name. Additional filtering capabilities may need to be implemented on the backend or handled through client-side filtering of the returned results.

**Acceptance Criteria**:
- Users can be filtered by role, status, and registration flags
- Search works for name and email
- Multiple filters can be combined
- Pagination works with applied filters
- Clear filters functionality resets all filters

**Dependencies**:
- User management service
- User list component

**Complexity**: Medium

### Phase 4: Testing & Integration (Estimated: 5 days)

#### 4.1 Unit Testing (2 days)
**Description**: Create unit tests for all components and services.

**Tasks**:
- Write tests for user management components (1 day)
  - Test form validation logic
  - Test conditional display logic
- Write tests for authentication components (1 day)
  - Test login/logout functionality
  - Test password management

**Acceptance Criteria**:
- All components have unit tests
- All services have unit tests
- Tests cover edge cases and error scenarios
- Tests pass consistently

**Dependencies**:
- All implemented components and services

**Complexity**: Medium

#### 4.2 Integration Testing (2 days)
**Description**: Test complete workflows and API integrations.

**Tasks**:
- Test user management workflows (1 day)
  - Test create-edit-view-delete user flow
  - Test status change and password reset flows
- Test authentication workflows (1 day)
  - Test login-logout flow
  - Test first-time login and password reset flow

**Acceptance Criteria**:
- All workflows function correctly end-to-end
- API integrations work as expected
- Error handling works correctly
- Edge cases are handled properly

**Dependencies**:
- All implemented components and services

**Complexity**: Medium

#### 4.3 Performance Optimization (1 day)
**Description**: Optimize user list performance and implement efficient data loading.

**Tasks**:
- Implement virtual scrolling for large user lists (0.5 day)
  - Add virtual scroll component
  - Optimize rendering for large datasets
- Optimize API calls and data loading (0.5 day)
  - Implement efficient pagination
  - Add caching where appropriate

**Acceptance Criteria**:
- User list loads within 2 seconds
- Scrolling through large lists is smooth
- Pagination works efficiently
- API calls are minimized

**Dependencies**:
- User list component
- User management service

**Complexity**: Medium

## Implementation Timeline

Total estimated time: **26 days**

- **Phase 1: Core User Management Components** - 8 days
- **Phase 2: Authentication Features** - 7 days
- **Phase 3: Advanced User Management Features** - 6 days
- **Phase 4: Testing & Integration** - 5 days

## Technical Considerations

### Form Builder Integration
- Leverage existing form-builder patterns from create-user component
- Use Tel input type for Saudi phone validation
- Follow shared styling patterns (form-container.scss)

### Validation Strategy
- Client-side validation using Angular reactive forms
- Server-side validation for security
- Saudi phone number validation using existing validator

### Role Management Logic
- Multi-select enabled only for specific role combinations
- Single-holder role validation and replacement workflow
- Dynamic UI based on role selection

### File Upload Strategy
- CV files: PDF/DOCX, max 10MB
- Profile photos: JPG/PNG, max 2MB
- Secure file storage integration

### Performance Considerations
- Virtual scrolling for large user lists
- Lazy loading of user details
- Efficient API pagination and filtering

## API Improvements Needed

Based on the analysis of the current API generated file, the following improvements should be considered:

### Return Type Issues
- `userList()` method returns `void` instead of proper paginated result type
- `getUserById()` method returns `void` instead of user details
- `userRoles()` method returns `void` instead of role list

### Missing Endpoints
- User deactivation endpoint (currently only activation is available)
- Enhanced filtering options for user list (date ranges, multiple status filters)
- File upload endpoints for CV and photos (may be integrated into profile update)

### Recommended Backend Updates
1. Update return types for user management endpoints to provide proper response models
2. Implement user deactivation endpoint or enhance existing endpoints
3. Add comprehensive filtering support to user list endpoint
4. Ensure proper error response models are returned

## Conclusion

This task breakdown provides a structured approach to implementing the remaining Sprint 3 features. By following this plan, the development team can efficiently complete all required user management functionality while maintaining consistency with the existing codebase and meeting the requirements specified in the Sprint 3 documentation.

**Important Note**: Some API endpoints may need backend updates to provide proper return types and additional functionality as outlined in the API Improvements section above.
