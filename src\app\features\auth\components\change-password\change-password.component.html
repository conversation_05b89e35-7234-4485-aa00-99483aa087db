<div class="change-password" [ngStyle]="{'background-image': 'url(' + (currentLang == 'en' ? 'assets/images/login-en-bg.jpg' : 'assets/icons/bg-login.png') + ')'}">

    <div class="d-flex justify-content-between align-items-center margin-bottom-24">
    <img [src]="currentLang =='en'? 'assets/images/logo-new-en.png' :'assets/icons/jadwa-logo.png'" class="" alt="icon" height="114"  />
    <div>
      <button class="btn primary-btn lang-btn" (click)="changeLanguage()">
        <img src="assets/icons/icon-lang.png" class="mx-1" alt="icon"  />
        {{'LOGIN_PAGE.LANG' | translate}}
      </button>
    </div>
  </div>
  <div class="change-password-container" >

  <p class="font-size-m navy-color mb-0 bold-400">{{'LOGIN_PAGE.CHANGE_PASSWORD' | translate}} </p>
  <img src="assets/icons/title-shape.png" class="" alt="icon"  />

    <div class="">

        <div class="form-fields">
            <app-form-builder [formControls]="formControls" [formGroup]="formGroup" [isFormSubmitted]="isFormSubmitted"  >
                <div slot="between" class="hr-first-container">
                    <ul class="password-rules p-0 list-unstyled">
                        <li class="title mb-2">{{ 'LOGIN_PAGE.PASSWORD_RULES.TITLE' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_1' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_2' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_3' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_4' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_5' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_6' | translate }}</li>
                      </ul>
                </div>

            </app-form-builder>

          </div>


    <div class="my-4 w-50 d-flex flex-column justify-content-center align-items-center w-50">
      <app-custom-button type="submit" class="w-100 change-password-btn"  [iconName]="" [btnName]="'LOGIN_PAGE.CHANGE_PASSWORD' | translate" (click)="onSubmit()"></app-custom-button>

    </div>

  </div>
</div>
</div>

