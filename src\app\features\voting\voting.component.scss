.voting-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  max-width: 400px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

.boxes-stats {
  margin-bottom: 24px;

  .total-box {
    background-color: #e6e1d7;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 18px;
      color: #333;
      margin-bottom: 8px;
    }

    .count {
      font-size: 32px;
      font-weight: bold;
      color: #000;
    }
  }

  .boxes-status {
    display: flex;
    gap: 16px;
    justify-content: space-between;

    .status-box {
      flex: 1;
      padding: 16px;
      border-radius: 12px;
      text-align: center;

      h3 {
        margin: 0;
        font-size: 16px;
        color: #333;
        margin-bottom: 8px;
      }

      .count {
        font-size: 28px;
        font-weight: bold;
        color: #000;
      }

      &.active {
        background-color: #e8f1f9;
      }

      &.inactive {
        background-color: #e2e2e2;
      }
    }
  }
}

.voting-header {
  text-align: right;
  margin-bottom: 24px;

  .user-info {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 16px;

    .profile-image {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      margin-left: 12px;
    }

    .user-name {
      font-size: 16px;
      color: #333;
      font-weight: 500;
    }
  }

  .voting-title {
    font-size: 24px;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    font-weight: 600;
  }

  .voting-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

.voting-content {
  text-align: center;

  .remaining-elements {
    font-size: 16px;
    color: #333;
    margin-bottom: 16px;
  }

  .vote-button {
    background-color: #0f3c6c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 12px 32px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover:not(:disabled) {
      background-color: #0a2d52;
    }

    &:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
  }
} 