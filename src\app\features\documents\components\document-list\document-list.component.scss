.document-list-container {
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    gap: 16px;

    p {
      color: #718096;
      margin: 0;
    }
  }

  .empty-state {
    text-align: center;
    padding: 48px 24px;
    color: #718096;

    .empty-icon {
      margin-bottom: 16px;

      mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #cbd5e0;
      }
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #4a5568;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  .documents-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
}

// RTL Support
[dir="rtl"] {
  .document-list-container {
    .empty-state {
      text-align: center;
    }
  }
}
