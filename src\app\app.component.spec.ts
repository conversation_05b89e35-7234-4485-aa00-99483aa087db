import { TestBed } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { LanguageService } from '@core/gl-services/language-services/language.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

describe('AppComponent', () => {
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AppComponent,TranslateModule.forRoot()],
      providers: [
        LanguageService,
        TranslateService
      ],
    }).compileComponents();
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });

});
