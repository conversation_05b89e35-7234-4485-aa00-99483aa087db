@import "../../../../assets/scss/variables";

.upload-container {
  border: 2px dashed $navy-blue;
  background: #fff;
  border-radius: 8px;
  padding: 32px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
  //opacity: 0.3;

  &:hover {
    border-color: --navy-blue;
  }
  &.disabled{
    opacity: 30%;
  }
}
.wrap{
  flex-wrap: wrap;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-text {
  font-weight: bold;
  color: $navy-blue;
  margin: 0;
}

.subtext {
  font-size: 13px;
  color: $navy-blue;
  margin: 0;
}

.upload-button {
  margin-top: 8px;
  background-color: transparent;
  border: none;
  color: #2563eb;
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    color: --navy-blue;
  }
}
