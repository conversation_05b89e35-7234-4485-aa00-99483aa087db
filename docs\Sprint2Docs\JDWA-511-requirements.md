# 📋 JDWA-511: Create a Resolution - Fund Manager

## 🎯 User Story

**As a** fund manager  
**I want** to create a new resolution  
**So that** I can manage resolutions within my funds

## 📖 Description

This feature enables fund managers to create new resolutions for their managed funds. The resolution creation process supports both draft and final submission workflows, with automatic code generation, validation, and notification systems.

## 🔄 Process Flow

### Primary Flow: Save as Draft
1. From resolutions screen, user presses "add new resolution"
2. System displays "new resolution" screen
3. User fills in basic info required data
4. User presses "Save as draft" button
5. System validates entering required data
   - If any violation exists, system displays error message **MSG001**
6. System performs the following actions:
   - Generates resolution code, format: `(fund code/resolution year/resolution no.)`
   - Saves resolution with status "draft"
   - Displays success message **MSG003**

### Alternative Flow: Send for Approval
1. User presses "send" button instead of "Save as draft"
2. System validates entering required data
   - If any violation exists, system displays error message **MSG001**
3. System performs the following actions:
   - Generates resolution code (serial no), format: `(fund code/resolution year/resolution no.)`
   - Saves resolution with status "pending"
   - Logs resolution action details: action name (resolution creation), date, user (user name), role
   - Sends notification to legal council && board secretary (if exist) attached to the fund **MSG002**
   - Displays success message **MSG003**

## ✅ Acceptance Criteria

| Scenario | Given | When | Then |
|----------|-------|------|------|
| Missing required fields | The user has not filled in all mandatory fields | The user clicks "send"/save as draft | An error message is displayed indicating which fields are required. **MSG001** |
| Successful resolution submission | The user is on the add resolution screen | The user fills in the required fields and clicks "send"/save as | The new resolution is saved and a confirmation message is displayed. **MSG003** |
| Unknown error during submission | The user is connected to the internet | The user clicks "send/save as draft" | An error message is displayed indicating unknown error, and the order remains unsaved. **MSG004** |

## 📝 Data Field Validation

| Field Title (AR) | Field Title (EN) | Type | Required | Nullable | Decimals | Special Chars | Spaces | Min | Max | Condition | Sample Data AR | Sample Data EN |
|------------------|------------------|------|----------|----------|----------|---------------|--------|-----|-----|-----------|----------------|----------------|
| **Screen Elements** |
| تاريخ القرار | Resolution date | Date | Y | N | N | N | N | Min (fund initiation date) | Max (today) | Gregorian/Hijri | | |
| وصف القرار | Description | Text | N | N | N | Y | Y | | Max (500) char | | | |
| نوع القرار | Type | DDL, One select | Y | | | | | | | | استحواذ – تخارج- بيع – توزيع أرباح – تمديد مدة الصندوق – تعديل شروط واحكام الصندوق – الموافقة على القوائم المالية – تعيين مقدمي خدمات – موافقة على شروط واحكام الصندوق – أخرى | |
| نوع القرار المضاف | New type | Text | Y | N | N | N | Y | | | Appears in case user select (اخرى) | | |
| ملف القرار | Resolution file | File | Y | | | | | | 1 file, Size up to (10 Mb) | File type (PDF) | | |
| الية التصويت للقرار | Voting methodology | Option | Y | | | | | | | editable, Display value as fund voting type | جميع الأعضاء - أغلبية الأعضاء | All members - Majority |
| احتساب نتيجة التصويت للاعضاء | Members Voting result | Option | N | | | | | | | | جميع البنود - أغلبية البنود | All items - Majority of items (default selection) |

## 🎬 Screen Actions

| Action (AR) | Action (EN) | Type | Description |
|-------------|-------------|------|-------------|
| Save as draft | Save as draft | Button | Saves resolution with draft status |
| send | send | Button | Submits resolution for approval |
| cancel | cancel | Button | Back to resolutions list screen |

## 💬 System Messages

| Message ID | Arabic Message | English Message | Subject | Type |
|------------|----------------|-----------------|---------|------|
| MSG001 | حقل إلزامي | Required Field | | Error |
| MSG002 | تم إضافة قرار جديد ضمن فعاليات صندوق "اسم الصندوق" بواسطة مدير الصندوق "اسم المستخدم" يرجى استكمال بيانات القرار | A new resolution is added attached to fund "fund name" by fund manager "user name", kindly complete resolution info. | استكمال بيانات القرار/ completing resolution data | Notification |
| MSG003 | تم حفظ البيانات بنجاح | Record Saved Successfully | | Success |
| MSG004 | حدث خطأ بالنظام , لم يتم حفظ البيانات | An error is occurred while saving data | | Error |

## 🔗 Related User Stories

- **JDWA-509**: Edit a draft/pending resolution - fund manager
- **JDWA-508**: Cancel a pending resolution - fund manager
- **JDWA-588**: View draft/pending/cancelled resolution details - fund manager
- **JDWA-504**: Resolutions (Parent Epic)

## 🎯 Business Rules

### Resolution Code Generation
- Format: `{fund_code}/{resolution_year}/{resolution_no}`
- Auto-generated and unique per fund
- Sequential numbering within each year

### Status Management
- **Draft**: Editable by fund manager, not visible to other roles
- **Pending**: Submitted for approval, visible to legal council and board secretary
- **Approved**: Final status after approval process
- **Cancelled**: Cancelled by fund manager before approval

### Voting Configuration
- Inherits fund's default voting methodology
- Can be overridden per resolution
- Options: All members, Majority of members
- Voting result calculation: All items, Majority of items

### File Upload Requirements
- File type: PDF only
- Maximum size: 10 MB
- One file per resolution
- Required for submission

### Notification Rules
- Draft save: No notifications sent
- Send for approval: Notify legal council and board secretary
- Include fund name, user name, and resolution details

## 🔐 Security & Permissions

### Required Permissions
- `Resolution.Create`: Create new resolutions
- `Resolution.SaveDraft`: Save resolution as draft
- `Resolution.Submit`: Submit resolution for approval

### Role-Based Access
- **Fund Manager**: Can create, edit, and cancel resolutions for their funds
- **Legal Council**: Receives notifications for pending resolutions
- **Board Secretary**: Receives notifications for pending resolutions

## 🌐 Internationalization

### Supported Languages
- Arabic (Primary)
- English (Secondary)

### Translation Keys Required
- Form labels and placeholders
- Validation error messages
- Success and confirmation messages
- Resolution type options
- Voting methodology options

---

**📝 Note**: This requirements document serves as the foundation for the technical design and implementation phases.
