// See https://firebase.google.com/docs/cloud-messaging/js/receive#setting_notification_options_in_the_service_worker
// Give the service worker access to Firebase Messaging.
// Note that you can only use Firebase Messaging here, other Firebase libraries
// are not available in the service worker.

// Add event listener for install event to improve reliability
self.addEventListener('install', event => {
  console.log('Firebase Messaging SW installed');
  self.skipWaiting(); // Force activation on install
});

// Add event listener for activate event
self.addEventListener('activate', event => {
  console.log('Firebase Messaging SW activated');
  event.waitUntil(self.clients.claim()); // Take control of all clients
});

// Import Firebase scripts with error handling
try {
  importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js');
  importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js');
  console.log('Firebase scripts loaded successfully');
} catch (e) {
  console.error('Error loading Firebase scripts:', e);
}

// Initialize the Firebase app in the service worker by passing in the configuration
firebase.initializeApp({
  apiKey: "AIzaSyCyYIcNpjLjpgDkL3piGUVYCfoWYqmVhus",
  authDomain: "jadwa-6a040.firebaseapp.com",
  projectId: "jadwa-6a040",
  storageBucket: "jadwa-6a040.firebasestorage.app",
  messagingSenderId: "939943387803",
  appId: "1:939943387803:web:7064ea9c94abfb82715c3e",
  measurementId: "G-8Y87V9Z4NR"
});

// Retrieve an instance of Firebase Messaging so that it can handle background messages.
const messaging = firebase.messaging();


// Optional: Add message handler for background notifications
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);

  // Customize notification here
  const notificationTitle = payload.notification.title || 'Notification';
  const notificationOptions = {
    body: payload.notification.body || 'New notification',
    icon: '/assets/icons/jadwa-logo.png'
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});
