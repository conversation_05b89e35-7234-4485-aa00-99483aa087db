import { TestBed } from '@angular/core/testing';
import { SpinnerService } from './spinner.service';

describe('SpinnerService', () => {
  let service: SpinnerService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(SpinnerService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should start with loading false', () => {
    expect(service.isLoading).toBeFalse();
    expect(service.activeRequestsCount).toBe(0);
  });

  it('should show spinner on first request', () => {
    service.show();
    expect(service.isLoading).toBeTrue();
    expect(service.activeRequestsCount).toBe(1);
  });

  it('should handle multiple concurrent requests', () => {
    service.show();
    service.show();
    service.show();

    expect(service.isLoading).toBeTrue();
    expect(service.activeRequestsCount).toBe(3);

    service.hide();
    expect(service.isLoading).toBeTrue();
    expect(service.activeRequestsCount).toBe(2);

    service.hide();
    service.hide();
    expect(service.isLoading).toBeFalse();
    expect(service.activeRequestsCount).toBe(0);
  });

  it('should not go below zero requests', () => {
    service.hide();
    service.hide();

    expect(service.activeRequestsCount).toBe(0);
    expect(service.isLoading).toBeFalse();
  });

  it('should force hide spinner', () => {
    service.show();
    service.show();
    service.forceHide();

    expect(service.isLoading).toBeFalse();
    expect(service.activeRequestsCount).toBe(0);
  });

  it('should provide debug info', () => {
    service.show();
    const debugInfo = service.getDebugInfo();

    expect(debugInfo.isLoading).toBeTrue();
    expect(debugInfo.activeRequests).toBe(1);
  });

  it('should emit state changes', (done) => {
    let emissionCount = 0;

    service.state$.subscribe(state => {
      emissionCount++;

      if (emissionCount === 1) {
        // Initial state
        expect(state.isLoading).toBeFalse();
        expect(state.activeRequests).toBe(0);
      } else if (emissionCount === 2) {
        // After show()
        expect(state.isLoading).toBeTrue();
        expect(state.activeRequests).toBe(1);
      } else if (emissionCount === 3) {
        // After hide()
        expect(state.isLoading).toBeFalse();
        expect(state.activeRequests).toBe(0);
        done();
      }
    });

    service.show();
    service.hide();
  });
});
