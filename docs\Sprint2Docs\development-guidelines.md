# 📋 JadwaUI Development Guidelines

## 🎯 Overview

This document outlines the comprehensive development guidelines for the JadwaUI project, incorporating lessons learned and best practices established during Sprint 2 development.

## 🚨 **MANDATORY STANDARDS FOR LIST VIEWS**

**As of December 2024, all list-based user stories MUST follow the comprehensive standards outlined in:**

📋 **[List View Development Standards](./list-view-development-standards.md)**

### Quick Reference - Non-Negotiable Requirements:

1. **✅ Card Layout**: Individual cards (not grid items) matching Figma design exactly
2. **✅ Real API Integration**: NSwag-generated service proxies only (NO mock data)
3. **✅ Advanced Search**: Modal dialogs following fund filter-dialog pattern
4. **✅ Server-side Pagination**: API pageNo/pageSize parameters required
5. **✅ Figma Design Compliance**: Exact match with provided designs
6. **✅ Comprehensive Task Breakdown**: Follow the 10-task structure (4h 20min total)

**Reference Implementation**: The Resolutions feature serves as the template for all future list-based features.

### ⚠️ Breaking Changes from Previous Standards:
- **NO MORE MOCK DATA**: All components must use real API integration
- **NO MORE TABLE LAYOUTS**: All list views must use card-based layouts
- **MANDATORY ADVANCED SEARCH**: All list views must include advanced search modals
- **STRICT FIGMA COMPLIANCE**: Designs must match Figma specifications exactly

## 🌍 Localization Requirements

### Translation Keys
1. **Ensure all placeholder text has proper localization keys** in both Arabic and English translation files
2. **Use descriptive, hierarchical key structure**: `FEATURE.SECTION.SPECIFIC_KEY`
3. **Test translations** in both languages before deployment
4. **Add new keys to both `ar.json` and `en.json`** files simultaneously

### Example Translation Structure
```json
{
  "FEATURE_NAME": {
    "FORM": {
      "FIELD_NAME": "Field Label",
      "FIELD_NAME_PLACEHOLDER": "Enter field value"
    },
    "MESSAGES": {
      "SUCCESS": "Operation completed successfully",
      "ERROR": "An error occurred"
    }
  }
}
```

## 🔧 Form Validation & Submit Behavior

### Submit Button Guidelines
1. **Keep submit buttons enabled at all times** - never disable based on form validity
2. **Validate on submit click** and display validation errors to guide the user
3. **Prevent duplicate submissions** using proper state management

### Implementation Pattern
```typescript
onSubmit(formValue: any) {
  this.isValidationFire = true;

  // Always validate form and show errors to guide user
  if (this.formGroup.valid) {
    // Prevent duplicate submissions
    if (!this.isFormSubmitted) {
      this.isFormSubmitted = true;
      this.callApi();
    }
  } else {
    // Mark all fields as touched to show validation errors
    this.markAllFieldsAsTouched();
  }
}

private markAllFieldsAsTouched() {
  Object.keys(this.formGroup.controls).forEach(key => {
    this.formGroup.get(key)?.markAsTouched();
  });
}
```

### Form Validation Rules
1. **Use comprehensive validation** including:
   - Required field validation
   - Min/max length constraints
   - Data type validation (numbers, emails, etc.)
   - Custom business rule validation
   - Pattern validation where applicable

2. **Implement proper error messages** with localization support
3. **Use CustomValidators service** for reusable validation logic

## 📅 Date Field Handling

### API Date Pattern
When sending date values to API endpoints, use this consistent pattern:
```typescript
initiationDate: this.formGroup.get('initiationDate')?.value ?? ''
```

### Date Validation
- Use `CustomValidators.notFutureDate` for fields that shouldn't accept future dates
- Use `CustomValidators.notPastDate` for fields that shouldn't accept past dates
- Implement date range validation for start/end date pairs

## 🏗️ Component Development Patterns

### Create Components
- **Reference Template**: `src/app/features/investment-funds/components/create-fund/`
- **Use NSwag-generated service proxies** instead of custom services
- **Implement standalone component architecture**
- **Use app-form-builder** for dynamic form generation

### Edit/Update Components  
- **Reference Template**: `src/app/features/investment-funds/components/update-fund/`
- **Follow same patterns as create components**
- **Handle pre-population of form data**

### List Components
- **Reference Template**: `src/app/features/fund-strategies/`
- **Use table components for data display**
- **Implement proper pagination and filtering**

## 📁 File Upload Guidelines

### Configuration
```typescript
{
  type: InputType.file,
  formControlName: 'attachmentId',
  allowedTypes: ['pdf'], // Supports both 'pdf' and '.pdf' formats
  max: 10, // Max size in MB
  isRequired: true
}
```

### Validation
- File type validation is case-insensitive
- Supports both dot-prefixed and non-prefixed file extensions
- Proper error messages with localization
- Dynamic accept attribute generation

## 🚫 Critical Issues to Avoid

### 1. Duplicate Form Submission
**Problem**: Having both `(formSubmit)` event and button `(click)` event calling the same submit method

**Solution**: 
- Remove unused `(formSubmit)` events from form-builder usage
- Use only button click events for form submission
- Implement proper submission state management

### 2. Form Builder Event Binding
**Avoid**: 
```html
<app-form-builder (formSubmit)="onSubmit($event)">
```

**Use**:
```html
<app-form-builder [formControls]="formControls" [formGroup]="formGroup">
```

## 🔄 API Integration Patterns

### Service Usage
1. **Use NSwag-generated API clients** from `api.generated.ts`
2. **Run `nswag run`** before API integration work
3. **Use generated models** instead of custom model files
4. **Handle API errors consistently**

### Error Handling
```typescript
this.apiService.method(data).subscribe({
  next: (response) => {
    this.errorModalService.showSuccess(response.message);
    // Handle success
  },
  error: (error) => {
    this.isFormSubmitted = false;
    this.isValidationFire = false;
    // Handle error
  }
});
```

## 📝 Documentation Requirements

### Code Documentation
1. **Document complex business logic** with clear comments
2. **Use TypeScript interfaces** for type safety
3. **Document API integration patterns**

### Sprint Documentation
1. **Update technical design documents** with implementation details
2. **Document architectural decisions** and their rationale
3. **Maintain implementation guides** with current patterns

## ✅ Testing & Integration

### Pre-Deployment Checklist
- [ ] Form validation works correctly for all field types
- [ ] File upload functionality tested with various file types and sizes
- [ ] No duplicate API calls on form submission
- [ ] All localization keys exist in both language files
- [ ] Submit buttons remain enabled and show proper validation errors
- [ ] Date field handling follows established patterns
- [ ] API integration uses NSwag-generated proxies

### Integration Testing
1. **Test with backend API** to ensure complete functionality
2. **Verify form submission flow** from UI to API
3. **Test file upload** with actual file storage service
4. **Validate error handling** for various failure scenarios

## 🎨 UI/UX Guidelines

### Design Consistency
1. **Follow Figma design specifications** at provided design links
2. **Maintain established design system patterns**
3. **Ensure proper RTL/LTR support** for Arabic/English languages
4. **Use consistent spacing and typography**

### Form Design
1. **Use card-based design patterns** for navigation integration
2. **Implement proper loading states** during API calls
3. **Show clear validation feedback** to users
4. **Maintain consistent button styling and behavior**

---

**📌 Note**: These guidelines should be followed for all new development and used to refactor existing code where applicable. Regular reviews should ensure adherence to these patterns.
