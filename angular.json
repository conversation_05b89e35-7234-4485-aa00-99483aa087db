{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"jadwa": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/jadwa", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js", "src/polyfills.ts", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, "src/assets", "src/manifest.json", "src/firebase-messaging-sw.js"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "allowedCommonJsDependencies": ["core-js", "canvg", "raf", "rgbcolor", "html2canvas", "dompurify", "file-saver", "buffer"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "4MB"}, {"type": "anyComponentStyle", "maximumWarning": "20kB", "maximumError": "40kB"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "test": {"budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "4MB"}, {"type": "anyComponentStyle", "maximumWarning": "20kB", "maximumError": "40kB"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "jadwa:build:production"}, "test": {"buildTarget": "jadwa:build:test"}, "development": {"buildTarget": "jadwa:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing", "src/polyfills.ts", "@angular/localize/init"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, "src/assets", "src/manifest.json", "src/firebase-messaging-sw.js"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}