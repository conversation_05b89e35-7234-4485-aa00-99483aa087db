# 🎨 Component Development Template
## [COMPONENT_NAME] - Angular Component Implementation

> **[Brief description of the component and its purpose within the Jadwa Investment Web Application]**

---

## 🧩 Component Overview

### 📊 Component Summary
| Field | Value |
|-------|-------|
| **Component ID** | COMP-[YYYY]-[###] |
| **Component Name** | [Descriptive component name] |
| **Component Type** | [Feature/Shared/Layout/Form/Display] |
| **Location** | `src/app/[features/shared]/[path]/` |
| **Selector** | `app-[component-name]` |
| **Standalone** | Yes (Angular 18+ pattern) |
| **Complexity** | [Simple/Medium/Complex] |
| **Reusability** | [High/Medium/Low] |

### 🎯 Component Purpose
- **Functional Purpose**: [What does this component do?]
- **User Interface Role**: [How does it contribute to the UI?]
- **Business Value**: [What business need does it serve?]
- **Reusability Scope**: [Where can this component be reused?]

---

## 🏗️ Component Architecture

### 📁 File Structure
Following the established Jadwa application patterns:

```
src/app/[features/shared]/[component-path]/
├── [component-name].component.ts      # Component logic
├── [component-name].component.html    # Template
├── [component-name].component.scss    # Styles
├── [component-name].component.spec.ts # Unit tests
└── index.ts                          # Barrel export (if needed)
```

### 🎨 Component Classification

**Component Type: [Select One]**

#### 🎯 Feature Component
- **Purpose**: Business-specific functionality
- **Location**: `src/app/features/[feature-name]/components/`
- **Characteristics**: Domain-specific, feature-bound
- **Examples**: `InvestmentFundsComponent`, `FundDetailsComponent`

#### 🔧 Shared Component
- **Purpose**: Reusable UI elements
- **Location**: `src/app/shared/components/`
- **Characteristics**: Generic, highly reusable
- **Examples**: `TableComponent`, `CustomButtonComponent`

#### 🏠 Layout Component
- **Purpose**: Application structure
- **Location**: `src/app/core/layout/`
- **Characteristics**: Structural, persistent
- **Examples**: `AdminLayoutComponent`, `HeaderComponent`

#### 📝 Form Component
- **Purpose**: Data input and validation
- **Location**: `src/app/[features/shared]/components/`
- **Characteristics**: Form-focused, validation-heavy
- **Examples**: `FormBuilderComponent`, `DatePickerComponent`

---

## 💻 Component Implementation

### 🎯 Component Class Structure

```typescript
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';

// Import shared components and services
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { [RequiredService] } from '@core/services/[service-name].service';

// Import interfaces and models
import { [ComponentDataInterface] } from '@shared/interfaces/[interface-name]';
import { [EnumName] } from '@core/enums/[enum-name]';

@Component({
  selector: 'app-[component-name]',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    // Add required component imports
    CustomButtonComponent,
    // Add other shared components
  ],
  templateUrl: './[component-name].component.html',
  styleUrls: ['./[component-name].component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush // For performance optimization
})
export class [ComponentName]Component implements OnInit, OnDestroy {

  // Input properties
  @Input() data: [ComponentDataInterface][] = [];
  @Input() loading: boolean = false;
  @Input() disabled: boolean = false;
  @Input() config: [ConfigInterface] | null = null;

  // Output events
  @Output() itemSelected = new EventEmitter<[ComponentDataInterface]>();
  @Output() actionTriggered = new EventEmitter<[ActionEventInterface]>();
  @Output() dataChanged = new EventEmitter<[ComponentDataInterface][]>();

  // Component state
  selectedItem: [ComponentDataInterface] | null = null;
  internalLoading = false;
  errorMessage: string | null = null;

  // Lifecycle management
  private destroy$ = new Subject<void>();

  // Enums for template use
  readonly [EnumName] = [EnumName];

  constructor(
    private [serviceName]: [RequiredService],
    // Inject other required services
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
    this.setupSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Public methods for template
  onItemClick(item: [ComponentDataInterface]): void {
    this.selectedItem = item;
    this.itemSelected.emit(item);
  }

  onActionClick(action: string, item?: [ComponentDataInterface]): void {
    const actionEvent: [ActionEventInterface] = {
      action,
      item,
      timestamp: new Date()
    };
    this.actionTriggered.emit(actionEvent);
  }

  // Component-specific business logic
  [customMethod]([parameters]): void {
    // Implement component-specific functionality
  }

  // Private helper methods
  private initializeComponent(): void {
    // Component initialization logic
  }

  private setupSubscriptions(): void {
    // Setup reactive subscriptions
    this.[serviceName].data$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => this.handleDataUpdate(data),
        error: (error) => this.handleError(error)
      });
  }

  private handleDataUpdate(data: [ComponentDataInterface][]): void {
    this.data = data;
    this.dataChanged.emit(data);
  }

  private handleError(error: any): void {
    this.errorMessage = error.message || 'An error occurred';
    console.error('[ComponentName]Component error:', error);
  }
}
```

### 🎨 Template Structure

```html
<!-- [component-name].component.html -->
<div class="[component-name]-container" [class.loading]="loading || internalLoading">

  <!-- Component Header (if applicable) -->
  <div class="component-header" *ngIf="config?.showHeader">
    <h3 class="component-title">
      {{ config?.title || '[COMPONENT_NAME].TITLE' | translate }}
    </h3>

    <!-- Action buttons -->
    <div class="header-actions" *ngIf="config?.showActions">
      <app-custom-button
        [btnName]="'COMMON.ADD' | translate"
        [iconName]="ButtonIcon.Plus"
        class="btn-primary"
        [disabled]="disabled"
        (click)="onActionClick('add')">
      </app-custom-button>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="loading || internalLoading">
    <div class="spinner-border" role="status">
      <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
    </div>
  </div>

  <!-- Error State -->
  <div class="alert alert-danger" *ngIf="errorMessage && !loading">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {{ errorMessage }}
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!loading && !errorMessage && data.length === 0">
    <div class="empty-state-content">
      <i class="fas fa-inbox empty-state-icon"></i>
      <h4>{{ '[COMPONENT_NAME].EMPTY_STATE.TITLE' | translate }}</h4>
      <p>{{ '[COMPONENT_NAME].EMPTY_STATE.MESSAGE' | translate }}</p>
    </div>
  </div>

  <!-- Main Content -->
  <div class="component-content" *ngIf="!loading && !errorMessage && data.length > 0">

    <!-- List View Example -->
    <div class="item-list" *ngFor="let item of data; trackBy: trackByFn">
      <div
        class="item-card"
        [class.selected]="selectedItem?.id === item.id"
        (click)="onItemClick(item)">

        <!-- Item Content -->
        <div class="item-header">
          <h5 class="item-title">{{ item.name }}</h5>
          <span class="item-status" [class]="'status-' + item.status">
            {{ item.status | translate }}
          </span>
        </div>

        <div class="item-body">
          <p class="item-description">{{ item.description }}</p>
          <div class="item-meta">
            <small class="text-muted">
              {{ 'COMMON.CREATED_AT' | translate }}:
              {{ item.createdAt | date:'short' }}
            </small>
          </div>
        </div>

        <!-- Item Actions -->
        <div class="item-actions">
          <button
            class="btn btn-sm btn-outline-primary"
            (click)="onActionClick('edit', item); $event.stopPropagation()">
            <i class="fas fa-edit"></i>
            {{ 'COMMON.EDIT' | translate }}
          </button>

          <button
            class="btn btn-sm btn-outline-danger"
            (click)="onActionClick('delete', item); $event.stopPropagation()">
            <i class="fas fa-trash"></i>
            {{ 'COMMON.DELETE' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Component Footer (if applicable) -->
  <div class="component-footer" *ngIf="config?.showFooter">
    <div class="footer-info">
      <small class="text-muted">
        {{ 'COMMON.TOTAL_ITEMS' | translate }}: {{ data.length }}
      </small>
    </div>
  </div>
</div>
```

### 🎨 Component Styling

```scss
// [component-name].component.scss
.#{component-name}-container {
  padding: 1rem;
  background: var(--bs-body-bg);
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

  &.loading {
    opacity: 0.7;
    pointer-events: none;
  }

  // Component Header
  .component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--bs-border-color);

    .component-title {
      margin: 0;
      color: var(--bs-heading-color);
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  // Loading State
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  // Empty State
  .empty-state {
    text-align: center;
    padding: 3rem 1rem;

    .empty-state-content {
      max-width: 400px;
      margin: 0 auto;

      .empty-state-icon {
        font-size: 3rem;
        color: var(--bs-secondary);
        margin-bottom: 1rem;
      }

      h4 {
        color: var(--bs-heading-color);
        margin-bottom: 0.5rem;
      }

      p {
        color: var(--bs-secondary);
        margin: 0;
      }
    }
  }

  // Item List
  .item-list {
    .item-card {
      background: var(--bs-body-bg);
      border: 1px solid var(--bs-border-color);
      border-radius: 0.375rem;
      padding: 1rem;
      margin-bottom: 1rem;
      cursor: pointer;
      transition: all 0.2s ease-in-out;

      &:hover {
        border-color: var(--bs-primary);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
      }

      &.selected {
        border-color: var(--bs-primary);
        background: rgba(var(--bs-primary-rgb), 0.05);
      }

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;

        .item-title {
          margin: 0;
          font-size: 1.1rem;
          font-weight: 500;
        }

        .item-status {
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: uppercase;

          &.status-active {
            background: rgba(var(--bs-success-rgb), 0.1);
            color: var(--bs-success);
          }

          &.status-inactive {
            background: rgba(var(--bs-danger-rgb), 0.1);
            color: var(--bs-danger);
          }
        }
      }

      .item-body {
        margin-bottom: 1rem;

        .item-description {
          margin-bottom: 0.5rem;
          color: var(--bs-body-color);
        }

        .item-meta {
          font-size: 0.875rem;
        }
      }

      .item-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
      }
    }
  }

  // Component Footer
  .component-footer {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--bs-border-color);
    text-align: center;
  }
}

// RTL Support
[dir="rtl"] {
  .#{component-name}-container {
    .component-header {
      .header-actions {
        flex-direction: row-reverse;
      }
    }

    .item-list {
      .item-card {
        .item-actions {
          justify-content: flex-start;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .#{component-name}-container {
    padding: 0.75rem;

    .component-header {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;

      .header-actions {
        justify-content: center;
      }
    }

    .item-list {
      .item-card {
        .item-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.5rem;
        }

        .item-actions {
          justify-content: center;
          flex-wrap: wrap;
        }
      }
    }
  }
}
```

---

## 🔄 Component Lifecycle and Architecture

### 📊 Component Lifecycle Management

The following diagram illustrates the comprehensive Angular component lifecycle and state management patterns used in the Jadwa web application:

```mermaid
flowchart TD
    A[Component Creation] --> B[Constructor]
    B --> C[ngOnInit]
    C --> D[Service Injection]
    D --> E[Data Subscriptions]
    E --> F[ngAfterViewInit]
    F --> G[Template Rendering]

    H[Input Changes] --> I[ngOnChanges]
    I --> J[Update Component State]
    J --> K[Trigger Change Detection]

    G --> L[User Interactions]
    L --> M[Event Handling]
    M --> N[Output Events]
    N --> O[Parent Component]

    E --> P[Observable Data]
    P --> Q[State Updates]
    Q --> R[markForCheck]
    R --> S[UI Refresh]

    T[Component Destroy] --> U[ngOnDestroy]
    U --> V[Unsubscribe Observables]
    V --> W[Cleanup Resources]

    style A fill:#e3f2fd
    style G fill:#e8f5e8
    style Q fill:#f3e5f5
    style U fill:#ffcdd2
```

### 🔗 Component Interaction Patterns

The diagram below shows the various communication patterns between components, including parent-child communication, service-based communication, and event broadcasting:

```mermaid
flowchart TD
    A[Parent Component] --> B[Input Properties]
    B --> C[Child Component]
    C --> D[Output Events]
    D --> A

    E[Shared Service] --> F[Observable Data]
    F --> C
    F --> G[Sibling Component]

    C --> H[Service Method Call]
    H --> E

    I[State Service] --> J[BehaviorSubject]
    J --> K[Component A]
    J --> L[Component B]
    J --> M[Component C]

    K --> N[Update State]
    N --> I

    O[Event Bus Service] --> P[Global Events]
    P --> Q[All Components]

    style A fill:#e3f2fd
    style E fill:#e8f5e8
    style I fill:#f3e5f5
    style O fill:#fff3e0
```

---

## 🔄 Component Communication

### 📥 Input Properties

**Standard Input Properties**
```typescript
// Data inputs
@Input() data: [DataType][] = [];
@Input() selectedItems: [DataType][] = [];
@Input() config: [ConfigInterface] | null = null;

// State inputs
@Input() loading: boolean = false;
@Input() disabled: boolean = false;
@Input() readonly: boolean = false;

// Behavior inputs
@Input() allowMultiSelect: boolean = false;
@Input() showActions: boolean = true;
@Input() pageSize: number = 10;

// Styling inputs
@Input() variant: 'default' | 'compact' | 'detailed' = 'default';
@Input() theme: 'light' | 'dark' = 'light';
```

### 📤 Output Events

**Standard Output Events**
```typescript
// Selection events
@Output() itemSelected = new EventEmitter<[DataType]>();
@Output() itemDeselected = new EventEmitter<[DataType]>();
@Output() selectionChanged = new EventEmitter<[DataType][]>();

// Action events
@Output() actionTriggered = new EventEmitter<ComponentActionEvent>();
@Output() editRequested = new EventEmitter<[DataType]>();
@Output() deleteRequested = new EventEmitter<[DataType]>();

// Data events
@Output() dataChanged = new EventEmitter<[DataType][]>();
@Output() filterChanged = new EventEmitter<ComponentFilterEvent>();
@Output() sortChanged = new EventEmitter<ComponentSortEvent>();

// Lifecycle events
@Output() componentReady = new EventEmitter<void>();
@Output() componentError = new EventEmitter<Error>();
```

### 🔗 Service Integration

**Component-Service Communication Pattern**
```typescript
export class [ComponentName]Component implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  constructor(
    private [serviceName]: [ServiceName]Service,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.setupServiceSubscriptions();
  }

  private setupServiceSubscriptions(): void {
    // Subscribe to service data
    this.[serviceName].data$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.data = data;
          this.cdr.markForCheck(); // For OnPush change detection
        },
        error: (error) => this.handleError(error)
      });

    // Subscribe to loading state
    this.[serviceName].loading$
      .pipe(takeUntil(this.destroy$))
      .subscribe(loading => {
        this.loading = loading;
        this.cdr.markForCheck();
      });
  }

  // Trigger service actions
  onRefresh(): void {
    this.[serviceName].refreshData();
  }

  onFilter(filter: ComponentFilterEvent): void {
    this.[serviceName].applyFilter(filter);
    this.filterChanged.emit(filter);
  }
}
```

---

## 🌍 Internationalization

### 🗣️ Translation Integration

**Translation Keys Structure**
```json
{
  "[COMPONENT_NAME]": {
    "TITLE": "Component Title",
    "SUBTITLE": "Component Subtitle",
    "ACTIONS": {
      "ADD": "Add New",
      "EDIT": "Edit",
      "DELETE": "Delete",
      "VIEW": "View Details",
      "REFRESH": "Refresh"
    },
    "STATES": {
      "LOADING": "Loading...",
      "EMPTY": "No items found",
      "ERROR": "An error occurred"
    },
    "EMPTY_STATE": {
      "TITLE": "No Data Available",
      "MESSAGE": "There are no items to display at this time.",
      "ACTION": "Add New Item"
    },
    "FILTERS": {
      "SEARCH_PLACEHOLDER": "Search items...",
      "FILTER_BY": "Filter by",
      "SORT_BY": "Sort by",
      "CLEAR_FILTERS": "Clear Filters"
    },
    "MESSAGES": {
      "ITEM_SELECTED": "Item selected",
      "ITEMS_SELECTED": "{{count}} items selected",
      "ACTION_SUCCESS": "Action completed successfully",
      "ACTION_ERROR": "Action failed"
    }
  }
}
```

**Translation Usage in Component**
```typescript
export class [ComponentName]Component {
  constructor(private translateService: TranslateService) {}

  getTranslatedMessage(key: string, params?: any): string {
    return this.translateService.instant(`[COMPONENT_NAME].${key}`, params);
  }

  showSuccessMessage(itemCount: number): void {
    const message = itemCount === 1
      ? this.getTranslatedMessage('MESSAGES.ITEM_SELECTED')
      : this.getTranslatedMessage('MESSAGES.ITEMS_SELECTED', { count: itemCount });

    // Show message to user
  }
}
```

### 🔄 RTL Support

**RTL-Aware Template Structure**
```html
<div class="component-container" [attr.dir]="currentDirection">
  <!-- Content automatically adapts to RTL/LTR -->
  <div class="component-header">
    <h3 class="title">{{ title | translate }}</h3>
    <div class="actions">
      <!-- Actions will be right-aligned in RTL -->
      <button class="btn btn-primary">
        <i class="fas fa-plus" [class.me-2]="!isRTL" [class.ms-2]="isRTL"></i>
        {{ 'COMMON.ADD' | translate }}
      </button>
    </div>
  </div>
</div>
```

---

## 🎨 Styling and Theming

### 🌈 Theme Support

**CSS Custom Properties for Theming**
```scss
.component-container {
  // Use CSS custom properties for theme support
  background: var(--component-bg, var(--bs-body-bg));
  color: var(--component-text, var(--bs-body-color));
  border: 1px solid var(--component-border, var(--bs-border-color));

  // Theme variants
  &.theme-light {
    --component-bg: #ffffff;
    --component-text: #212529;
    --component-border: #dee2e6;
  }

  &.theme-dark {
    --component-bg: #212529;
    --component-text: #ffffff;
    --component-border: #495057;
  }

  // Component variants
  &.variant-compact {
    padding: 0.5rem;

    .item-card {
      padding: 0.75rem;
      margin-bottom: 0.5rem;
    }
  }

  &.variant-detailed {
    padding: 1.5rem;

    .item-card {
      padding: 1.5rem;
      margin-bottom: 1.5rem;
    }
  }
}
```

### 📱 Responsive Design Patterns

**Mobile-First Responsive Design**
```scss
// Mobile-first approach
.component-container {
  // Base mobile styles
  padding: 0.75rem;

  .component-header {
    flex-direction: column;
    gap: 1rem;
  }

  .item-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  // Tablet styles
  @media (min-width: 768px) {
    padding: 1rem;

    .component-header {
      flex-direction: row;
      justify-content: space-between;
    }

    .item-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  // Desktop styles
  @media (min-width: 1024px) {
    padding: 1.5rem;

    .item-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 1.5rem;
    }
  }

  // Large desktop styles
  @media (min-width: 1200px) {
    .item-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}
```

---

## ♿ Accessibility

### 🎯 WCAG Compliance

**Accessibility Implementation**
```typescript
export class [ComponentName]Component implements OnInit {
  // ARIA attributes
  @HostBinding('attr.role') role = 'region';
  @HostBinding('attr.aria-label') ariaLabel = 'Component content';
  @HostBinding('attr.aria-live') ariaLive = 'polite';

  // Keyboard navigation
  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'ArrowDown':
        this.navigateNext();
        event.preventDefault();
        break;
      case 'ArrowUp':
        this.navigatePrevious();
        event.preventDefault();
        break;
      case 'Enter':
      case ' ':
        this.selectCurrentItem();
        event.preventDefault();
        break;
      case 'Escape':
        this.clearSelection();
        event.preventDefault();
        break;
    }
  }

  // Focus management
  private focusedIndex = -1;

  navigateNext(): void {
    if (this.focusedIndex < this.data.length - 1) {
      this.focusedIndex++;
      this.focusItem(this.focusedIndex);
    }
  }

  navigatePrevious(): void {
    if (this.focusedIndex > 0) {
      this.focusedIndex--;
      this.focusItem(this.focusedIndex);
    }
  }

  private focusItem(index: number): void {
    const items = document.querySelectorAll('.item-card');
    if (items[index]) {
      (items[index] as HTMLElement).focus();
    }
  }
}
```

**Accessible Template Structure**
```html
<div
  class="component-container"
  role="region"
  [attr.aria-label]="'[COMPONENT_NAME].TITLE' | translate"
  [attr.aria-busy]="loading">

  <!-- Skip link for keyboard users -->
  <a class="skip-link" href="#main-content">
    {{ 'ACCESSIBILITY.SKIP_TO_CONTENT' | translate }}
  </a>

  <!-- Accessible heading structure -->
  <h2 class="component-title" id="component-title">
    {{ title | translate }}
  </h2>

  <!-- Screen reader announcements -->
  <div
    class="sr-only"
    aria-live="polite"
    aria-atomic="true"
    [attr.aria-label]="statusMessage">
    {{ statusMessage }}
  </div>

  <!-- Accessible list -->
  <ul
    class="item-list"
    role="list"
    [attr.aria-labelledby]="'component-title'"
    [attr.aria-describedby]="'list-description'">

    <li
      *ngFor="let item of data; trackBy: trackByFn; let i = index"
      class="item-card"
      role="listitem"
      [attr.tabindex]="0"
      [attr.aria-selected]="selectedItem?.id === item.id"
      [attr.aria-describedby]="'item-' + item.id + '-description'"
      (click)="onItemClick(item)"
      (keydown.enter)="onItemClick(item)"
      (keydown.space)="onItemClick(item)">

      <!-- Accessible item content -->
      <div class="item-content">
        <h3 class="item-title">{{ item.name }}</h3>
        <p
          class="item-description"
          [id]="'item-' + item.id + '-description'">
          {{ item.description }}
        </p>
      </div>

      <!-- Accessible actions -->
      <div class="item-actions" role="group" [attr.aria-label]="'Actions for ' + item.name">
        <button
          class="btn btn-sm btn-outline-primary"
          [attr.aria-label]="'Edit ' + item.name"
          (click)="onActionClick('edit', item); $event.stopPropagation()">
          <i class="fas fa-edit" aria-hidden="true"></i>
          <span class="sr-only">{{ 'COMMON.EDIT' | translate }}</span>
        </button>
      </div>
    </li>
  </ul>

  <!-- Accessible pagination -->
  <nav aria-label="Pagination" *ngIf="totalPages > 1">
    <!-- Pagination controls -->
  </nav>
</div>
```

---

## 🧪 Testing Strategy

### 🔬 Unit Testing

**Component Unit Tests**
```typescript
describe('[ComponentName]Component', () => {
  let component: [ComponentName]Component;
  let fixture: ComponentFixture<[ComponentName]Component>;
  let mockService: jasmine.SpyObj<[ServiceName]Service>;

  beforeEach(async () => {
    const serviceSpy = jasmine.createSpyObj('[ServiceName]Service', ['getData', 'updateData']);

    await TestBed.configureTestingModule({
      imports: [[ComponentName]Component, TranslateModule.forRoot()],
      providers: [
        { provide: [ServiceName]Service, useValue: serviceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent([ComponentName]Component);
    component = fixture.componentInstance;
    mockService = TestBed.inject([ServiceName]Service) as jasmine.SpyObj<[ServiceName]Service>;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.data).toEqual([]);
      expect(component.loading).toBeFalse();
      expect(component.selectedItem).toBeNull();
    });

    it('should setup subscriptions on init', () => {
      spyOn(component as any, 'setupSubscriptions');
      component.ngOnInit();
      expect((component as any).setupSubscriptions).toHaveBeenCalled();
    });
  });

  describe('Input Properties', () => {
    it('should accept data input', () => {
      const testData = [{ id: 1, name: 'Test Item' }];
      component.data = testData;
      fixture.detectChanges();
      expect(component.data).toEqual(testData);
    });

    it('should handle loading state', () => {
      component.loading = true;
      fixture.detectChanges();
      const loadingElement = fixture.debugElement.query(By.css('.loading-container'));
      expect(loadingElement).toBeTruthy();
    });
  });

  describe('User Interactions', () => {
    it('should emit itemSelected when item is clicked', () => {
      spyOn(component.itemSelected, 'emit');
      const testItem = { id: 1, name: 'Test Item' };

      component.onItemClick(testItem);

      expect(component.selectedItem).toEqual(testItem);
      expect(component.itemSelected.emit).toHaveBeenCalledWith(testItem);
    });

    it('should emit actionTriggered when action is clicked', () => {
      spyOn(component.actionTriggered, 'emit');
      const testItem = { id: 1, name: 'Test Item' };

      component.onActionClick('edit', testItem);

      expect(component.actionTriggered.emit).toHaveBeenCalledWith(
        jasmine.objectContaining({
          action: 'edit',
          item: testItem
        })
      );
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      fixture.detectChanges();
      const container = fixture.debugElement.query(By.css('.component-container'));
      expect(container.nativeElement.getAttribute('role')).toBe('region');
      expect(container.nativeElement.getAttribute('aria-label')).toBeTruthy();
    });

    it('should handle keyboard navigation', () => {
      const testData = [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' }
      ];
      component.data = testData;
      fixture.detectChanges();

      const event = new KeyboardEvent('keydown', { key: 'ArrowDown' });
      spyOn(component, 'navigateNext');

      component.onKeyDown(event);

      expect(component.navigateNext).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle service errors gracefully', () => {
      const error = new Error('Service error');
      spyOn(console, 'error');

      (component as any).handleError(error);

      expect(component.errorMessage).toBe('Service error');
      expect(console.error).toHaveBeenCalled();
    });
  });
});
```

### 🔄 Integration Testing

**Component Integration Tests**
```typescript
describe('[ComponentName] Integration', () => {
  let component: [ComponentName]Component;
  let fixture: ComponentFixture<[ComponentName]Component>;
  let service: [ServiceName]Service;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        [ComponentName]Component,
        HttpClientTestingModule,
        TranslateModule.forRoot()
      ],
      providers: [[ServiceName]Service]
    }).compileComponents();

    fixture = TestBed.createComponent([ComponentName]Component);
    component = fixture.componentInstance;
    service = TestBed.inject([ServiceName]Service);
  });

  it('should load data from service', fakeAsync(() => {
    const testData = [{ id: 1, name: 'Test Item' }];
    spyOn(service, 'getData').and.returnValue(of(testData));

    component.ngOnInit();
    tick();
    fixture.detectChanges();

    expect(component.data).toEqual(testData);
    expect(fixture.debugElement.queryAll(By.css('.item-card')).length).toBe(1);
  }));

  it('should handle service errors', fakeAsync(() => {
    spyOn(service, 'getData').and.returnValue(throwError('Service error'));

    component.ngOnInit();
    tick();
    fixture.detectChanges();

    expect(component.errorMessage).toBeTruthy();
    expect(fixture.debugElement.query(By.css('.alert-danger'))).toBeTruthy();
  }));
});
```

---

## 📋 Implementation Checklist

### 🏗️ Development Tasks
- [ ] Create component file structure
- [ ] Implement component class with proper TypeScript types
- [ ] Create responsive HTML template
- [ ] Implement SCSS styles with theme support
- [ ] Add internationalization support
- [ ] Implement accessibility features
- [ ] Add input/output properties
- [ ] Implement service integration
- [ ] Add error handling
- [ ] Implement loading states

### 🎨 UI/UX Tasks
- [ ] Design component variants (compact, detailed, etc.)
- [ ] Implement responsive breakpoints
- [ ] Add RTL support for Arabic language
- [ ] Create empty state design
- [ ] Implement loading animations
- [ ] Add hover and focus states
- [ ] Test color contrast for accessibility
- [ ] Verify touch targets for mobile

### 🧪 Testing Tasks
- [ ] Write unit tests for component logic
- [ ] Test input/output properties
- [ ] Test user interactions
- [ ] Test accessibility features
- [ ] Test keyboard navigation
- [ ] Test responsive design
- [ ] Test RTL layout
- [ ] Test error scenarios
- [ ] Test integration with services
- [ ] Perform manual testing

### 📚 Documentation Tasks
- [ ] Document component API (inputs/outputs)
- [ ] Create usage examples
- [ ] Document accessibility features
- [ ] Update component library documentation
- [ ] Create Storybook stories (if applicable)
- [ ] Document styling customization options

---

*Template Version: 1.0*
*Last Updated: 2025-06-24*
*Created by: Jadwa Development Team*
