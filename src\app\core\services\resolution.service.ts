import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SingleResolutionResponseBaseResponse } from '@core/api/api.generated';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ResolutionService {

baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

    getResolutionById(id: number): Observable<SingleResolutionResponseBaseResponse> {
      const url = `${this.baseUrl}/api/Resolutions/GetResolutionById/${id}`;
      return this.http.get<SingleResolutionResponseBaseResponse>(url);
    }

     GetResolutionStatusById(id: number): Observable<SingleResolutionResponseBaseResponse> {
      const url = `${this.baseUrl}/api/Resolutions/GetResolutionStatusById/${id}`;
      return this.http.get<SingleResolutionResponseBaseResponse>(url);

    }
}
