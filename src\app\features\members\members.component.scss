.members {
  padding: 20px;

  .alert {
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;

    &.alert-warning {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      color: #856404;
    }
  }

  .loading-container,
  .error-container,
  .empty-state {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .spinner-border {
      width: 3rem;
      height: 3rem;
    }

    h5 {
      margin-bottom: 12px;
      font-weight: 600;
    }

    p {
      margin-bottom: 20px;
      max-width: 400px;
      text-align: center;
    }

    .btn {
      padding: 10px 24px;
      border-radius: 6px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .members-list {
    .row {
      margin: 0 -8px;
    }

    .col-12,
    .col-md-6,
    .col-lg-4 {
      padding: 0 8px;
    }
  }

  .members-info {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-top: 24px;

    .row {
      align-items: center;
    }

    small {
      font-size: 14px;
      color: #6c757d;
      font-weight: 500;
    }
  }
}

// RTL Support
[dir="rtl"] .members {
  .alert {
    flex-direction: row-reverse;
  }

  .empty-state .btn,
  .error-container .btn {
    flex-direction: row-reverse;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .members {
    padding: 16px;

    .members-info {
      .row {
        flex-direction: column;
        gap: 8px;
        text-align: center;
      }

      .col-md-6 {
        &.text-md-end {
          text-align: center !important;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .members {
    .members-list {
      .col-12,
      .col-md-6,
      .col-lg-4 {
        padding: 0 4px;
      }
    }
  }
}



