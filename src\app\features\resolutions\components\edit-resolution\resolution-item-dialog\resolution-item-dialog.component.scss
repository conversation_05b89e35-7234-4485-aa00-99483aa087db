.resolution-item-dialog {
  width: 100%;
  max-width: 600px;
  background: white;
  border-radius: 12px;
  overflow: hidden;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;

    .dialog-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #00205a;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 18px;
      color: #666;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background: #e0e0e0;
        color: #333;
      }

      i {
        font-size: 16px;

        // Fallback for when FontAwesome doesn't load
        &.fas.fa-times:before {
          content: "×";
          font-family: Arial, sans-serif;
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;

    .spinner-border {
      width: 2rem;
      height: 2rem;
      margin-bottom: 15px;
    }

    .loading-text {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }

  .dialog-content {
    padding: 24px;

    .item-title-section {
      margin-bottom: 20px;

      .item-title-display {
       border-radius: 6px;
      font-weight: 400;
      color: #4F4F4F;
      font-size: 16px;
      }
    }

    .form-section {
      margin-bottom: 20px;
    }



    .dialog-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      padding-top: 20px;
      border-top: 1px solid #e0e0e0;

      .btn {
        padding: 10px 20px;
        font-weight: 500;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 100px;
        justify-content: center;
        transition: all 0.2s ease;

        // &.cancel-btn {
        //   background: #6c757d;
        //   border-color: #6c757d;
        //   color: white;

        //   &:hover {
        //     background: #5a6268;
        //     border-color: #545b62;
        //   }
        // }

        // &.submit-btn {
        //   background: #007bff;
        //   border-color: #007bff;
        //   color: white;

        //   &:hover {
        //     background: #0056b3;
        //     border-color: #004085;
        //   }

        //   &:disabled {
        //     opacity: 0.6;
        //     cursor: not-allowed;
        //   }
        // }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .resolution-item-dialog {
    max-width: 95vw;
    margin: 10px;

    .dialog-header {
      padding: 16px 20px;

      .dialog-title {
        font-size: 16px;
      }
    }

    .dialog-content {
      padding: 20px;

      .dialog-actions {
        flex-direction: column;

        .btn {
          width: 100%;
        }
      }
    }
  }
}
