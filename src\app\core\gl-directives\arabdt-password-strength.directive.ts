import { Directive, ElementRef, HostListener, Renderer2, Input, OnInit } from '@angular/core';

@Directive({
  selector: '[appArabdtPasswordStrength]',
  standalone: true
})
export class ArabdtPasswordStrengthDirective {

  private strengthBar: HTMLElement;

  @Input('appArabdtPasswordStrength') passwordField!: any;

  constructor(private el: ElementRef, private renderer: Renderer2) {
    this.strengthBar = this.renderer.createElement('div');
  }

  ngOnInit() {
    this.initializeStrengthBar();
  }

  @HostListener('input', ['$event.target.value'])
  onPasswordInput(password: string) {
    this.updateStrengthBar(password);
  }

  private initializeStrengthBar() {
    this.renderer.setStyle(this.strengthBar, 'height', '8px');
    this.renderer.setStyle(this.strengthBar, 'width', '100%');
    this.renderer.setStyle(this.strengthBar, 'backgroundColor', '#e0e0e0');
    this.renderer.setStyle(this.strengthBar, 'borderRadius', '4px');
    this.renderer.setStyle(this.strengthBar, 'marginTop', '5px');

    const strengthLevel = this.renderer.createElement('div');
    this.renderer.setStyle(strengthLevel, 'height', '100%');
    this.renderer.setStyle(strengthLevel, 'width', '0%');
    this.renderer.setStyle(strengthLevel, 'backgroundColor', 'red');
    this.renderer.setStyle(strengthLevel, 'borderRadius', '4px');
    
    this.renderer.appendChild(this.strengthBar, strengthLevel);
    this.renderer.insertBefore(this.el.nativeElement.parentNode, this.strengthBar, this.el.nativeElement.nextSibling);
  }

  private updateStrengthBar(password: string) {
    const strength = this.calculateStrength(password);
    const strengthLevel = this.strengthBar.firstChild as HTMLElement;
    this.renderer.setStyle(strengthLevel, 'width', `${strength}%`);

    if (strength < 40) {
      this.renderer.setStyle(strengthLevel, 'backgroundColor', 'red');
    } else if (strength < 70) {
      this.renderer.setStyle(strengthLevel, 'backgroundColor', 'orange');
    } else {
      this.renderer.setStyle(strengthLevel, 'backgroundColor', 'green');
    }
  }

  private calculateStrength(password: string): number {
    let strength = 0;

    // Add points for password length
    if (password.length >= 8) strength += 20;

    // Add points for the presence of lowercase, uppercase, numbers, and special characters
    if (/[a-z]/.test(password)) strength += 20;
    if (/[A-Z]/.test(password)) strength += 20;
    if (/[0-9]/.test(password)) strength += 20;
    if (/[^a-zA-Z0-9]/.test(password)) strength += 20;

    return strength;
  }
}
