// @import "../../../../../../assets/scss/variables";

.form-container {
 // background: #f8fafc;
  border-radius: 8px;
 // border: 0.5px solid #dce0e3;

  .header {
    color: #00205a;
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;

    span img {
      width: 20px;
      height: 20px;
    }
  }

  ::ng-deep {
    .form-group {
      margin-bottom: 1.5rem;

      &.col-12 {
        width: 100%;
      }

      &.col-md-6 {
        width: 48%;
        margin-inline-end: 2%;
        &:nth-child(2n) {
          margin-inline-end: 0;
        }
      }
    }

    // Custom button styles
    app-custom-button {
      .btn {
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.2s ease;
      }
    }

    // Form field styles
    .mat-mdc-form-field {
      width: 100%;
      
      .mat-mdc-form-field-subscript-wrapper {
        display: none;
      }
    }
  }
}

// RTL support
[dir="rtl"] .form-container {
  .header {
    flex-direction: row-reverse;
  }
}

// Responsive design
@media (max-width: 768px) {
  .form-container {
    padding: 1rem !important;
    
    .header {
      font-size: 14px;
      margin-bottom: 16px;
    }

    ::ng-deep {
      .form-group {
        margin-bottom: 1rem;

        &.col-md-6 {
          width: 100%;
          margin-inline-end: 0;
        }
      }
    }
  }
}
