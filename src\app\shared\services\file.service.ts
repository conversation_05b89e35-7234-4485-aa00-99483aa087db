// file-upload.service.ts
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { MinIOFileServiceProxy } from '@core/api/api.generated';
import { AttachmentModule } from '@shared/enum/AttachmentModule';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class FileUploadService {
  private apiUrl =environment.apiUrl+'/api/Users/<USER>/UploadFile';
private minioApiUrl =environment.apiUrl+'/api/MinIO/MinIOFile/Upload';

  constructor(
    private http: HttpClient,
    private minioFileService: MinIOFileServiceProxy
  ) {}

  uploadFile(file: File, moduleId: number) {
    const formData = new FormData();
    formData.append('File', file);

    const params = new HttpParams()
      .set('FileName', file.name)
      .set('ModuleId', moduleId.toString());

    return this.http.post(this.apiUrl, formData, { params });
  }

  /**
   * Upload a single file to MinIO
   * @param file - The file to upload
   * @param bucketName - The MinIO bucket name (e.g., 'user-photos', 'documents', etc.)
   * @param moduleId - The module ID for categorization
   * @param fileName - Optional custom file name (defaults to original file name)
   * @returns Observable<void>
   */
  uploadFileToMinIO(
    file: File,
    moduleId: AttachmentModule,
    fileName?: string
  ) {
 const formData = new FormData();
    formData.append('File', file);

    const params = new HttpParams()
      .set('FileName', file.name)
      .set('BucketName', moduleId)
      .set('ModuleId', moduleId);

    return this.http.post(this.minioApiUrl, formData, { params });

    // // Create FileParameter object as required by the MinIO API
    // const fileParameter: FileParameter = {
    //   data: file,
    //   fileName: fileName || file.name
    // };

    // // Use the original file name if no custom name provided
    // const finalFileName = fileName || file.name;

    // console.log('Uploading file to MinIO:', {
    //   fileName: finalFileName,
    //   bucketName,
    //   moduleId,
    //   fileSize: file.size,
    //   fileType: file.type
    // });

    // // Call the MinIO upload service
    // return this.minioFileService.upload(
    //   bucketName,
    //   finalFileName,
    //   moduleId,
    //   undefined, // body parameter
    //   fileParameter
    // );
  }
}
