<ng-container *ngIf="dataSource">
  <table
    mat-table
    [dataSource]="dataSource"
    class="mat-elevation-z8 demo-table"
    matSort
  >
    <!-- Loop through columns to define each type -->
    <ng-container
      *ngFor="let column of columns"
      [matColumnDef]="column.columnDef"
    >
      <!-- Checkbox Column -->
      <ng-container
        *ngIf="column.columnType === columnTypeEnum.Checkbox"
        [class]="column.class"
      >
        <th mat-header-cell *matHeaderCellDef>
          <mat-checkbox
            (change)="onToggleAllRows()"
            [checked]="selection.hasValue() && isAllSelected()"
            [indeterminate]="selection.hasValue() && !isAllSelected()"
            [aria-label]="checkboxLabel()"
          >
          </mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let row" [class]="column.class">
          <mat-checkbox
            (click)="$event.stopPropagation()"
            (change)="onToggleRow(row)"
            [checked]="selection.isSelected(row)"
            [aria-label]="checkboxLabel(row)"
          >
          </mat-checkbox>
        </td>
      </ng-container>
      <ng-container *ngIf="column.isSortingBy">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          {{ column.header | translate }}
        </th>
      </ng-container>
      <ng-container *ngIf="!column.isSortingBy">
        <th mat-header-cell *matHeaderCellDef>{{ column.header |translate }}</th>
      </ng-container>

      <!-- Text Column -->
      <ng-container *ngIf="column.columnType === columnTypeEnum.Text">
        <td mat-cell *matCellDef="let row" [class]="column.class">
          {{ column.cell(row) }}
        </td>
      </ng-container>

      <!-- TextLink Column -->

      <ng-container *ngIf="column.columnType === columnTypeEnum.TextLink">
        <td mat-cell *matCellDef="let row" [class]="column.class">
          <a (click)="onTextLinkClick(row, column.columnDef)">{{
            column.cell(row)
          }}</a>
        </td>
      </ng-container>

      <!-- Status Column -->

      <ng-container *ngIf="column.columnType === columnTypeEnum.Status">
        <td mat-cell *matCellDef="let row" [class]="column.class">
          <span class="status-badge" [ngClass]="column.cell(row)?.class">
            <span class="dot"></span>
            {{
            column.cell(row)?.label
          }}</span>
        </td>
      </ng-container>

      <!-- Image Column -->

      <ng-container *ngIf="column.columnType === columnTypeEnum.Image">
        <td mat-cell *matCellDef="let row" [class]="column.class">
          <img [src]="row.imgSrc" alt="{{ column.header }}" width="50" />
        </td>
      </ng-container>

      <!-- Switch Column -->

      <ng-container *ngIf="column.columnType === columnTypeEnum.Switch">
        <td mat-cell *matCellDef="let row" [class]="column.class">
          <mat-slide-toggle
            [checked]="column.cell(row)"
            (change)="onToggle(row, $event.checked)"
          ></mat-slide-toggle>
        </td>
      </ng-container>

      <!-- Actions Column -->

      <ng-container *ngIf="column.columnType === columnTypeEnum.Actions">
        <td mat-cell *matCellDef="let row" [class]="column.class">
          <ng-container *ngIf="column.displayMode === ActionDisplayMode.Dropdown">
            <!-- Dropdown Button -->
            <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Example icon-button with a menu">
              <mat-icon>more_vert</mat-icon>
            </button>

            <!-- Dropdown Menu -->
            <mat-menu #menu="matMenu">
              <div class="d-flex action-menu-item" *ngFor="let button of column.cell(row)?.buttons">
                <button
                  class="btn action-menu-button"
                  (click)="handleAction(button.action, row)"
                >
                  <img
                    [src]="button.iconSrc"
                    [alt]="button.label | translate"
                    width="16"
                    height="16"
                    class="action-icon"
                    *ngIf="button.iconSrc"
                  />
                  <span class="action-label">{{ button.label | translate }}</span>
                </button>
              </div>
            </mat-menu>
          </ng-container>

          <ng-container *ngIf="column.displayMode === ActionDisplayMode.Flex || !column.displayMode">
            <!-- Flex Buttons -->
            <div class="flex-buttons">
              <button
                *ngFor="let button of column.cell(row)?.buttons"
                (click)="handleAction(button.action, row)"
                [title]="button.label | translate"
                class="action-button"
              >
                <img
                  [src]="button.iconSrc"
                  [alt]="button.label | translate"
            
                  *ngIf="button.iconSrc"
                />
                <span *ngIf="!button.iconSrc">{{ button.label | translate }}</span>
              </button>
            </div>
          </ng-container>
        </td>

      </ng-container>

      <ng-container *ngIf="column.columnType === columnTypeEnum.Custom">
        <th mat-header-cell *matHeaderCellDef>{{ column.header }}</th>
        <td mat-cell *matCellDef="let row">
          <ng-container
            *ngTemplateOutlet="customColumnTemplate; context: { element: row }"
          ></ng-container>
        </td>
      </ng-container>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>

  <mat-paginator
    [length]="totalItems"
    [pageSize]="pageSize"
    [pageSizeOptions]="[5, 10, 20]"
    aria-label="Pagination"
    (page)="onPageChange($event)"
  ></mat-paginator>


</ng-container>

<ng-container  *ngIf="!dataSource">
  <div 
class="d-flex  flex-column gap-4 justify-content-center mt-5 align-items-center">
<img src="assets/images/nodata.png" width="350">
    <p  class="text-center mt-3 header fs-20">{{'INVESTMENT_FUNDS.NO_DATA' | translate}}</p>
</div>
</ng-container>