import { Component } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'app-voting-card',
  standalone: true,
  imports: [CommonModule,TranslateModule],
  templateUrl: './voting-card.component.html',
  styleUrl: './voting-card.component.scss'
})
export class VotingCardComponent {
  userName: string = '<PERSON>';
  userImage: string = "assets/images/5a88f6c30078d932a34b61c983a4185389144193.jpg";
  remainingVotes: number = 3;
}
