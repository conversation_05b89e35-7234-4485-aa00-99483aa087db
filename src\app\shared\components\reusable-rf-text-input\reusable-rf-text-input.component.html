<div class="form-group">
  <!-- Label above input -->
  <label *ngIf="labelPosition === labelPositionEnum.Above" [for]="id">
    {{ label||'' | translate }} <span class="text-danger" *ngIf="isRequired">*</span>
  </label>
  <div class="d-flex align-items-center">
    <!-- Label before input -->
    <label *ngIf="labelPosition === labelPositionEnum.Before" [for]="id">
      {{ label||'' | translate}} <span class="text-danger" *ngIf="isRequired">*</span>
    </label>
    <!-- Input field -->
    <div class="input-wrapper w-100">
      <input [type]="type" [min]="min" [max]="max" [step]="step" [id]="id" [placeholder]="placeholder ||'' | translate"
        [readonly]="readonly" [(ngModel)]="value" [ngModelOptions]="{ standalone: true }" class="form-control"
        [ngClass]="getFormControlClasses()" (blur)="onBlur($event)" (input)="onInputChange($event)"
        (focus)="onFocus($event)" (keydown)="onKeyDown($event)" />
    </div>
    <!-- Label after input -->
    <label *ngIf="labelPosition === labelPositionEnum.After" [for]="id">
      {{ label||'' | translate }} <span class="text-danger" *ngIf="isRequired">*</span>
    </label>
  </div>
</div>