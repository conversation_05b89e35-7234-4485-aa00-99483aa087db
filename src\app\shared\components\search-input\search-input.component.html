<div class="search-input-container" [style.width]="width">
  <div class="search-input-wrapper">
    <input
      type="text"
      class="search-input"
      [placeholder]="placeholder | translate"
      [(ngModel)]="value"
      (input)="onSearchInput($event)"
      [disabled]="disabled"
    />
    <div class="search-icons">
      <button 
        *ngIf="value" 
        class="clear-button" 
        type="button" 
        (click)="onClear()"
        [disabled]="disabled">
        <i class="fas fa-times"></i>
      </button>
      <i class="fas fa-search search-icon"></i>
    </div>
  </div>
</div>
