import { LanguageService } from './core/gl-services/language-services/language.service';
import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ReusableArabdtSpinnerComponent } from '@shared/components/reusable-arabdt-spinner/reusable-arabdt-spinner.component';
import { ErrorModalService } from '@core/services/error-modal.service';
import { CommonModule } from '@angular/common';
import { AlertComponent } from "./shared/components/alert/alert.component";
import { SpinnerService } from '@core/gl-services/spinner-services/spinner.service';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, ReusableArabdtSpinnerComponent, CommonModule, AlertComponent,TranslateModule],
  templateUrl: './app.component.html',

  styleUrl: './app.component.scss'
})

export class AppComponent {
  alert: { message: string; type: any } | null = null;

  constructor(private languageService: LanguageService,private errorModalService: ErrorModalService,public loaderService: SpinnerService) {
    languageService.initLang();
    this.errorModalService.alert$.subscribe((alert:any) => this.alert = alert);

  }

  // ngAfterViewInit(): void {
  //   const elementsWithTitle = document.querySelectorAll('[title]');
  //   elementsWithTitle.forEach(el => el.removeAttribute('title'));
  // }

  ngAfterViewInit(): void {
    setTimeout(() => {
      document.querySelectorAll('[title]').forEach(el => el.removeAttribute('title'));
    });
  }

  currentTheme: 'dark' | 'light' = 'light';

  toggleTheme() {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
  }





}

