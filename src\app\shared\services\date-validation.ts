import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';

export function dateRangeValidator(
  fromFieldName: string,
  toFieldName: string
): ValidatorFn {
  return (group: AbstractControl): ValidationErrors | null => {
    const from = group.get(fromFieldName)?.value as NgbDateStruct;
    const to = group.get(toFieldName)?.value as NgbDateStruct;

    if (!from || !to) return null;

    const fromDate = new Date(from.year, from.month - 1, from.day);
    const toDate = new Date(to.year, to.month - 1, to.day);

    return fromDate <= toDate ? null : { dateRangeInvalid: true };
  };
}
