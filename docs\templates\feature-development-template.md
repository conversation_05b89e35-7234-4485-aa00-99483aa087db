# 🎯 Feature Development Template
## [FEATURE_NAME] - New Feature Implementation

> **[Brief description of the feature and its business value within the Jadwa Investment Web Application]**

---

## 🌟 Feature Overview

### 📊 Feature Summary
| Field | Value |
|-------|-------|
| **Feature ID** | FEAT-[YYYY]-[###] |
| **Feature Name** | [Descriptive feature name] |
| **Business Domain** | [Investment Funds/Strategies/Authentication/Dashboard/Voting] |
| **Priority** | [High/Medium/Low] |
| **Complexity** | [Simple/Medium/Complex] |
| **Estimated Effort** | [Story Points/Hours] |
| **Target Sprint** | [Sprint number/date] |
| **Epic** | [Related epic if applicable] |

### 🎯 Business Value
- **Problem Statement**: [What problem does this feature solve?]
- **Business Impact**: [How does this feature benefit the business?]
- **User Benefit**: [How does this feature benefit end users?]
- **Success Metrics**: [How will success be measured?]

---

## 👥 User Stories and Scenarios

### 🎭 Primary User Stories

**US-01: [Primary User Story]**
```
As a [user role]
I want to [desired functionality]
So that [business benefit]
```

**Acceptance Criteria:**
- [ ] **Given** [precondition], **When** [action], **Then** [expected result]
- [ ] **Given** [precondition], **When** [action], **Then** [expected result]
- [ ] **Given** [precondition], **When** [action], **Then** [expected result]

**US-02: [Secondary User Story]**
```
As a [user role]
I want to [desired functionality]
So that [business benefit]
```

**Acceptance Criteria:**
- [ ] **Given** [precondition], **When** [action], **Then** [expected result]
- [ ] **Given** [precondition], **When** [action], **Then** [expected result]

### 🔄 User Workflows

**Primary User Workflow: Complete Feature Interaction**

The following diagram illustrates the comprehensive user workflow for feature interaction, including authentication, permission checks, data operations, and error handling:

```mermaid
graph TD
    A[User Access Request] --> B{Authentication Status?}
    B -->|Not Authenticated| C[Login Process]
    B -->|Authenticated| D[Permission Check]

    C --> E[JWT Token Validation]
    E --> F{Valid Credentials?}
    F -->|No| G[Authentication Error]
    F -->|Yes| H[Generate JWT Token]
    H --> D

    D --> I{Has Permission?}
    I -->|No| J[Access Denied]
    I -->|Yes| K[Feature Landing Page]

    K --> L[Load Initial Data]
    L --> M{Data Available?}
    M -->|No| N[Empty State Display]
    M -->|Yes| O[Display Data List]

    N --> P[Create New Action]
    O --> Q[User Action Selection]

    Q --> R{Action Type?}
    R -->|View Details| S[Load Detail View]
    R -->|Edit Item| T[Load Edit Form]
    R -->|Delete Item| U[Confirmation Dialog]
    R -->|Create New| P
    R -->|Filter/Search| V[Apply Filters]

    S --> W[Display Details]
    T --> X[Form Validation]
    U --> Y{Confirm Delete?}
    P --> Z[Create Form]
    V --> AA[Filtered Results]

    X --> BB{Valid Data?}
    BB -->|No| CC[Show Validation Errors]
    BB -->|Yes| DD[Submit to API]

    Y -->|No| O
    Y -->|Yes| EE[Delete API Call]

    Z --> FF[Form Validation]
    FF --> GG{Valid Data?}
    GG -->|No| HH[Show Validation Errors]
    GG -->|Yes| II[Create API Call]

    DD --> JJ{API Success?}
    EE --> KK{API Success?}
    II --> LL{API Success?}

    JJ -->|No| MM[Show Error Message]
    JJ -->|Yes| NN[Show Success Message]
    KK -->|No| MM
    KK -->|Yes| NN
    LL -->|No| MM
    LL -->|Yes| NN

    NN --> OO[Refresh Data]
    MM --> PP[Return to Previous State]
    OO --> O

    CC --> T
    HH --> Z
    AA --> O
    W --> Q

    style A fill:#e3f2fd
    style K fill:#e8f5e8
    style NN fill:#c8e6c9
    style MM fill:#ffcdd2
    style G fill:#ffcdd2
    style J fill:#ffcdd2
```

**Workflow Key Steps:**
1. **Authentication**: User credentials validation and JWT token generation
2. **Authorization**: Permission-based access control for feature operations
3. **Data Loading**: Initial data retrieval with loading states and error handling
4. **User Interactions**: CRUD operations with form validation and confirmation dialogs
5. **API Communication**: Service layer integration with comprehensive error handling
6. **State Management**: Real-time UI updates and data synchronization

---

## 🏗️ Technical Architecture

### 🎯 Component Architecture Overview

The following diagram shows the comprehensive architecture for feature development, including component layers, service integration, and external API communication:

```mermaid
graph TB
    subgraph "Feature Module Structure"
        FM[Feature Module]
        FR[Feature Routes]
        FG[Route Guards]
    end

    subgraph "Component Layer"
        LC[List Component]
        DC[Details Component]
        FC[Form Component]
        MC[Modal Component]
    end

    subgraph "Service Layer"
        FS[Feature Service]
        SS[State Service]
        VS[Validation Service]
        TS[Transform Service]
    end

    subgraph "API Layer"
        SP[Service Proxy]
        INT[HTTP Interceptors]
        ERR[Error Handler]
    end

    subgraph "Shared Components"
        TC[Table Component]
        BC[Button Component]
        PC[Page Header Component]
        BRC[Breadcrumb Component]
    end

    subgraph "Core Services"
        AS[Auth Service]
        TKS[Token Service]
        TRS[Translation Service]
        EMS[Error Modal Service]
    end

    subgraph "External APIs"
        SWAGGER[Swagger API]
        JWT[JWT Auth Server]
    end

    FM --> FR
    FR --> FG
    FG --> LC
    FG --> DC
    FG --> FC

    LC --> FS
    DC --> FS
    FC --> FS
    MC --> FS

    FS --> SS
    FS --> VS
    FS --> TS
    FS --> SP

    SP --> INT
    INT --> ERR
    SP --> SWAGGER

    LC --> TC
    LC --> BC
    LC --> PC
    LC --> BRC

    DC --> BC
    FC --> BC
    MC --> BC

    FS --> AS
    FS --> TKS
    LC --> TRS
    DC --> TRS
    FC --> TRS

    ERR --> EMS
    AS --> JWT
    TKS --> JWT

    style FM fill:#e3f2fd
    style LC fill:#e8f5e8
    style FS fill:#f3e5f5
    style SP fill:#fff3e0
    style SWAGGER fill:#ffcdd2
```

### 📁 Feature Structure
Following the established Jadwa application patterns:

```
src/app/features/[feature-name]/
├── components/
│   ├── [feature-list]/
│   │   ├── [feature-list].component.ts
│   │   ├── [feature-list].component.html
│   │   ├── [feature-list].component.scss
│   │   └── [feature-list].component.spec.ts
│   ├── [feature-details]/
│   │   ├── [feature-details].component.ts
│   │   ├── [feature-details].component.html
│   │   ├── [feature-details].component.scss
│   │   └── [feature-details].component.spec.ts
│   └── [feature-form]/
│       ├── [feature-form].component.ts
│       ├── [feature-form].component.html
│       ├── [feature-form].component.scss
│       └── [feature-form].component.spec.ts
├── services/
│   ├── [feature].service.ts
│   └── [feature].service.spec.ts
├── models/
│   └── [feature].models.ts
├── [feature].routes.ts
└── [feature].module.ts (if needed)
```

### 🎨 Component Architecture

**Main Feature Component**
```typescript
@Component({
  selector: 'app-[feature-name]',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    TableComponent,
    PageHeaderComponent,
    CustomButtonComponent,
    BreadcrumbComponent
  ],
  templateUrl: './[feature-name].component.html',
  styleUrls: ['./[feature-name].component.scss']
})
export class [FeatureName]Component implements OnInit, OnDestroy {
  // Component properties
  private destroy$ = new Subject<void>();
  loading = false;
  data: [FeatureDataType][] = [];
  
  // Pagination and filtering
  currentPage = 1;
  pageSize = 10;
  totalCount = 0;
  searchTerm = '';
  
  constructor(
    private [featureName]Service: [FeatureName]Service,
    private router: Router,
    private dialog: MatDialog,
    private translateService: TranslateService,
    private tokenService: TokenService
  ) {}

  ngOnInit(): void {
    this.loadData();
    this.setupPermissions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Feature-specific methods
  loadData(): void {
    this.loading = true;
    this.[featureName]Service.getList(
      this.currentPage,
      this.pageSize,
      this.searchTerm
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (response) => {
        this.data = response.data;
        this.totalCount = response.totalCount;
        this.loading = false;
      },
      error: (error) => {
        this.loading = false;
        // Error handling is done by interceptor
      }
    });
  }
}
```

### ⚙️ Service Implementation

**Feature Service**
```typescript
@Injectable({ providedIn: 'root' })
export class [FeatureName]Service {
  constructor(
    private http: HttpClient,
    private [featureName]Proxy: [FeatureName]ServiceProxy
  ) {}

  // CRUD Operations following established patterns
  getList(
    pageNumber: number,
    pageSize: number,
    search: string,
    orderBy: string = 'Id desc'
  ): Observable<PaginatedResponse<[FeatureDataType]>> {
    return this.[featureName]Proxy.list(
      pageNumber,
      pageSize,
      search,
      orderBy
    ).pipe(
      catchError(this.handleError),
      shareReplay(1)
    );
  }

  getById(id: number): Observable<[FeatureDataType]> {
    return this.[featureName]Proxy.getById(id).pipe(
      map(response => response.data),
      catchError(this.handleError)
    );
  }

  create(data: Create[FeatureName]Command): Observable<BaseResponse> {
    return this.[featureName]Proxy.create(data).pipe(
      catchError(this.handleError)
    );
  }

  update(data: Update[FeatureName]Command): Observable<BaseResponse> {
    return this.[featureName]Proxy.update(data).pipe(
      catchError(this.handleError)
    );
  }

  delete(id: number): Observable<BaseResponse> {
    return this.[featureName]Proxy.delete(id).pipe(
      catchError(this.handleError)
    );
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    // Error handling is managed by global interceptor
    return throwError(() => error);
  }
}
```

---

## 🔌 API Integration

### 🌐 Required API Endpoints
Based on Swagger specification: `http://************:44301/swagger/v2/swagger.json`

**Endpoint 1: List [Feature] Items**
- **Method**: `GET`
- **Path**: `/api/[Controller]/List`
- **Parameters**:
  - `PageNumber`: number (query)
  - `PageSize`: number (query)
  - `Search`: string (query)
  - `OrderBy`: string (query)
- **Response**: `[FeatureDataType]PaginatedResult`
- **Authentication**: Required

**Endpoint 2: Get [Feature] by ID**
- **Method**: `GET`
- **Path**: `/api/[Controller]/GetById`
- **Parameters**:
  - `id`: number (query)
- **Response**: `[FeatureDataType]BaseResponse`
- **Authentication**: Required

**Endpoint 3: Create [Feature]**
- **Method**: `POST`
- **Path**: `/api/[Controller]/Create`
- **Request Body**: `Create[FeatureName]Command`
- **Response**: `StringBaseResponse`
- **Authentication**: Required
- **Permissions**: [Required permissions]

**Endpoint 4: Update [Feature]**
- **Method**: `PUT`
- **Path**: `/api/[Controller]/Update`
- **Request Body**: `Update[FeatureName]Command`
- **Response**: `StringBaseResponse`
- **Authentication**: Required
- **Permissions**: [Required permissions]

### 📋 Data Models

**Request Models**
```typescript
export interface Create[FeatureName]Command {
  // Define properties based on API specification
  name: string;
  description?: string;
  // Add other required fields
}

export interface Update[FeatureName]Command extends Create[FeatureName]Command {
  id: number;
  updatedAt?: string;
}
```

**Response Models**
```typescript
export interface [FeatureDataType] {
  id: number;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt?: string;
  // Add other fields from API
}

export interface [FeatureName]DetailsDto extends [FeatureDataType] {
  // Additional fields for detailed view
  additionalInfo?: string;
  relatedData?: any[];
}
```

---

## 🎨 UI/UX Implementation

### 📱 Responsive Design
Following Bootstrap 5 and Angular Material patterns:

**Layout Structure**
```html
<!-- Feature List Component Template -->
<div class="container-fluid">
  <!-- Page Header -->
  <app-page-header 
    [title]="'[FEATURE_NAME].TITLE' | translate"
    [breadcrumbs]="breadcrumbs">
  </app-page-header>

  <!-- Action Bar -->
  <div class="row mb-3">
    <div class="col-md-6">
      <app-custom-button
        [btnName]="'[FEATURE_NAME].CREATE_NEW' | translate"
        [iconName]="createButtonIcon.Plus"
        class="btn-primary"
        (click)="onCreate()">
      </app-custom-button>
    </div>
    <div class="col-md-6">
      <!-- Search and filters -->
      <div class="d-flex justify-content-end">
        <input 
          type="text" 
          class="form-control me-2" 
          [placeholder]="'COMMON.SEARCH' | translate"
          [(ngModel)]="searchTerm"
          (keyup.enter)="onSearch()">
        <button class="btn btn-outline-primary" (click)="onFilter()">
          {{ 'COMMON.FILTER' | translate }}
        </button>
      </div>
    </div>
  </div>

  <!-- Data Table -->
  <app-table
    [columns]="tableColumns"
    [data]="data"
    [totalCount]="totalCount"
    [pageSize]="pageSize"
    [loading]="loading"
    (actionClicked)="onTableAction($event)"
    (pageChanged)="onPageChange($event)">
  </app-table>
</div>
```

### 🌍 Internationalization
**Translation Keys Structure**
```json
{
  "[FEATURE_NAME]": {
    "TITLE": "Feature Title",
    "CREATE_NEW": "Create New [Feature]",
    "EDIT": "Edit [Feature]",
    "DELETE": "Delete [Feature]",
    "DETAILS": "[Feature] Details",
    "LIST": "[Feature] List",
    "FORM": {
      "NAME": "Name",
      "DESCRIPTION": "Description",
      "SAVE": "Save",
      "CANCEL": "Cancel"
    },
    "MESSAGES": {
      "CREATE_SUCCESS": "[Feature] created successfully",
      "UPDATE_SUCCESS": "[Feature] updated successfully",
      "DELETE_SUCCESS": "[Feature] deleted successfully",
      "DELETE_CONFIRM": "Are you sure you want to delete this [feature]?"
    }
  }
}
```

---

## 🔐 Security and Permissions

### 🛡️ Authentication Requirements
- **JWT Token**: Required for all feature operations
- **Token Validation**: Automatic validation via interceptors
- **Session Management**: Handled by AuthService

### 👥 Authorization Matrix
| Action | Fund Manager | Board Secretary | Legal Council | Admin |
|--------|-------------|----------------|---------------|-------|
| **View List** | ✅ | ✅ | ✅ | ✅ |
| **View Details** | ✅ | ✅ | ✅ | ✅ |
| **Create** | [✅/❌] | [✅/❌] | [✅/❌] | ✅ |
| **Update** | [✅/❌] | [✅/❌] | [✅/❌] | ✅ |
| **Delete** | [❌] | [❌] | [❌] | ✅ |

### 🔒 Permission Implementation
```typescript
// Permission checking in component
ngOnInit(): void {
  this.setupPermissions();
  this.loadData();
}

private setupPermissions(): void {
  this.canCreate = this.tokenService.hasPermission('[FEATURE_NAME]_CREATE');
  this.canEdit = this.tokenService.hasPermission('[FEATURE_NAME]_EDIT');
  this.canDelete = this.tokenService.hasPermission('[FEATURE_NAME]_DELETE');
}
```

---

## ✅ Testing Strategy

### 🧪 Unit Testing
**Component Testing**
```typescript
describe('[FeatureName]Component', () => {
  let component: [FeatureName]Component;
  let fixture: ComponentFixture<[FeatureName]Component>;
  let mockService: jasmine.SpyObj<[FeatureName]Service>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('[FeatureName]Service', ['getList', 'create', 'update', 'delete']);

    await TestBed.configureTestingModule({
      imports: [[FeatureName]Component],
      providers: [
        { provide: [FeatureName]Service, useValue: spy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent([FeatureName]Component);
    component = fixture.componentInstance;
    mockService = TestBed.inject([FeatureName]Service) as jasmine.SpyObj<[FeatureName]Service>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load data on init', () => {
    const mockData = [/* mock data */];
    mockService.getList.and.returnValue(of({ data: mockData, totalCount: 1 }));

    component.ngOnInit();

    expect(mockService.getList).toHaveBeenCalled();
    expect(component.data).toEqual(mockData);
  });
});
```

### 🔄 Integration Testing
- **API Integration**: Test service-to-API communication
- **Component Integration**: Test component-to-service interaction
- **Route Testing**: Test navigation and route guards

### 👥 User Acceptance Testing
**Test Scenarios:**
1. **Create [Feature]**: User can successfully create a new [feature]
2. **View [Feature] List**: User can view paginated list of [features]
3. **Edit [Feature]**: User can update existing [feature] details
4. **Delete [Feature]**: User can delete [feature] with confirmation
5. **Search [Features]**: User can search and filter [features]
6. **Permission Validation**: Users see only authorized actions

---

## 📋 Implementation Checklist

### 🏗️ Development Tasks
- [ ] Create feature directory structure
- [ ] Implement main component with list view
- [ ] Implement details component
- [ ] Implement form component (create/edit)
- [ ] Create feature service with CRUD operations
- [ ] Define data models and interfaces
- [ ] Implement routing configuration
- [ ] Add translation keys
- [ ] Implement permission checks
- [ ] Add breadcrumb navigation

### 🎨 UI/UX Tasks
- [ ] Design responsive layouts
- [ ] Implement table with pagination
- [ ] Create form validation
- [ ] Add loading states
- [ ] Implement error handling UI
- [ ] Add confirmation dialogs
- [ ] Test RTL layout support
- [ ] Verify accessibility compliance

### 🧪 Testing Tasks
- [ ] Write unit tests for components
- [ ] Write unit tests for services
- [ ] Create integration tests
- [ ] Perform manual testing
- [ ] Conduct UAT sessions
- [ ] Performance testing
- [ ] Security testing

### 📚 Documentation Tasks
- [ ] Update API documentation
- [ ] Create user guide sections
- [ ] Update architecture documentation
- [ ] Create deployment notes
- [ ] Update release notes

---

*Template Version: 1.0*
*Last Updated: 2025-06-24*
*Created by: Jadwa Development Team*
