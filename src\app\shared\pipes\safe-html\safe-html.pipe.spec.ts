import { SafeHtmlPipe } from './safe-html.pipe';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { TestBed } from '@angular/core/testing';

describe('SafeHtmlPipe', () => {
  let pipe: SafeHtmlPipe;
  let sanitizer: jasmine.SpyObj<DomSanitizer>;

  beforeEach(() => {
    const sanitizerSpy = jasmine.createSpyObj('DomSanitizer', ['bypassSecurityTrustHtml']);

    TestBed.configureTestingModule({
      providers: [
        SafeHtmlPipe,
        { provide: DomSanitizer, useValue: sanitizerSpy }
      ]
    });

    pipe = TestBed.inject(SafeHtmlPipe);
    sanitizer = TestBed.inject(DomSanitizer) as jasmine.SpyObj<DomSanitizer>;
  });

  it('should create the pipe', () => {
    expect(pipe).toBeTruthy();
  });

  it('should sanitize the html using DomSanitizer', () => {
    const unsafeHtml = '<div>Unsafe HTML</div>';
    const safeHtml = {} as SafeHtml; // Mock safe HTML return value
    sanitizer.bypassSecurityTrustHtml.and.returnValue(safeHtml);

    const result = pipe.transform(unsafeHtml);

    expect(sanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith(unsafeHtml);
    expect(result).toBe(safeHtml);
  });
});
