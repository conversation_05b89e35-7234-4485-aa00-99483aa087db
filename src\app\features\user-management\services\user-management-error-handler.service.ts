import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';
import { ApiException, ProblemDetails } from '@core/api/api.generated';

export interface UserManagementErrorContext {
  operation: 'create' | 'update' | 'delete' | 'activate' | 'deactivate' | 'resetPassword' | 'resendMessage' | 'load' | 'loadRoles';
  userId?: number;
  userName?: string;
  additionalInfo?: any;
}

@Injectable({
  providedIn: 'root'
})
export class UserManagementErrorHandlerService {

  constructor(private translateService: TranslateService) {}

  /**
   * Handle user activation/deactivation errors with backend message extraction
   */
  handleActivationError(error: HttpErrorResponse, context: UserManagementErrorContext): void {
    const backendMessage = this.extractBackendErrorMessage(error);
    const contextualMessage = this.getContextualErrorMessage(error.status, context);
    
    // Use backend message if available, otherwise use contextual message
    const finalMessage = backendMessage || contextualMessage;
    const errorTitle = this.getContextualErrorTitle(context);

    this.showUserManagementError(errorTitle, finalMessage, error.status, context);
  }

  /**
   * Extract specific error message from backend API response
   */
  private extractBackendErrorMessage(error: HttpErrorResponse): string | null {
    if (!error.error) {
      return null;
    }

    // Handle ApiException from generated client
    if (ApiException.isApiException(error.error)) {
      return this.extractFromApiException(error.error as ApiException);
    }

    // Handle direct error response formats
    return this.extractFromErrorResponse(error.error, error.status);
  }

  /**
   * Extract error message from ApiException (generated client errors)
   */
  private extractFromApiException(apiException: ApiException): string | null {
    // Check if the result contains a ProblemDetails object
    if (apiException.result && this.isProblemDetails(apiException.result)) {
      const problemDetails = apiException.result as ProblemDetails;
      return problemDetails.detail || problemDetails.title || null;
    }
    
    // Check if the result contains a BaseResponse object
    if (apiException.result && this.isBaseResponse(apiException.result)) {
      const baseResponse = apiException.result;
      return baseResponse.message || null;
    }

    // Return ApiException message if available
    return apiException.message || null;
  }

  /**
   * Extract error message from direct error response
   */
  private extractFromErrorResponse(errorResponse: any, statusCode: number): string | null {
    // Format 1: ProblemDetails object
    if (this.isProblemDetails(errorResponse)) {
      const problemDetails = errorResponse as ProblemDetails;
      return problemDetails.detail || problemDetails.title || null;
    }

    // Format 2: BaseResponse object
    if (this.isBaseResponse(errorResponse)) {
      return errorResponse.message || null;
    }

    // Format 3: Simple message
    if (errorResponse.message) {
      return errorResponse.message;
    }

    // Format 4: Detail field
    if (errorResponse.detail) {
      return errorResponse.detail;
    }

    // Format 5: String error
    if (typeof errorResponse === 'string') {
      return errorResponse;
    }

    return null;
  }

  /**
   * Check if object is a ProblemDetails
   */
  private isProblemDetails(obj: any): boolean {
    return obj && typeof obj === 'object' && 
           ('type' in obj || 'title' in obj || 'status' in obj || 'detail' in obj || 'instance' in obj);
  }

  /**
   * Check if object is a BaseResponse
   */
  private isBaseResponse(obj: any): boolean {
    return obj && typeof obj === 'object' && 
           ('statusCode' in obj && 'successed' in obj && 'message' in obj);
  }

  /**
   * Get contextual error message based on operation and status code
   */
  private getContextualErrorMessage(statusCode: number, context: UserManagementErrorContext): string {
    // Check if we have a specific translation for this operation + status code
    const specificKey = `USER_MANAGEMENT.ERRORS.${context.operation.toUpperCase()}_${statusCode}`;
    const specificMessage = this.translateService.instant(specificKey);
    
    if (specificMessage !== specificKey) {
      return this.interpolateMessage(specificMessage, context);
    }

    // Check for general operation error
    const operationKey = `USER_MANAGEMENT.ERRORS.${context.operation.toUpperCase()}_FAILED`;
    const operationMessage = this.translateService.instant(operationKey);
    
    if (operationMessage !== operationKey) {
      return this.interpolateMessage(operationMessage, context);
    }

    // Fallback to base message with operation context
    const fallbackKey = this.getFallbackErrorKey(context.operation);
    const fallbackMessage = this.translateService.instant(fallbackKey);
    
    return this.interpolateMessage(fallbackMessage, context);
  }

  /**
   * Get contextual error title based on operation
   */
  private getContextualErrorTitle(context: UserManagementErrorContext): string {
    const titleKey = `USER_MANAGEMENT.ERROR_TITLES.${context.operation.toUpperCase()}`;
    const title = this.translateService.instant(titleKey);
    
    if (title !== titleKey) {
      return title;
    }

    // Fallback to generic error title
    return this.translateService.instant('COMMON.ERROR');
  }

  /**
   * Get fallback error key based on operation
   */
  private getFallbackErrorKey(operation: string): string {
    const operationKeys: { [key: string]: string } = {
      'activate': 'USER_MANAGEMENT.ERRORS.STATUS_UPDATE_FAILED',
      'deactivate': 'USER_MANAGEMENT.ERRORS.STATUS_UPDATE_FAILED',
      'create': 'USER_MANAGEMENT.ERRORS.CREATE_FAILED',
      'update': 'USER_MANAGEMENT.ERRORS.UPDATE_FAILED',
      'delete': 'USER_MANAGEMENT.ERRORS.DELETE_FAILED',
      'resetPassword': 'USER_MANAGEMENT.ERRORS.RESET_PASSWORD_FAILED',
      'resendMessage': 'USER_MANAGEMENT.ERRORS.RESEND_MESSAGE_FAILED',
      'load': 'USER_MANAGEMENT.ERRORS.LOAD_FAILED',
      'loadRoles': 'USER_MANAGEMENT.ERRORS.ROLES_LOAD_ERROR'
    };

    return operationKeys[operation] || 'USER_MANAGEMENT.ERRORS.OPERATION_FAILED';
  }

  /**
   * Interpolate message with context variables
   */
  private interpolateMessage(message: string, context: UserManagementErrorContext): string {
    if (context.userName) {
      message = message.replace('{{name}}', context.userName);
      message = message.replace('{{userName}}', context.userName);
    }
    
    if (context.userId) {
      message = message.replace('{{userId}}', context.userId.toString());
    }

    return message;
  }

  /**
   * Show user management specific error with SweetAlert
   */
  private showUserManagementError(title: string, message: string, statusCode: number, context: UserManagementErrorContext): void {
    const isRTL = this.translateService.currentLang === 'ar';
    
    // Determine icon based on status code and operation
    let icon: 'error' | 'warning' | 'info' = 'error';
    
    if (statusCode === 409) {
      icon = 'warning'; // Conflict - user already exists, role conflicts, etc.
    } else if (statusCode === 404) {
      icon = 'info'; // User not found
    }

    Swal.fire({
      title,
      text: message,
      icon,
      confirmButtonText: this.translateService.instant('COMMON.OK'),
      confirmButtonColor: '#d33',
      customClass: {
        popup: isRTL ? 'rtl-popup' : 'ltr-popup',
        title: isRTL ? 'rtl-title' : 'ltr-title',
        htmlContainer: isRTL ? 'rtl-content' : 'ltr-content'
      },
      // Show as toast for less critical errors
      toast: statusCode >= 500,
      position: statusCode >= 500 ? 'top-end' : 'center',
      timer: statusCode >= 500 ? 4000 : undefined,
      showConfirmButton: statusCode < 500,
      timerProgressBar: statusCode >= 500
    });
  }

  /**
   * Handle successful operations
   */
  showSuccess(operation: string, userName?: string): void {
    const messageKey = `USER_MANAGEMENT.SUCCESS.${operation.toUpperCase()}_SUCCESS`;
    let message = this.translateService.instant(messageKey);
    
    if (userName) {
      message = message.replace('{{name}}', userName).replace('{{userName}}', userName);
    }

    const isRTL = this.translateService.currentLang === 'ar';

    Swal.fire({
      title: this.translateService.instant('COMMON.SUCCESS'),
      text: message,
      icon: 'success',
      timer: 2000,
      showConfirmButton: false,
      customClass: {
        popup: isRTL ? 'rtl-popup' : 'ltr-popup',
        title: isRTL ? 'rtl-title' : 'ltr-title',
        htmlContainer: isRTL ? 'rtl-content' : 'ltr-content'
      }
    });
  }
}
