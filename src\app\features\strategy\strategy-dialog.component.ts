import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ViewChild } from '@angular/core';
import { NgModel } from '@angular/forms';
import Swal from 'sweetalert2';
import { ValidationMessagesComponent } from "../../shared/components/validation/validation-messages.component";


@Component({
  selector: 'app-strategy-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    TranslateModule,
    ValidationMessagesComponent
],
  templateUrl: './strategy-dialog.component.html',
  styleUrls: ['./strategy-dialog.component.scss'],
})
export class StrategyDialogComponent {
  strategyName: string = '' ;
  arabicName: string = '';
  initialArabicName: string = '';
initialStrategyName: string = '';

  @ViewChild('arabicNameRef') arabicNameRef!: NgModel;
  @ViewChild('strategyNameRef') strategyNameRef!: NgModel;
  constructor(
    public dialogRef: MatDialogRef<StrategyDialogComponent>,
    private translateService: TranslateService,@Inject(MAT_DIALOG_DATA) public data: any
  ) {

  }

  ngOnInit(): void {
    if (this.data.isEdit) {
      this.strategyName = this.data.strategyName;
      this.arabicName = this.data.arabicName;

      this.initialStrategyName = this.data.strategyName;
      this.initialArabicName = this.data.arabicName;
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }



  onSubmit(): void {
    const isArabicNameValid = this.arabicNameRef?.valid;
    const isStrategyNameValid = this.strategyNameRef?.valid;

    const arabicInput = this.arabicName?.trim().toLowerCase();
    const englishInput = this.strategyName?.trim().toLowerCase();

    const isArabicDuplicate = this.data.strategysNames.filteredData.some((strategy:any) => {
      const sameArabic = strategy.nameAr.trim().toLowerCase() === arabicInput;
      const isSameId = this.data?.isEdit && strategy.id === this.data.id;
      return sameArabic && !isSameId;
    });

    const isEnglishDuplicate = this.data.strategysNames.filteredData.some((strategy:any) => {
      const sameEnglish = strategy.nameEn.trim().toLowerCase() === englishInput;
      const isSameId = this.data?.isEdit && strategy.id === this.data.id;
      return sameEnglish && !isSameId;
    });

    if (isArabicDuplicate || isEnglishDuplicate) {
      if (isArabicDuplicate) {
        this.arabicNameRef.control.setErrors({ duplicate: true });
        this.arabicNameRef.control.markAsTouched();
      }
      if (isEnglishDuplicate) {
        this.strategyNameRef.control.setErrors({ duplicate: true });
        this.strategyNameRef.control.markAsTouched();
      }
      return;
    }

    if (isArabicNameValid && isStrategyNameValid) {
      this.dialogRef.close({
        strategyName: this.strategyName,
        arabicName: this.arabicName,
      });
    } else {
      this.arabicNameRef.control.markAsTouched();
      this.strategyNameRef.control.markAsTouched();
    }
  }
  isChanged(): boolean {
    return this.strategyName?.trim() !== this.initialStrategyName?.trim() ||
           this.arabicName?.trim() !== this.initialArabicName?.trim();
  }


}
