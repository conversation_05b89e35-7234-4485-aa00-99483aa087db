<div class="document-upload-dialog">
  <h2 mat-dialog-title>{{ 'DOCUMENTS.UPLOAD_DOCUMENT' | translate }}</h2>

  <mat-dialog-content class="upload-content">
    <!-- Form Builder Component -->
    <div class="form-container">
      <app-form-builder
        [formGroup]="formGroup"
        [formControls]="formControls"
        [isFormSubmitted]="isValidationFire"
        (formSubmit)="onSubmit($event)"
        (fileUploaded)="onFileUpload($event)">
      </app-form-builder>
    </div>

    <!-- Upload Progress -->
    <div *ngIf="isLoading" class="upload-progress">
      <mat-spinner diameter="24"></mat-spinner>
      <span>{{ 'DOCUMENTS.UPLOADING' | translate }}</span>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end" class="dialog-actions">
    <app-custom-button
      [btnName]="'COMMON.CANCEL' | translate"
      (click)="onCancel()"
      [buttonType]="buttonEnum.Secondary">
    </app-custom-button>

    <app-custom-button
      [btnName]="'DOCUMENTS.UPLOAD' | translate"
      (click)="onSubmitClick()"
      [buttonType]="buttonEnum.Primary"
      [iconName]="iconEnum.plus">
    </app-custom-button>
  </mat-dialog-actions>
</div>
