<div class="document-upload-dialog">
  <h2 mat-dialog-title>{{ 'DOCUMENTS.UPLOAD_DOCUMENT' | translate }}</h2>
  
  <mat-dialog-content class="upload-content">
    <!-- Document Category Selection -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>{{ 'DOCUMENTS.CATEGORY' | translate }}</mat-label>
      <mat-select [(value)]="selectedCategory" required>
        <mat-option *ngFor="let category of documentCategories" [value]="category.id">
          {{ (category.name || 'Unknown Category') | translate }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <!-- Document Title (Optional) -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>{{ 'DOCUMENTS.DOCUMENT_TITLE' | translate }}</mat-label>
      <input matInput [(ngModel)]="documentTitle" 
             [placeholder]="'DOCUMENTS.DOCUMENT_TITLE_PLACEHOLDER' | translate">
    </mat-form-field>

    <!-- File Upload Component -->
    <div class="file-upload-section">
      <label class="upload-label">{{ 'DOCUMENTS.SELECT_FILES' | translate }}</label>
      <app-file-upload
        [multiple]="true"
        [allowedTypes]="['pdf','doc','docx','xls','xlsx','jpg','jpeg','png']"
        (fileUploaded)="onFileSelected($event)">
      </app-file-upload>
    </div>

    <!-- Upload Progress -->
    <div *ngIf="isUploading" class="upload-progress">
      <mat-spinner diameter="24"></mat-spinner>
      <span>{{ 'DOCUMENTS.UPLOADING' | translate }}</span>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end" class="dialog-actions">
    <app-custom-button
      [btnName]="'COMMON.CANCEL' | translate"
      (click)="onCancel()"
      [buttonType]="buttonEnum.Secondary">
    </app-custom-button>

    <app-custom-button
      [btnName]="'DOCUMENTS.UPLOAD' | translate"
      (click)="onUpload()"
      [buttonType]="buttonEnum.Primary"
      [iconName]="iconEnum.plus"
      [disabled]="!isFormValid() || isUploading">
    </app-custom-button>
  </mat-dialog-actions>
</div>
