<div class="dialog-container">
  <h2 class=" header title">
    {{'INVESTMENT_FUNDS.MEMBERS.ADD_MEMBER_TO_BOX' | translate}}
  </h2>
  <hr>
  <div class="form-container">
    <app-form-builder
      (dropdownChanged)="dropdownChanged($event)" [formGroup]="formGroup"
      [formControls]="formControls" [isFormSubmitted]="isValidationFire"
      (formSubmit)="onSubmit($event)">
    </app-form-builder>
    </div>
    <hr>
    <div class="dialog-actions">

      <button class="btn cancel-btn w-50" (click)="onClose()" type="button">
        <img src="assets/icons/cancel-icon.png" class="mx-2" alt="verify">

        {{'INVESTMENT_FUNDS.MEMBERS.CANCEL' | translate}}

      </button>
      <button type="button" class="btn primary-btn w-50"
        (click)="onSubmitClick()" [disabled]="isLoading">
        <img src="assets/icons/verify-icon.png" class="mx-2" alt="verify">
        <span *ngIf="!isLoading">{{'INVESTMENT_FUNDS.MEMBERS.ADD' | translate}}</span>
        <span *ngIf="isLoading">{{'COMMON.LOADING' | translate}}</span>
      </button>
    </div>
  </div>
