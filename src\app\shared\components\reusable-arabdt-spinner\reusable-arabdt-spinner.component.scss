.spinner-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
  
	.spinner {
	  border: 4px solid rgba(0, 0, 0, 0.1);
	  border-left-color: #09f;
	  border-radius: 50%;
	  width: 50px;
	  height: 50px;
	  animation: spin 1s linear infinite;
	}
  
	@keyframes spin {
	  0% {
		transform: rotate(0deg);
	  }
	  100% {
		transform: rotate(360deg);
	  }
	}
  }
  