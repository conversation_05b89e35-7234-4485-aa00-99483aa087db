import { Component, EventEmitter, Input, Output, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Shared components
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';

// Interfaces and enums
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// API and services
import {
  UserManagementServiceProxy,
  ChangePasswordCommand,
  IJwtAuthResponseBaseResponse,
  AuthenticationServiceProxy,
  SignInCommand
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { ToastrService } from 'ngx-toastr';
import { ReactiveFormsModule } from '@angular/forms';
import { UserManagementService } from '@shared/services/users/user-management.service';
import { TokenService } from 'src/app/features/auth/services/token.service';
import { AuthService } from '../../../auth/services/auth-service/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-change-password',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    CustomButtonComponent,
    FormBuilderComponent,ReactiveFormsModule
  ],
  templateUrl: './change-password.component.html',
  styleUrl: './change-password.component.scss'
})
export class ChangePasswordComponent implements OnInit {
  @Input() userId?: any; // Optional - if not provided, uses current user
  @Output() passwordChanged = new EventEmitter<void>();
  @Output() cancelled = new EventEmitter<void>();

  constructor(private userManagementService:UserManagementService,private formBuilder :FormBuilder,private router: Router,
     private translateService:TranslateService, private authService:AuthService, private apiClient: AuthenticationServiceProxy,
    private errorModalService:ErrorModalService, private tokenService:TokenService){}


  // Form properties
  changePasswordForm!: FormGroup;
  isFormSubmitted = false;
  isLoading = false;
  buttonEnum = ButtonTypeEnum;
    IconEnum = IconEnum;

  // Enums for template
  ButtonTypeEnum = ButtonTypeEnum;

  // Form controls configuration
  formControls: IControlOption[] = [
    {
      formControlName: 'currentPassword',
      type: InputType.Password,
      id: 'currentPassword',
      name: 'currentPassword',
      label: 'USER_PROFILE.CURRENT_PASSWORD',
      placeholder: 'USER_PROFILE.ENTER_CURRENT_PASSWORD',
      isRequired: true,
      class: 'col-md-6 margin-left-inhert padding-left-22 w-51',
      minLength: 1,
      maxLength: 50
    },
    {
      formControlName: 'newPassword',
      type: InputType.Password,
      id: 'newPassword',
      name: 'newPassword',
      label: 'USER_PROFILE.NEW_PASSWORD',
      placeholder: 'USER_PROFILE.ENTER_NEW_PASSWORD',
      isRequired: true,
      class: 'col-md-6 w-51',
      minLength: 8,
      maxLength: 50,
      showStrengthIndicator: true
    },
    {
      formControlName: 'confirmPassword',
      type: InputType.Password,
      id: 'confirmPassword',
      name: 'confirmPassword',
      label: 'USER_PROFILE.CONFIRM_NEW_PASSWORD',
      placeholder: 'USER_PROFILE.CONFIRM_NEW_PASSWORD',
      isRequired: true,
      class: 'col-md-6 w-51',
      minLength: 8,
      maxLength: 50
    }
  ];

  ngOnInit(): void {
    this.initForm();
  }

  private initForm(): void {
    const formGroup: any = {};

    this.formControls.forEach((control) => {
      if (control.formControlName === '') return;

      const validators = [];
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      if (control.minLength) {
        validators.push(Validators.minLength(control.minLength));
      }
      if (control.maxLength) {
        validators.push(Validators.maxLength(control.maxLength));
      }

      formGroup[control.formControlName] = [null, validators];
    });

    this.changePasswordForm = this.formBuilder.group(formGroup);
  }

  cancel(){
   this.router.navigate(['/admin/user-management/my-profile']);
  }

   onSubmit(): void {
    debugger
    this.isFormSubmitted = true;

    if (this.changePasswordForm.invalid) {
      this.changePasswordForm.markAllAsTouched();
      return;
    }

    const newPassword = this.changePasswordForm.get('newPassword')?.value;
    const confirmPassword = this.changePasswordForm.get('confirmPassword')?.value;

    if (newPassword !== confirmPassword) {
      this.changePasswordForm.get('confirmPassword')?.setErrors({ passwordMismatch: true });
      return;
    } else {
      this.changePasswordForm.get('confirmPassword')?.setErrors(null);
    }

     this.userId = localStorage.getItem('userId');
    const obj = {
      userId: Number(this.userId),
      ...this.changePasswordForm.value
    };

 this.userManagementService.changePasswordForUser(obj).subscribe({
  next: () => {
    this.errorModalService.showSuccess(
      this.translateService.instant('LOGIN_PAGE.CHANGE_PASSWORD_SUCCESSFUL')
    );


  this.userManagementService.getUserNameById(this.userId).subscribe({
  next: (response) => {
    if (response) {
      const username = response.data.userName;
      const newPassword = this.changePasswordForm.get('newPassword')?.value;

      const signInCommand = new SignInCommand({
        userName: username,
        password: newPassword,
      });

      this.tokenService.logout();

      this.apiClient.signIn(signInCommand).subscribe({
        next: (res: IJwtAuthResponseBaseResponse) => {
          this.authService.login({
            accessToken: res.data.accessToken!,
            refreshToken: res.data.refreshToken!,
          });
          this.tokenService.setToken(res.data.accessToken!);

          this.router.navigate(['/admin/user-management/my-profile']);
        },
        error: (err) => {
          console.error('Error during sign in', err);
        }
      });
    }
  },
  error: (err) => {
    console.error('Error getting user by ID', err);
  }
});

  }
})


  }

  // onSubmit(): void {
  //   this.isFormSubmitted = true;

  //   if (this.changePasswordForm.invalid) {
  //     this.changePasswordForm.markAllAsTouched();
  //     return;
  //   }

  //   const formValue = this.changePasswordForm.value;

  //   // Validate password confirmation
  //   if (formValue.newPassword !== formValue.confirmPassword) {
  //     this.changePasswordForm.get('confirmPassword')?.setErrors({ passwordMismatch: true });
  //     return;
  //   } else {
  //     this.changePasswordForm.get('confirmPassword')?.setErrors(null);
  //   }

  //   // Get user ID (from input or current user)
  //   const targetUserId = this.userId || this.getCurrentUserId();



  //   // Prepare command
  //   const command = new ChangePasswordCommand({
  //     userId: targetUserId,
  //     currentPassword: formValue.currentPassword,
  //     newPassword: formValue.newPassword,
  //     confirmPassword: formValue.confirmPassword
  //   });

  //   this.isLoading = true;

  //   // Call API
  //   this.userManagementProxy.changePasswordForUser(command).subscribe({
  //     next: (response) => {
  //       this.isLoading = false;

  //       this.toastr.success(
  //         this.translateService.instant('USER_PROFILE.PASSWORD_CHANGED_SUCCESSFULLY') || 'Password changed successfully',
  //         this.translateService.instant('COMMON.SUCCESS') || 'Success'
  //       );

  //       // Reset form
  //       this.changePasswordForm.reset();
  //       this.isFormSubmitted = false;

  //       // Emit success event
  //       this.passwordChanged.emit();
  //     },
  //     error: (error) => {
  //       this.isLoading = false;
  //       console.error('Error changing password:', error);

  //       // Handle specific error cases
  //       let errorMessage = this.translateService.instant('USER_PROFILE.PASSWORD_CHANGE_FAILED') || 'Failed to change password';

  //       if (error.status === 401) {
  //         errorMessage = this.translateService.instant('USER_PROFILE.CURRENT_PASSWORD_INCORRECT') || 'Current password is incorrect';
  //       } else if (error.status === 400) {
  //         errorMessage = this.translateService.instant('USER_PROFILE.PASSWORD_COMPLEXITY_ERROR') || 'Password does not meet complexity requirements';
  //       }

  //       this.toastr.error(errorMessage, this.translateService.instant('COMMON.ERROR') || 'Error');
  //     }
  //   });
  // }

  onCancel(): void {
    // Reset form
    this.changePasswordForm.reset();
    this.isFormSubmitted = false;

    // Emit cancel event
    this.cancelled.emit();
  }

  private getCurrentUserId(): number | null {
    const userId = localStorage.getItem('userId');
    return userId ? parseInt(userId, 10) : null;
  }
}
