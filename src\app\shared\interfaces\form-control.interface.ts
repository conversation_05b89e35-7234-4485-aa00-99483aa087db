export type FormControlType = 'text' | 'number' | 'select' | 'date' | 'checkbox' | 'radio';

export interface SelectOption {
  value: any;
  label: string;
}

export interface FormControlConfig {
  name: string;
  label: string;
  type: FormControlType;
  value?: any;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: SelectOption[]; // For select and radio controls
  validations?: {
    type: string;
    value?: any;
    message: string;
  }[];
  cssClass?: string;
} 