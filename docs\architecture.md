# 🏗️ Jadwa Investment Web Application
## Architecture Documentation

> **A comprehensive guide to the system architecture, design patterns, and implementation details of the Jadwa Investment Web Application**

---

## 📋 Table of Contents

| Section | Description |
|---------|-------------|
| [🏛️ High-Level Architecture](#️-high-level-architecture) | System overview and architectural principles |
| [🛠️ Technology Stack](#️-technology-stack) | Technologies, frameworks, and tools used |
| [🔄 System Overview](#-system-overview) | Application flow and module relationships |
| [🔧 Low-Level Architecture](#-low-level-architecture) | Detailed component structure and organization |
| [⚙️ Service Architecture](#️-service-architecture) | API services and business logic layer |
| [🔐 Security Architecture](#-security-architecture) | Authentication, authorization, and security measures |
| [🚀 Deployment Architecture](#-deployment-architecture) | Environment configuration and deployment strategy |

---

## 🏛️ High-Level Architecture

### 📖 System Overview

The **Jadwa Investment Web Application** is a modern Angular-based single-page application (SPA) designed for comprehensive investment fund management. The application follows a **modular architecture** with clear separation of concerns between presentation, business logic, and data access layers.

#### 🎯 Key Architectural Goals
- **Scalability**: Support for growing user base and feature set
- **Maintainability**: Clean code structure for easy updates and bug fixes
- **Security**: Robust authentication and data protection
- **Performance**: Optimized loading and responsive user experience
- **Internationalization**: Multi-language support (Arabic/English)

<div align="center">
<svg viewBox="0 0 1024 768" style="max-width: 100%; height: auto;" xmlns="http://www.w3.org/2000/svg">
  <style>
    .node rect, .node circle, .node ellipse, .node polygon, .node path { fill: #ECECFF; stroke: #9370DB; stroke-width: 1px; }
    .node text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 14px; }
    .edgePath path { stroke: #333; stroke-width: 1.5px; }
    .cluster rect { fill: #ffffde; stroke: #aaaa33; stroke-width: 1px; }
    .cluster text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 14px; font-weight: bold; }
  </style>

  <!-- Client Layer Cluster -->
  <rect x="50" y="50" width="200" height="150" rx="5" ry="5" class="cluster"/>
  <text x="150" y="40" text-anchor="middle" class="cluster">Client Layer</text>

  <!-- Angular SPA -->
  <rect x="70" y="70" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="150" y="90" text-anchor="middle" class="node">Angular SPA</text>

  <!-- PWA Features -->
  <rect x="70" y="110" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="150" y="130" text-anchor="middle" class="node">PWA Features</text>

  <!-- Firebase Messaging -->
  <rect x="70" y="150" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="150" y="170" text-anchor="middle" class="node">Firebase Messaging</text>

  <!-- API Layer Cluster -->
  <rect x="350" y="50" width="250" height="250" rx="5" ry="5" class="cluster"/>
  <text x="475" y="40" text-anchor="middle" class="cluster">API Layer</text>

  <!-- .NET Core API -->
  <rect x="370" y="70" width="210" height="30" rx="5" ry="5" class="node"/>
  <text x="475" y="90" text-anchor="middle" class="node">.NET Core API</text>

  <!-- Authentication Service -->
  <rect x="370" y="110" width="210" height="30" rx="5" ry="5" class="node"/>
  <text x="475" y="130" text-anchor="middle" class="node">Authentication Service</text>

  <!-- Fund Management Service -->
  <rect x="370" y="150" width="210" height="30" rx="5" ry="5" class="node"/>
  <text x="475" y="170" text-anchor="middle" class="node">Fund Management Service</text>

  <!-- Strategy Service -->
  <rect x="370" y="190" width="210" height="30" rx="5" ry="5" class="node"/>
  <text x="475" y="210" text-anchor="middle" class="node">Strategy Service</text>

  <!-- File Management Service -->
  <rect x="370" y="230" width="210" height="30" rx="5" ry="5" class="node"/>
  <text x="475" y="250" text-anchor="middle" class="node">File Management Service</text>

  <!-- External Services Cluster -->
  <rect x="700" y="50" width="200" height="150" rx="5" ry="5" class="cluster"/>
  <text x="800" y="40" text-anchor="middle" class="cluster">External Services</text>

  <!-- Firebase Cloud Messaging -->
  <rect x="720" y="70" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="800" y="90" text-anchor="middle" class="node">Firebase Cloud Messaging</text>

  <!-- File Storage -->
  <rect x="720" y="130" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="800" y="150" text-anchor="middle" class="node">File Storage</text>

  <!-- Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>

  <!-- Angular SPA to .NET Core API -->
  <path d="M 230 85 L 370 85" class="edgePath" marker-end="url(#arrowhead)"/>

  <!-- Angular SPA to Firebase Cloud Messaging -->
  <path d="M 230 85 Q 465 85 720 85" class="edgePath" marker-end="url(#arrowhead)"/>

  <!-- .NET Core API to services -->
  <path d="M 475 100 L 475 110" class="edgePath" marker-end="url(#arrowhead)"/>
  <path d="M 475 100 L 475 150" class="edgePath" marker-end="url(#arrowhead)"/>
  <path d="M 475 100 L 475 190" class="edgePath" marker-end="url(#arrowhead)"/>
  <path d="M 475 100 L 475 230" class="edgePath" marker-end="url(#arrowhead)"/>

  <!-- Fund Management Service to File Storage -->
  <path d="M 580 170 L 720 150" class="edgePath" marker-end="url(#arrowhead)"/>

  <!-- Firebase Messaging to Firebase Cloud Messaging -->
  <path d="M 230 170 Q 465 170 720 85" class="edgePath" marker-end="url(#arrowhead)"/>
</svg>
</div>

#### 🏗️ Key Architectural Principles

| Principle | Description | Implementation |
|-----------|-------------|----------------|
| **🧩 Modular Design** | Feature-based module organization | Standalone components with lazy loading |
| **🔄 Separation of Concerns** | Clear boundaries between layers | Distinct core, features, and shared modules |
| **⚡ Reactive Programming** | RxJS for asynchronous operations | Observables and reactive patterns throughout |
| **🎨 Component-Based** | Reusable UI components | Shared component library with consistent design |
| **🔧 Service-Oriented** | Business logic encapsulated in services | Injectable services with dependency injection |
| **🛡️ Security-First** | JWT-based authentication | HTTP interceptors and route guards |

---

## 🛠️ Technology Stack

### 🎨 Frontend Technologies

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| **Framework** | Angular | 18+ | Core application framework with standalone components |
| **Language** | TypeScript | 5.4+ | Type-safe development with modern ES features |
| **Styling** | SCSS + Bootstrap + Angular Material | 5.x | Responsive design and component styling |
| **State Management** | RxJS Observables | Latest | Reactive state management and async operations |
| **Internationalization** | ngx-translate | Latest | Multi-language support (Arabic/English) |
| **Build Tool** | Angular CLI + Webpack | Latest | Development and production builds |
| **Testing** | Jasmine + Karma | Latest | Unit testing and test automation |

### 🔗 Backend Integration

| Component | Technology | Purpose |
|-----------|------------|---------|
| **API Communication** | HTTP Client + Interceptors | RESTful API integration with automatic token handling |
| **Code Generation** | NSwag | Type-safe TypeScript client generation from OpenAPI |
| **Authentication** | JWT Bearer Tokens | Secure user authentication and authorization |
| **Real-time** | Firebase Cloud Messaging | Push notifications and real-time updates |

### 🔧 Development Tools

| Tool | Purpose | Configuration |
|------|---------|---------------|
| **📦 Package Manager** | npm | Dependency management and script execution |
| **🐳 Containerization** | Docker | Multi-stage builds for different environments |
| **🌐 Web Server** | Nginx | Production deployment with optimized serving |
| **🌍 Environment Management** | Angular Environments | Local, test, and production configurations |

---

## 🔄 System Overview

<div align="center">
<svg viewBox="0 0 1200 600" style="max-width: 100%; height: auto;" xmlns="http://www.w3.org/2000/svg">
  <style>
    .node rect { fill: #ECECFF; stroke: #9370DB; stroke-width: 1px; }
    .node text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 12px; }
    .edgePath path { stroke: #333; stroke-width: 1.5px; }
    .cluster rect { fill: #ffffde; stroke: #aaaa33; stroke-width: 1px; }
    .cluster text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 14px; font-weight: bold; }
  </style>

  <!-- User Interface Layer -->
  <rect x="50" y="50" width="200" height="180" rx="5" ry="5" class="cluster"/>
  <text x="150" y="40" text-anchor="middle" class="cluster">User Interface Layer</text>

  <rect x="70" y="70" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="150" y="90" text-anchor="middle" class="node">Auth Layout</text>

  <rect x="70" y="110" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="150" y="130" text-anchor="middle" class="node">Admin Layout</text>

  <rect x="70" y="150" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="150" y="170" text-anchor="middle" class="node">Shared Components</text>

  <!-- Feature Modules -->
  <rect x="300" y="50" width="200" height="250" rx="5" ry="5" class="cluster"/>
  <text x="400" y="40" text-anchor="middle" class="cluster">Feature Modules</text>

  <rect x="320" y="70" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="400" y="90" text-anchor="middle" class="node">Authentication</text>

  <rect x="320" y="110" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="400" y="130" text-anchor="middle" class="node">Dashboard</text>

  <rect x="320" y="150" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="400" y="170" text-anchor="middle" class="node">Investment Funds</text>

  <rect x="320" y="190" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="400" y="210" text-anchor="middle" class="node">Fund Strategies</text>

  <rect x="320" y="230" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="400" y="250" text-anchor="middle" class="node">Voting System</text>

  <!-- Core Services -->
  <rect x="550" y="50" width="200" height="200" rx="5" ry="5" class="cluster"/>
  <text x="650" y="40" text-anchor="middle" class="cluster">Core Services</text>

  <rect x="570" y="70" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="650" y="90" text-anchor="middle" class="node">API Services</text>

  <rect x="570" y="110" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="650" y="130" text-anchor="middle" class="node">Auth Service</text>

  <rect x="570" y="150" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="650" y="170" text-anchor="middle" class="node">Language Service</text>

  <rect x="570" y="190" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="650" y="210" text-anchor="middle" class="node">Token Service</text>

  <!-- Shared Utilities -->
  <rect x="800" y="50" width="200" height="200" rx="5" ry="5" class="cluster"/>
  <text x="900" y="40" text-anchor="middle" class="cluster">Shared Utilities</text>

  <rect x="820" y="70" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="900" y="90" text-anchor="middle" class="node">Pipes</text>

  <rect x="820" y="110" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="900" y="130" text-anchor="middle" class="node">Directives</text>

  <rect x="820" y="150" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="900" y="170" text-anchor="middle" class="node">Components</text>

  <rect x="820" y="190" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="900" y="210" text-anchor="middle" class="node">Interfaces</text>

  <!-- Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>

  <!-- Auth Layout to Authentication -->
  <path d="M 230 85 L 320 85" class="edgePath" marker-end="url(#arrowhead)"/>

  <!-- Admin Layout to other features -->
  <path d="M 230 125 L 320 125" class="edgePath" marker-end="url(#arrowhead)"/>
  <path d="M 230 125 L 320 165" class="edgePath" marker-end="url(#arrowhead)"/>
  <path d="M 230 125 L 320 205" class="edgePath" marker-end="url(#arrowhead)"/>
  <path d="M 230 125 L 320 245" class="edgePath" marker-end="url(#arrowhead)"/>

  <!-- Features to Core Services -->
  <path d="M 480 85 L 570 125" class="edgePath" marker-end="url(#arrowhead)"/>
  <path d="M 480 125 L 570 85" class="edgePath" marker-end="url(#arrowhead)"/>
  <path d="M 480 165 L 570 85" class="edgePath" marker-end="url(#arrowhead)"/>
  <path d="M 480 205 L 570 85" class="edgePath" marker-end="url(#arrowhead)"/>
  <path d="M 480 245 L 570 85" class="edgePath" marker-end="url(#arrowhead)"/>

  <!-- API Services to Token Service -->
  <path d="M 650 100 L 650 190" class="edgePath" marker-end="url(#arrowhead)"/>

  <!-- Auth Service to Token Service -->
  <path d="M 650 140 L 650 190" class="edgePath" marker-end="url(#arrowhead)"/>

  <!-- Shared Components to Components -->
  <path d="M 230 165 Q 515 165 820 165" class="edgePath" marker-end="url(#arrowhead)"/>

  <!-- Investment Funds to Pipes -->
  <path d="M 480 165 Q 650 85 820 85" class="edgePath" marker-end="url(#arrowhead)"/>

  <!-- Fund Strategies to Directives -->
  <path d="M 480 205 Q 650 125 820 125" class="edgePath" marker-end="url(#arrowhead)"/>
</svg>
</div>

### 🔄 Application Flow

The application follows a well-defined flow pattern that ensures security, performance, and maintainability:

| Step | Process | Description |
|------|---------|-------------|
| **1️⃣** | **Authentication Flow** | User login → JWT token storage → Route protection |
| **2️⃣** | **Main Application** | Dashboard → Feature modules → API interactions |
| **3️⃣** | **Data Flow** | Components → Services → HTTP Client → API → Response handling |
| **4️⃣** | **State Management** | Local component state + service-based shared state |

#### 🎯 Key Flow Characteristics
- **🔐 Security-First**: Every request is authenticated and authorized
- **⚡ Performance-Optimized**: Lazy loading and efficient data handling
- **🔄 Reactive**: Observable-based data flow for real-time updates
- **🧩 Modular**: Clear separation between different application concerns

---

## 🔧 Low-Level Architecture

### 📁 Project Structure

The application follows a **feature-based architecture** with clear separation of concerns:

```
📁 src/app/
├── 🏗️ core/                    # Core functionality & infrastructure
│   ├── 🔌 api/                # Generated API clients (NSwag)
│   ├── 🛡️ guards/             # Route guards & access control
│   ├── 🔄 interceptors/       # HTTP interceptors (auth, error)
│   ├── 🎨 layout/             # Layout components & shells
│   ├── ⚙️ services/           # Core business services
│   └── 📝 enums/              # Application-wide enumerations
├── 🎯 features/               # Feature modules (lazy-loaded)
│   ├── 🔐 auth/               # Authentication & user management
│   ├── 📊 dashboard/          # Main dashboard & analytics
│   ├── 💰 investment-funds/   # Fund management & operations
│   ├── 📈 fund-strategies/    # Strategy configuration & management
│   └── 🗳️ voting/             # Voting system & governance
├── 🔧 shared/                 # Shared utilities & components
│   ├── 🎨 components/         # Reusable UI components
│   ├── 🔄 pipes/              # Custom transformation pipes
│   ├── 📐 directives/         # Custom Angular directives
│   ├── ⚙️ services/           # Utility & helper services
│   └── 📋 interfaces/         # TypeScript type definitions
└── 🎭 assets/                 # Static assets & resources
```

#### 📂 Directory Organization Principles

| Directory | Purpose | Key Features |
|-----------|---------|--------------|
| **🏗️ Core** | Application foundation | Singletons, guards, interceptors |
| **🎯 Features** | Business functionality | Lazy-loaded, self-contained modules |
| **🔧 Shared** | Reusable components | Cross-feature utilities and UI elements |
| **🎭 Assets** | Static resources | Images, fonts, configuration files |

### 🎨 Component Architecture

The application uses a **hierarchical component structure** with clear separation between layout, feature, and shared components:

<div align="center">
<svg viewBox="0 0 1000 700" style="max-width: 100%; height: auto;" xmlns="http://www.w3.org/2000/svg">
  <style>
    .node rect { fill: #ECECFF; stroke: #9370DB; stroke-width: 1px; }
    .node text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 11px; }
    .edgePath path { stroke: #333; stroke-width: 1.5px; }
    .cluster rect { fill: #ffffde; stroke: #aaaa33; stroke-width: 1px; }
    .cluster text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 13px; font-weight: bold; }
  </style>

  <!-- Layout Components -->
  <rect x="50" y="50" width="280" height="220" rx="5" ry="5" class="cluster"/>
  <text x="190" y="40" text-anchor="middle" class="cluster">Layout Components</text>

  <rect x="70" y="70" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="130" y="87" text-anchor="middle" class="node">App Component</text>

  <rect x="200" y="70" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="260" y="87" text-anchor="middle" class="node">Auth Layout</text>

  <rect x="70" y="105" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="130" y="122" text-anchor="middle" class="node">Admin Layout</text>

  <rect x="200" y="105" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="260" y="122" text-anchor="middle" class="node">Header Component</text>

  <rect x="70" y="140" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="130" y="157" text-anchor="middle" class="node">Side Navigation</text>

  <rect x="200" y="140" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="260" y="157" text-anchor="middle" class="node">Breadcrumb</text>

  <!-- Feature Components -->
  <rect x="370" y="50" width="280" height="220" rx="5" ry="5" class="cluster"/>
  <text x="510" y="40" text-anchor="middle" class="cluster">Feature Components</text>

  <rect x="390" y="70" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="450" y="87" text-anchor="middle" class="node">Login Component</text>

  <rect x="520" y="70" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="580" y="87" text-anchor="middle" class="node">Dashboard Component</text>

  <rect x="390" y="105" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="450" y="122" text-anchor="middle" class="node">Investment Funds List</text>

  <rect x="520" y="105" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="580" y="122" text-anchor="middle" class="node">Fund Details</text>

  <rect x="390" y="140" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="450" y="157" text-anchor="middle" class="node">Fund Strategies</text>

  <rect x="520" y="140" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="580" y="157" text-anchor="middle" class="node">Voting Card</text>

  <!-- Shared Components -->
  <rect x="690" y="50" width="280" height="220" rx="5" ry="5" class="cluster"/>
  <text x="830" y="40" text-anchor="middle" class="cluster">Shared Components</text>

  <rect x="710" y="70" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="770" y="87" text-anchor="middle" class="node">Table Component</text>

  <rect x="840" y="70" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="900" y="87" text-anchor="middle" class="node">Form Builder</text>

  <rect x="710" y="105" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="770" y="122" text-anchor="middle" class="node">Custom Button</text>

  <rect x="840" y="105" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="900" y="122" text-anchor="middle" class="node">Alert Component</text>

  <rect x="710" y="140" width="120" height="25" rx="5" ry="5" class="node"/>
  <text x="770" y="157" text-anchor="middle" class="node">Page Header</text>

  <!-- Arrows -->
  <defs>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>

  <!-- App Component connections -->
  <path d="M 150 95 L 200 82" class="edgePath" marker-end="url(#arrowhead2)"/>
  <path d="M 150 95 L 130 105" class="edgePath" marker-end="url(#arrowhead2)"/>

  <!-- Admin Layout connections -->
  <path d="M 190 117 L 200 117" class="edgePath" marker-end="url(#arrowhead2)"/>
  <path d="M 150 130 L 130 140" class="edgePath" marker-end="url(#arrowhead2)"/>
  <path d="M 190 117 L 200 152" class="edgePath" marker-end="url(#arrowhead2)"/>

  <!-- Auth Layout to Login -->
  <path d="M 320 82 L 390 82" class="edgePath" marker-end="url(#arrowhead2)"/>

  <!-- Admin Layout to features -->
  <path d="M 190 117 Q 290 82 520 82" class="edgePath" marker-end="url(#arrowhead2)"/>
  <path d="M 190 117 Q 290 117 390 117" class="edgePath" marker-end="url(#arrowhead2)"/>
  <path d="M 190 117 Q 290 152 390 152" class="edgePath" marker-end="url(#arrowhead2)"/>

  <!-- Investment Funds to Fund Details -->
  <path d="M 510 117 L 520 117" class="edgePath" marker-end="url(#arrowhead2)"/>

  <!-- Dashboard to Voting Card -->
  <path d="M 580 95 L 580 140" class="edgePath" marker-end="url(#arrowhead2)"/>

  <!-- Features to Shared Components -->
  <path d="M 510 117 L 710 82" class="edgePath" marker-end="url(#arrowhead2)"/>
  <path d="M 640 117 L 840 87" class="edgePath" marker-end="url(#arrowhead2)"/>
  <path d="M 510 152 L 710 117" class="edgePath" marker-end="url(#arrowhead2)"/>
  <path d="M 510 82 L 840 117" class="edgePath" marker-end="url(#arrowhead2)"/>
  <path d="M 510 117 L 710 152" class="edgePath" marker-end="url(#arrowhead2)"/>
</svg>
</div>

#### 🏗️ Component Types

| Type | Purpose | Examples | Characteristics |
|------|---------|----------|----------------|
| **🏠 Layout** | Application shell | App, Auth Layout, Admin Layout | Structural, persistent |
| **🎯 Feature** | Business functionality | Dashboard, Fund Management | Domain-specific, lazy-loaded |
| **🔧 Shared** | Reusable UI elements | Table, Form Builder, Buttons | Generic, highly reusable |

### 🔄 Data Flow Architecture

The application implements a **unidirectional data flow** pattern with reactive programming:

<div align="center">
<svg viewBox="0 0 800 500" style="max-width: 100%; height: auto;" xmlns="http://www.w3.org/2000/svg">
  <style>
    .actor rect { fill: #ECECFF; stroke: #9370DB; stroke-width: 2px; }
    .actor text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 12px; font-weight: bold; }
    .message line { stroke: #333; stroke-width: 2px; }
    .message text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 11px; }
    .activation rect { fill: #f4f4f4; stroke: #666; stroke-width: 1px; }
  </style>

  <!-- Actors -->
  <rect x="50" y="30" width="80" height="40" rx="5" ry="5" class="actor"/>
  <text x="90" y="55" text-anchor="middle" class="actor">User</text>
  <line x1="90" y1="70" x2="90" y2="450" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>

  <rect x="180" y="30" width="100" height="40" rx="5" ry="5" class="actor"/>
  <text x="230" y="55" text-anchor="middle" class="actor">Component</text>
  <line x1="230" y1="70" x2="230" y2="450" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>

  <rect x="330" y="30" width="80" height="40" rx="5" ry="5" class="actor"/>
  <text x="370" y="55" text-anchor="middle" class="actor">Service</text>
  <line x1="370" y1="70" x2="370" y2="450" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>

  <rect x="460" y="30" width="100" height="40" rx="5" ry="5" class="actor"/>
  <text x="510" y="55" text-anchor="middle" class="actor">Interceptor</text>
  <line x1="510" y1="70" x2="510" y2="450" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>

  <rect x="610" y="30" width="80" height="40" rx="5" ry="5" class="actor"/>
  <text x="650" y="55" text-anchor="middle" class="actor">API</text>
  <line x1="650" y1="70" x2="650" y2="450" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>

  <!-- Messages -->
  <defs>
    <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>

  <!-- User Action -->
  <line x1="90" y1="100" x2="230" y2="100" class="message" marker-end="url(#arrowhead3)"/>
  <text x="160" y="95" text-anchor="middle" class="message">User Action</text>

  <!-- Call Service Method -->
  <line x1="230" y1="130" x2="370" y2="130" class="message" marker-end="url(#arrowhead3)"/>
  <text x="300" y="125" text-anchor="middle" class="message">Call Service Method</text>

  <!-- HTTP Request -->
  <line x1="370" y1="160" x2="510" y2="160" class="message" marker-end="url(#arrowhead3)"/>
  <text x="440" y="155" text-anchor="middle" class="message">HTTP Request</text>

  <!-- Add Auth Token (self-call) -->
  <rect x="510" y="180" width="15" height="30" class="activation"/>
  <path d="M 510 190 Q 530 190 510 200" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead3)"/>
  <text x="540" y="195" class="message">Add Auth Token</text>

  <!-- API Call -->
  <line x1="510" y1="220" x2="650" y2="220" class="message" marker-end="url(#arrowhead3)"/>
  <text x="580" y="215" text-anchor="middle" class="message">API Call</text>

  <!-- Response -->
  <line x1="650" y1="250" x2="510" y2="250" class="message" marker-end="url(#arrowhead3)"/>
  <text x="580" y="245" text-anchor="middle" class="message">Response</text>

  <!-- Handle Errors (self-call) -->
  <rect x="510" y="270" width="15" height="30" class="activation"/>
  <path d="M 510 280 Q 530 280 510 290" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead3)"/>
  <text x="540" y="285" class="message">Handle Errors</text>

  <!-- Processed Response -->
  <line x1="510" y1="310" x2="370" y2="310" class="message" marker-end="url(#arrowhead3)"/>
  <text x="440" y="305" text-anchor="middle" class="message">Processed Response</text>

  <!-- Observable Data -->
  <line x1="370" y1="340" x2="230" y2="340" class="message" marker-end="url(#arrowhead3)"/>
  <text x="300" y="335" text-anchor="middle" class="message">Observable Data</text>

  <!-- Update UI -->
  <line x1="230" y1="370" x2="90" y2="370" class="message" marker-end="url(#arrowhead3)"/>
  <text x="160" y="365" text-anchor="middle" class="message">Update UI</text>
</svg>
</div>

#### 🔄 Data Flow Principles

| Principle | Description | Benefits |
|-----------|-------------|----------|
| **📥 Unidirectional** | Data flows in one direction | Predictable state changes |
| **⚡ Reactive** | Observable-based patterns | Real-time updates and error handling |
| **🔒 Immutable** | State is never mutated directly | Safer state management |
| **🎯 Centralized** | Services manage shared state | Consistent data across components |

---

## ⚙️ Service Architecture

### 🔌 API Service Layer

The application uses **auto-generated TypeScript clients** from the backend API using NSwag. This ensures **type safety** and **consistency** between frontend and backend.

#### 🎯 Key Benefits
- **🔒 Type Safety**: Compile-time error checking
- **🔄 Consistency**: Automatic synchronization with backend changes
- **⚡ Performance**: Optimized HTTP client generation
- **🛠️ Maintainability**: Reduced manual coding and errors

<div align="center">
<svg viewBox="0 0 1000 600" style="max-width: 100%; height: auto;" xmlns="http://www.w3.org/2000/svg">
  <style>
    .node rect { fill: #ECECFF; stroke: #9370DB; stroke-width: 1px; }
    .node text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 11px; }
    .edgePath path { stroke: #333; stroke-width: 1.5px; }
    .cluster rect { fill: #ffffde; stroke: #aaaa33; stroke-width: 1px; }
    .cluster text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 13px; font-weight: bold; }
  </style>

  <!-- Generated API Services -->
  <rect x="50" y="50" width="280" height="250" rx="5" ry="5" class="cluster"/>
  <text x="190" y="40" text-anchor="middle" class="cluster">Generated API Services</text>

  <rect x="70" y="70" width="240" height="25" rx="5" ry="5" class="node"/>
  <text x="190" y="87" text-anchor="middle" class="node">AuthenticationServiceProxy</text>

  <rect x="70" y="105" width="240" height="25" rx="5" ry="5" class="node"/>
  <text x="190" y="122" text-anchor="middle" class="node">FundsServiceProxy</text>

  <rect x="70" y="140" width="240" height="25" rx="5" ry="5" class="node"/>
  <text x="190" y="157" text-anchor="middle" class="node">StrategiesServiceProxy</text>

  <rect x="70" y="175" width="240" height="25" rx="5" ry="5" class="node"/>
  <text x="190" y="192" text-anchor="middle" class="node">UsersServiceProxy</text>

  <rect x="70" y="210" width="240" height="25" rx="5" ry="5" class="node"/>
  <text x="190" y="227" text-anchor="middle" class="node">FileManagementServiceProxy</text>

  <rect x="70" y="245" width="240" height="25" rx="5" ry="5" class="node"/>
  <text x="190" y="262" text-anchor="middle" class="node">AuthorizationServiceProxy</text>

  <!-- Custom Services -->
  <rect x="370" y="50" width="200" height="250" rx="5" ry="5" class="cluster"/>
  <text x="470" y="40" text-anchor="middle" class="cluster">Custom Services</text>

  <rect x="390" y="70" width="160" height="25" rx="5" ry="5" class="node"/>
  <text x="470" y="87" text-anchor="middle" class="node">AuthService</text>

  <rect x="390" y="105" width="160" height="25" rx="5" ry="5" class="node"/>
  <text x="470" y="122" text-anchor="middle" class="node">TokenService</text>

  <rect x="390" y="140" width="160" height="25" rx="5" ry="5" class="node"/>
  <text x="470" y="157" text-anchor="middle" class="node">FundsService</text>

  <rect x="390" y="175" width="160" height="25" rx="5" ry="5" class="node"/>
  <text x="470" y="192" text-anchor="middle" class="node">StrategyService</text>

  <rect x="390" y="210" width="160" height="25" rx="5" ry="5" class="node"/>
  <text x="470" y="227" text-anchor="middle" class="node">LanguageService</text>

  <rect x="390" y="245" width="160" height="25" rx="5" ry="5" class="node"/>
  <text x="470" y="262" text-anchor="middle" class="node">FileUploadService</text>

  <!-- HTTP Layer -->
  <rect x="620" y="150" width="200" height="150" rx="5" ry="5" class="cluster"/>
  <text x="720" y="140" text-anchor="middle" class="cluster">HTTP Layer</text>

  <rect x="640" y="170" width="160" height="25" rx="5" ry="5" class="node"/>
  <text x="720" y="187" text-anchor="middle" class="node">HTTP Client</text>

  <rect x="640" y="205" width="160" height="25" rx="5" ry="5" class="node"/>
  <text x="720" y="222" text-anchor="middle" class="node">Token Interceptor</text>

  <rect x="640" y="240" width="160" height="25" rx="5" ry="5" class="node"/>
  <text x="720" y="257" text-anchor="middle" class="node">Error Interceptor</text>

  <!-- Arrows -->
  <defs>
    <marker id="arrowhead4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>

  <!-- Generated Services to HTTP Client -->
  <path d="M 310 82 L 640 182" class="edgePath" marker-end="url(#arrowhead4)"/>
  <path d="M 310 117 L 640 182" class="edgePath" marker-end="url(#arrowhead4)"/>
  <path d="M 310 152 L 640 182" class="edgePath" marker-end="url(#arrowhead4)"/>
  <path d="M 310 187 L 640 182" class="edgePath" marker-end="url(#arrowhead4)"/>
  <path d="M 310 222 L 640 182" class="edgePath" marker-end="url(#arrowhead4)"/>
  <path d="M 310 257 L 640 182" class="edgePath" marker-end="url(#arrowhead4)"/>

  <!-- Custom Services to Generated Services -->
  <path d="M 390 82 L 310 82" class="edgePath" marker-end="url(#arrowhead4)"/>
  <path d="M 390 152 L 310 117" class="edgePath" marker-end="url(#arrowhead4)"/>
  <path d="M 390 187 L 310 152" class="edgePath" marker-end="url(#arrowhead4)"/>
  <path d="M 390 262 L 310 222" class="edgePath" marker-end="url(#arrowhead4)"/>

  <!-- HTTP Client to Interceptors -->
  <path d="M 720 195 L 720 205" class="edgePath" marker-end="url(#arrowhead4)"/>
  <path d="M 720 195 L 720 240" class="edgePath" marker-end="url(#arrowhead4)"/>
</svg>
</div>

### 🎯 Service Responsibilities

#### 🏗️ Core Services

| Service | Purpose | Key Features |
|---------|---------|--------------|
| **🔐 AuthService** | Authentication state management | Login/logout, token validation, user session |
| **🎫 TokenService** | JWT token operations | Token storage, decoding, expiry handling |
| **🌍 LanguageService** | Internationalization | RTL/LTR switching, translation management |
| **⚠️ ErrorModalService** | Global error handling | User notifications, error display, logging |

#### 🎯 Feature Services

| Service | Domain | Functionality |
|---------|--------|---------------|
| **💰 FundsService** | Investment Management | Fund CRUD operations, portfolio management |
| **📈 StrategyService** | Strategy Management | Strategy configuration, performance tracking |
| **📁 FileUploadService** | Document Management | File upload, storage, retrieval |
| **📅 DateConversionService** | Localization | Hijri/Gregorian date conversions |

#### 🔧 API Integration Pattern

```typescript
// 🎯 Service Pattern Example - Clean and Type-Safe
@Injectable({ providedIn: 'root' })
export class FundsService {
  constructor(
    private http: HttpClient,
    private fundsProxy: FundsServiceProxy  // 🔌 Auto-generated client
  ) {}

  // 📊 Get fund details with error handling
  getFundDetails(id: number): Observable<FundDetailsDto> {
    return this.fundsProxy.getFundDetails(id).pipe(
      catchError(this.handleError),      // 🛡️ Error handling
      map(response => response.data),    // 🔄 Data extraction
      shareReplay(1)                     // ⚡ Caching optimization
    );
  }

  // 🛡️ Centralized error handling
  private handleError(error: HttpErrorResponse): Observable<never> {
    this.errorService.showError(error.message);
    return throwError(() => error);
  }
}
```

### 💾 State Management Strategy

The application implements a **hybrid state management approach** combining local component state with service-based shared state:

<div align="center">
<svg viewBox="0 0 900 400" style="max-width: 100%; height: auto;" xmlns="http://www.w3.org/2000/svg">
  <style>
    .node rect { fill: #ECECFF; stroke: #9370DB; stroke-width: 1px; }
    .node text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 12px; }
    .edgePath path { stroke: #333; stroke-width: 1.5px; }
    .cluster rect { fill: #ffffde; stroke: #aaaa33; stroke-width: 1px; }
    .cluster text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 14px; font-weight: bold; }
  </style>

  <!-- Component State -->
  <rect x="50" y="50" width="200" height="150" rx="5" ry="5" class="cluster"/>
  <text x="150" y="40" text-anchor="middle" class="cluster">Component State</text>

  <rect x="70" y="70" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="150" y="90" text-anchor="middle" class="node">Local Component State</text>

  <rect x="70" y="110" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="150" y="130" text-anchor="middle" class="node">Form State</text>

  <rect x="70" y="150" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="150" y="170" text-anchor="middle" class="node">UI State</text>

  <!-- Service State -->
  <rect x="300" y="50" width="200" height="150" rx="5" ry="5" class="cluster"/>
  <text x="400" y="40" text-anchor="middle" class="cluster">Service State</text>

  <rect x="320" y="70" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="400" y="90" text-anchor="middle" class="node">Authentication State</text>

  <rect x="320" y="110" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="400" y="130" text-anchor="middle" class="node">Language State</text>

  <rect x="320" y="150" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="400" y="170" text-anchor="middle" class="node">Theme State</text>

  <!-- Persistent State -->
  <rect x="550" y="50" width="200" height="150" rx="5" ry="5" class="cluster"/>
  <text x="650" y="40" text-anchor="middle" class="cluster">Persistent State</text>

  <rect x="570" y="70" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="650" y="90" text-anchor="middle" class="node">LocalStorage</text>

  <rect x="570" y="130" width="160" height="30" rx="5" ry="5" class="node"/>
  <text x="650" y="150" text-anchor="middle" class="node">Session Storage</text>

  <!-- Arrows -->
  <defs>
    <marker id="arrowhead5" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>

  <!-- Component State internal connections -->
  <path d="M 150 100 L 150 110" class="edgePath" marker-end="url(#arrowhead5)"/>
  <path d="M 150 100 L 150 150" class="edgePath" marker-end="url(#arrowhead5)"/>

  <!-- Service State to Persistent State -->
  <path d="M 480 85 L 570 85" class="edgePath" marker-end="url(#arrowhead5)"/>
  <path d="M 480 125 L 570 85" class="edgePath" marker-end="url(#arrowhead5)"/>
  <path d="M 480 165 L 570 145" class="edgePath" marker-end="url(#arrowhead5)"/>

  <!-- Service State to Component State -->
  <path d="M 320 85 Q 235 85 230 85" class="edgePath" marker-end="url(#arrowhead5)"/>
  <path d="M 320 125 Q 235 125 230 125" class="edgePath" marker-end="url(#arrowhead5)"/>
  <path d="M 320 165 Q 235 165 230 165" class="edgePath" marker-end="url(#arrowhead5)"/>
</svg>
</div>

#### 💾 State Management Layers

| Layer | Scope | Storage | Use Cases |
|-------|-------|---------|-----------|
| **🎨 Component State** | Local to component | Memory | UI state, form data, temporary values |
| **⚙️ Service State** | Application-wide | BehaviorSubject | User session, app configuration, shared data |
| **💾 Persistent State** | Browser storage | LocalStorage/SessionStorage | User preferences, tokens, cache |

#### 🔄 State Flow Patterns

- **📥 Input**: User interactions and API responses
- **🔄 Processing**: Services transform and validate data
- **📤 Output**: Components receive updates via observables
- **💾 Persistence**: Critical data saved to browser storage

---

## 🔐 Security Architecture

### 🔑 Authentication Flow

The application implements a **comprehensive security model** with multiple layers of protection:

<div align="center">
<svg viewBox="0 0 1000 600" style="max-width: 100%; height: auto;" xmlns="http://www.w3.org/2000/svg">
  <style>
    .actor rect { fill: #ECECFF; stroke: #9370DB; stroke-width: 2px; }
    .actor text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 11px; font-weight: bold; }
    .message line { stroke: #333; stroke-width: 2px; }
    .message text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 10px; }
    .activation rect { fill: #f4f4f4; stroke: #666; stroke-width: 1px; }
    .note rect { fill: #ffffcc; stroke: #aaaa33; stroke-width: 1px; }
    .note text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 10px; }
  </style>

  <!-- Actors -->
  <rect x="30" y="30" width="60" height="35" rx="5" ry="5" class="actor"/>
  <text x="60" y="52" text-anchor="middle" class="actor">User</text>
  <line x1="60" y1="65" x2="60" y2="550" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>

  <rect x="130" y="30" width="100" height="35" rx="5" ry="5" class="actor"/>
  <text x="180" y="52" text-anchor="middle" class="actor">Login Component</text>
  <line x1="180" y1="65" x2="180" y2="550" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>

  <rect x="270" y="30" width="90" height="35" rx="5" ry="5" class="actor"/>
  <text x="315" y="52" text-anchor="middle" class="actor">Auth Service</text>
  <line x1="315" y1="65" x2="315" y2="550" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>

  <rect x="400" y="30" width="90" height="35" rx="5" ry="5" class="actor"/>
  <text x="445" y="52" text-anchor="middle" class="actor">Token Service</text>
  <line x1="445" y1="65" x2="445" y2="550" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>

  <rect x="530" y="30" width="90" height="35" rx="5" ry="5" class="actor"/>
  <text x="575" y="52" text-anchor="middle" class="actor">Backend API</text>
  <line x1="575" y1="65" x2="575" y2="550" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>

  <rect x="660" y="30" width="80" height="35" rx="5" ry="5" class="actor"/>
  <text x="700" y="52" text-anchor="middle" class="actor">Auth Guard</text>
  <line x1="700" y1="65" x2="700" y2="550" stroke="#333" stroke-width="2" stroke-dasharray="5,5"/>

  <!-- Messages -->
  <defs>
    <marker id="arrowhead6" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>

  <!-- Enter Credentials -->
  <line x1="60" y1="90" x2="180" y2="90" class="message" marker-end="url(#arrowhead6)"/>
  <text x="120" y="85" text-anchor="middle" class="message">Enter Credentials</text>

  <!-- login(credentials) -->
  <line x1="180" y1="110" x2="315" y2="110" class="message" marker-end="url(#arrowhead6)"/>
  <text x="247" y="105" text-anchor="middle" class="message">login(credentials)</text>

  <!-- POST /auth/signin -->
  <line x1="315" y1="130" x2="575" y2="130" class="message" marker-end="url(#arrowhead6)"/>
  <text x="445" y="125" text-anchor="middle" class="message">POST /auth/signin</text>

  <!-- JWT Tokens -->
  <line x1="575" y1="150" x2="315" y2="150" class="message" marker-end="url(#arrowhead6)"/>
  <text x="445" y="145" text-anchor="middle" class="message">JWT Tokens</text>

  <!-- Store Tokens -->
  <line x1="315" y1="170" x2="445" y2="170" class="message" marker-end="url(#arrowhead6)"/>
  <text x="380" y="165" text-anchor="middle" class="message">Store Tokens</text>

  <!-- Decode JWT (self-call) -->
  <rect x="445" y="185" width="15" height="25" class="activation"/>
  <path d="M 445 195 Q 465 195 445 205" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead6)"/>
  <text x="475" y="200" class="message">Decode JWT</text>

  <!-- Success Response -->
  <line x1="315" y1="220" x2="180" y2="220" class="message" marker-end="url(#arrowhead6)"/>
  <text x="247" y="215" text-anchor="middle" class="message">Success Response</text>

  <!-- Redirect to Dashboard -->
  <line x1="180" y1="240" x2="60" y2="240" class="message" marker-end="url(#arrowhead6)"/>
  <text x="120" y="235" text-anchor="middle" class="message">Redirect to Dashboard</text>

  <!-- Route Protection Note -->
  <rect x="620" y="270" width="120" height="30" rx="5" ry="5" class="note"/>
  <text x="680" y="290" text-anchor="middle" class="note">Route Protection</text>

  <!-- Navigate to Protected Route -->
  <line x1="60" y1="320" x2="700" y2="320" class="message" marker-end="url(#arrowhead6)"/>
  <text x="380" y="315" text-anchor="middle" class="message">Navigate to Protected Route</text>

  <!-- isAuthenticated() -->
  <line x1="700" y1="340" x2="315" y2="340" class="message" marker-end="url(#arrowhead6)"/>
  <text x="507" y="335" text-anchor="middle" class="message">isAuthenticated()</text>

  <!-- Check Token Validity -->
  <line x1="315" y1="360" x2="445" y2="360" class="message" marker-end="url(#arrowhead6)"/>
  <text x="380" y="355" text-anchor="middle" class="message">Check Token Validity</text>

  <!-- Token Status -->
  <line x1="445" y1="380" x2="700" y2="380" class="message" marker-end="url(#arrowhead6)"/>
  <text x="572" y="375" text-anchor="middle" class="message">Token Status</text>

  <!-- Alternative flows -->
  <rect x="750" y="400" width="200" height="80" rx="5" ry="5" class="note"/>
  <text x="850" y="420" text-anchor="middle" class="note">Token Valid:</text>
  <text x="850" y="440" text-anchor="middle" class="message">Allow Access</text>
  <text x="850" y="460" text-anchor="middle" class="note">Token Invalid:</text>
  <text x="850" y="480" text-anchor="middle" class="message">Redirect to Login</text>

  <!-- Allow Access / Redirect arrows -->
  <line x1="700" y1="420" x2="60" y2="420" class="message" marker-end="url(#arrowhead6)" stroke="#008000"/>
  <line x1="700" y1="460" x2="60" y2="460" class="message" marker-end="url(#arrowhead6)" stroke="#ff0000"/>
</svg>
</div>

#### 🔐 Authentication Process Flow

| Step | Actor | Action | Security Measure |
|------|-------|--------|------------------|
| **1️⃣** | User | Enter credentials | Input validation and sanitization |
| **2️⃣** | Frontend | Send login request | HTTPS encryption |
| **3️⃣** | Backend | Validate credentials | Secure password hashing |
| **4️⃣** | Backend | Issue JWT tokens | Signed tokens with expiry |
| **5️⃣** | Frontend | Store tokens securely | Secure storage mechanisms |
| **6️⃣** | Frontend | Access protected routes | Route guards and token validation |

### 🛡️ Security Features

#### 🔄 HTTP Interceptors

| Interceptor | Purpose | Security Benefits |
|-------------|---------|-------------------|
| **🎫 Token Interceptor** | Automatic JWT token attachment | Seamless authentication for all API calls |
| **⚠️ Error Interceptor** | Handle 401/403 responses | Automatic logout and redirect on auth failures |
| **🌍 Language Interceptor** | Add Accept-Language headers | Proper localization without exposing user data |

#### 🛡️ Route Protection

| Component | Function | Protection Level |
|-----------|----------|------------------|
| **🚪 AuthGuard** | Protect admin routes | Prevents unauthorized access to sensitive areas |
| **⏰ Token Validation** | Check token expiry | Automatic session management |
| **🔄 Redirect Logic** | Handle authentication flow | Seamless user experience with security |

#### 🔒 Security Implementation

```typescript
// 🛡️ Token Interceptor - Secure Implementation
export const tokenInterceptor: HttpInterceptorFn = (req, next) => {
  const token = localStorage.getItem('auth_token');        // 🔐 Secure token retrieval
  const lang = localStorage.getItem('lang') || 'ar';       // 🌍 Language preference

  if (token) {
    req = req.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`,                   // 🎫 JWT Bearer token
        'Accept-Language': lang === 'en' ? 'en-US' : 'ar-EG', // 🌍 Localization
        'X-Requested-With': 'XMLHttpRequest',              // 🛡️ CSRF protection
        'Content-Type': 'application/json'                 // 📝 Content type specification
      }
    });
  }

  return next(req).pipe(
    catchError(handleAuthError),                           // ⚠️ Error handling
    timeout(30000),                                        // ⏰ Request timeout
    retry(2)                                               // 🔄 Retry mechanism
  );
};
```

#### 🔐 Security Best Practices

| Practice | Implementation | Benefit |
|----------|----------------|---------|
| **🔒 Token Security** | Secure storage, automatic expiry | Prevents token theft and misuse |
| **🛡️ CSRF Protection** | Custom headers and validation | Prevents cross-site request forgery |
| **⏰ Session Management** | Automatic logout on expiry | Reduces security exposure |
| **🔄 Error Handling** | Secure error messages | Prevents information leakage |

---

## 🚀 Deployment Architecture

### 🌍 Environment Configuration

The application supports **multiple deployment environments** with specific configurations for each stage:

<div align="center">
<svg viewBox="0 0 1000 500" style="max-width: 100%; height: auto;" xmlns="http://www.w3.org/2000/svg">
  <style>
    .node rect { fill: #ECECFF; stroke: #9370DB; stroke-width: 1px; }
    .node text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 11px; }
    .edgePath path { stroke: #333; stroke-width: 1.5px; }
    .cluster rect { fill: #ffffde; stroke: #aaaa33; stroke-width: 1px; }
    .cluster text { font-family: "trebuchet ms", verdana, arial, sans-serif; font-size: 13px; font-weight: bold; }
  </style>

  <!-- Development -->
  <rect x="50" y="50" width="180" height="150" rx="5" ry="5" class="cluster"/>
  <text x="140" y="40" text-anchor="middle" class="cluster">Development</text>

  <rect x="70" y="70" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="140" y="87" text-anchor="middle" class="node">Local Environment</text>

  <rect x="70" y="105" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="140" y="122" text-anchor="middle" class="node">localhost:7010 API</text>

  <rect x="70" y="140" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="140" y="157" text-anchor="middle" class="node">Firebase Dev Config</text>

  <!-- Testing -->
  <rect x="270" y="50" width="180" height="150" rx="5" ry="5" class="cluster"/>
  <text x="360" y="40" text-anchor="middle" class="cluster">Testing</text>

  <rect x="290" y="70" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="360" y="87" text-anchor="middle" class="node">Test Environment</text>

  <rect x="290" y="105" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="360" y="122" text-anchor="middle" class="node">Test API Server</text>

  <rect x="290" y="140" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="360" y="157" text-anchor="middle" class="node">Firebase Test Config</text>

  <!-- Production -->
  <rect x="490" y="50" width="180" height="150" rx="5" ry="5" class="cluster"/>
  <text x="580" y="40" text-anchor="middle" class="cluster">Production</text>

  <rect x="510" y="70" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="580" y="87" text-anchor="middle" class="node">Production Environment</text>

  <rect x="510" y="105" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="580" y="122" text-anchor="middle" class="node">Production API</text>

  <rect x="510" y="140" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="580" y="157" text-anchor="middle" class="node">Firebase Prod Config</text>

  <!-- Build Process -->
  <rect x="720" y="50" width="180" height="150" rx="5" ry="5" class="cluster"/>
  <text x="810" y="40" text-anchor="middle" class="cluster">Build Process</text>

  <rect x="740" y="70" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="810" y="87" text-anchor="middle" class="node">Angular CLI Build</text>

  <rect x="740" y="105" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="810" y="122" text-anchor="middle" class="node">Docker Container</text>

  <rect x="740" y="140" width="140" height="25" rx="5" ry="5" class="node"/>
  <text x="810" y="157" text-anchor="middle" class="node">Nginx Server</text>

  <!-- Arrows -->
  <defs>
    <marker id="arrowhead7" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>

  <!-- Environments to Build Process -->
  <path d="M 210 82 Q 465 82 740 82" class="edgePath" marker-end="url(#arrowhead7)"/>
  <path d="M 430 82 Q 585 82 740 82" class="edgePath" marker-end="url(#arrowhead7)"/>
  <path d="M 650 82 L 740 82" class="edgePath" marker-end="url(#arrowhead7)"/>

  <!-- Build Process internal flow -->
  <path d="M 810 95 L 810 105" class="edgePath" marker-end="url(#arrowhead7)"/>
  <path d="M 810 130 L 810 140" class="edgePath" marker-end="url(#arrowhead7)"/>
</svg>
</div>

#### 🌍 Environment Specifications

| Environment | Purpose | API Endpoint | Features |
|-------------|---------|--------------|----------|
| **🔧 Development** | Local development | `localhost:7010` | Hot reload, debugging, dev tools |
| **🧪 Testing** | QA and testing | `test-api.jadwa.com` | Staging data, test configurations |
| **🚀 Production** | Live application | `api.jadwa.com` | Optimized builds, monitoring |

### 🐳 Container Architecture

```dockerfile
# 🏗️ Multi-stage Docker build for optimal performance
FROM node:22 as build
WORKDIR /app

# 📦 Install dependencies
COPY package*.json .
RUN npm install --force --legacy-peer-deps

# 🔨 Build application
COPY . .
RUN npm run build:${environment}

# 🌐 Production server
FROM nginx:alpine
COPY --from=build /app/dist/jadwa /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

# 🔧 Optimize for production
RUN gzip -9 /usr/share/nginx/html/*.js
RUN gzip -9 /usr/share/nginx/html/*.css
```

### ⚙️ Configuration Management

| Variable | Purpose | Example | Environment |
|----------|---------|---------|-------------|
| **🔗 API_URL** | Backend endpoint | `https://api.jadwa.com` | All |
| **🔥 FIREBASE_CONFIG** | Firebase settings | `{apiKey: "...", ...}` | All |
| **🌍 ENVIRONMENT** | Deployment stage | `production` | Build-time |
| **🔐 AUTH_DOMAIN** | Authentication realm | `auth.jadwa.com` | Production |

### 🔨 Build Configurations

| Configuration | Command | Purpose | Optimizations |
|---------------|---------|---------|---------------|
| **🔧 Development** | `ng serve` | Local development | Hot reload, source maps |
| **🧪 Testing** | `ng build --configuration test` | QA deployment | Minification, test configs |
| **🚀 Production** | `ng build --configuration production` | Live deployment | Full optimization, compression |

### 🚀 Deployment Pipeline

| Stage | Process | Tools | Validation |
|-------|---------|-------|------------|
| **1️⃣ Code Commit** | Git repository update | Git, GitLab | Code review, automated tests |
| **2️⃣ Build Process** | Angular CLI build | Angular CLI, Webpack | Build success, linting |
| **3️⃣ Containerization** | Docker image creation | Docker, Multi-stage builds | Image security scan |
| **4️⃣ Deployment** | Container deployment | Kubernetes, Docker Compose | Health checks |
| **5️⃣ Monitoring** | Application monitoring | Logging, Analytics | Performance metrics |

### ⚡ Performance Optimizations

| Optimization | Implementation | Benefit |
|--------------|----------------|---------|
| **🔄 Lazy Loading** | Feature modules loaded on demand | Faster initial load time |
| **🌳 Tree Shaking** | Unused code elimination | Smaller bundle size |
| **📦 Code Splitting** | Vendor and application bundles | Better caching strategy |
| **🗜️ Compression** | Gzip compression in Nginx | Reduced transfer size |
| **💾 Caching** | Static asset caching strategies | Improved repeat visit performance |

---

## 🎯 Conclusion

This architecture provides a **scalable**, **maintainable**, and **secure** foundation for the Jadwa Investment Web Application. The modular design allows for easy feature additions, while the service-oriented approach ensures clean separation of concerns and testability.

### 🏆 Key Benefits

| Benefit | Implementation | Impact |
|---------|----------------|--------|
| **🔧 Maintainability** | Clear module boundaries and separation of concerns | Easier debugging, updates, and team collaboration |
| **📈 Scalability** | Lazy-loaded modules and efficient build process | Supports growing user base and feature set |
| **🔐 Security** | Comprehensive authentication and authorization | Protects sensitive financial data and user privacy |
| **👨‍💻 Developer Experience** | Type safety, auto-generated clients, modern tooling | Faster development cycles and fewer bugs |
| **⚡ Performance** | Optimized build process and deployment strategy | Superior user experience and reduced costs |

### 🚀 Future Considerations

- **📊 Analytics Integration**: Enhanced user behavior tracking
- **🔄 Real-time Features**: WebSocket integration for live updates
- **📱 Mobile App**: React Native or Flutter implementation
- **🤖 AI Integration**: Machine learning for investment recommendations
- **🌐 Microservices**: Backend service decomposition for better scalability

---

> **📝 Note**: This architecture documentation is a living document that should be updated as the application evolves and new requirements emerge.
