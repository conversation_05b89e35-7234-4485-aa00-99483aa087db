.form {
  .row {
    display: flex;
    flex-wrap: wrap;
    margin: -12px;

    .form-group {
      padding: 12px;
      margin-bottom: 0;

      &.col-12 {
        width: 100%;
      }
      &.col-md-6 {
        @media (min-width: 768px) {
          width: 50%;
        }
        @media (max-width: 767px) {
          width: 100%;
        }
      }

      label {
        display: block;
        color: #4f4f4f;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0%;
        font-weight: 400;
        margin-bottom: 8px;
        .required-mark {
          color: var(--system-red);
          margin-right: 4px;
        }
      }

      .textarea{
        border: 1px solid #BDBDBD !important;
        width: 100% !important;
        height: 64px !important;
      }

     .textarea::placeholder {
        color: #BDBDBD !important;
        background-color: #BDBDBD;
      }
      ::ng-deep .textarea::placeholder,
      ::ng-deep .textarea::-webkit-input-placeholder,
      ::ng-deep .textarea:-moz-placeholder,
      ::ng-deep .textarea::-moz-placeholder,
      ::ng-deep .textarea:-ms-input-placeholder {
        color: #BDBDBD !important;
      }

      ::ng-deep .textarea::placeholder {
        color: #BDBDBD !important;
      }

      .form-control , textarea {
        width: 100%;
        height: 42px;
        border-radius: 8px;
        border: 1px solid var(--border-color);
        padding: 0 16px;
        font-size: 14px;
        color: var(--navy-blue);
        background-color: white;
        transition: all 0.2s ease;

        &:focus {
          outline: none;
          box-shadow:none !important;
        }

        &::placeholder {
          // color: var(--mid-gray-bg);
          color:red !important;
        }

        &[rows] {
          height: auto;
          min-height: 65px;
          padding: 12px 16px;
          resize: vertical;
        }
      }

      // Tel input specific styles
      .tel-input-container {
        direction: ltr;
        display: flex;
        align-items: stretch;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        transition: all 0.2s ease;
        overflow: hidden;
        border-radius: 4px;
        background-color: #fff !important;
        border: 1px solid #BDBDBD;
        height: 42px; // Match standard form control height

        // Remove custom focus styling to match standard form controls
        &:focus-within {
          outline: none;
          box-shadow: none !important;
        }

        arabdt-reusable-rf-text-input .form-group {
          margin-top: 0px !important;
        }

        .country-code-prefix {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 12px;
          background-color: #dadada;
          border-right: 1px solid var(--border-color);
          min-width: 70px;
          font-size: 14px;
          font-weight: 500;
          color: var(--text-grey); // Use consistent color variable
          white-space: nowrap;
          user-select: none;
          height: 100%; // Fill container height

          .country-flag {
            margin-right: 6px;
            font-size: 16px;
          }

          .country-code {
            font-weight: 500;
            color: var(--text-grey); // Use consistent color variable
          }
        }

        .tel-input {
          flex: 1;
          display: flex;
          align-items: center;
          height: 100%; // Fill container height

          arabdt-reusable-rf-text-input .form-group{
            margin-top: 0px !important;
          }

          ::ng-deep {
            .form-control {
              border: none !important;
              border-radius: 0 !important;
              box-shadow: none !important;
              padding: 0 16px !important; // Match standard form control padding
              font-size: 14px !important;
              line-height: 1.5 !important;
              height: 40px !important; // Slightly less than container to account for border
              background-color: transparent !important;
              color: var(--navy-blue) !important; // Match standard form control text color

              &:focus {
                border: none !important;
                box-shadow: none !important;
                outline: none !important;
              }

              &::placeholder {
                color: var(--text-grey); // Use consistent placeholder color
                font-size: 14px;
              }
            }
          }
        }

        // Match validation styling with standard form controls
        &.is-invalid {
          border-color: var(--system-red); // Use consistent error color

          .country-code-prefix {
            border-right-color: var(--system-red); // Use consistent error color
          }
        }
      }

      // Password input specific styles
      .password-input-container {
        position: relative;

        .password-toggle-btn {
          position: absolute;
          top: 32%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: #6c757d;
          padding: 0;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          z-index: 10;

          &:hover {
            color: #495057;
          }

          &:focus {
            outline: none;
            color: #007bff;
          }

          i {
            font-size: 14px;
          }
          .eye-icon {
            display: flex;
            justify-content: end;
            align-items: end;
            width: 100%;
            top: 64%;

            img {
              margin: -7px 10px;
              width: 24px;
              height: 24px;

            }
          }
        }

        // RTL support for password toggle
        [dir="rtl"] & {
          .password-toggle-btn {
            right: auto;
            left: 12px;

          }
        }

        // Password strength indicator
        .password-strength-indicator {
          margin-top: 8px;

          .strength-bar {
            width: 100%;
            height: 4px;
            background-color: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 4px;

            .strength-fill {
              height: 100%;
              transition: all 0.3s ease;
              border-radius: 2px;

              &.strength-weak {
                width: 33%;
                background-color: #dc3545;
              }

              &.strength-medium {
                width: 66%;
                background-color: #ffc107;
              }

              &.strength-strong {
                width: 100%;
                background-color: #28a745;
              }

              &.strength-none {
                width: 0%;
                background-color: transparent;
              }
            }
          }

          .strength-text {
            font-size: 12px;
            font-weight: 500;

            &.strength-weak {
              color: #dc3545;
            }

            &.strength-medium {
              color: #ffc107;
            }

            &.strength-strong {
              color: #28a745;
            }

            &.strength-none {
              color: #6c757d;
            }
          }
        }
      }

      // RTL support for tel input
      [dir="rtl"] .tel-input-container {
        .country-code-prefix {
          border-right: none;
          border-left: 1px solid var(--border-color);

          .country-flag {
            margin-right: 0;
            margin-left: 6px;
          }
        }

        .tel-input {
          arabdt-reusable-rf-text-input .form-group{
            margin-top: 0px !important;
          }
          ::ng-deep {
            .form-control {
              padding: 0 16px !important;
              text-align: right !important;
            }
          }
        }

        &.is-invalid {
          .country-code-prefix {
            border-left-color: var(--system-red); // Use consistent error color
            border-right: none;
          }
        }
      }

      .ng-select .ng-select-container {
        height: 48px;
        border-radius: 8px;
        border: 1px solid var(--border-color);
        padding: 0 16px;
        background-color: white;
        min-height: 42px !important;

        .ng-value-container {
          padding-top: 0;

          .ng-placeholder {
            color: var(--text-grey);
            position: relative;

          }

          .ng-value {
            font-size: 14px;
            color: var(--navy-blue);
          }
        }
      }

      .mat-radio-group {
        display: flex;
        flex-direction: row;
        gap: 32px;
        padding-top: 16px;

        .mat-radio-button {
          position: relative;

          .mat-radio-container {
            width: 24px;
            height: 24px;
            margin-left: 8px;

            .mat-radio-outer-circle {
              border-color: var(--border-color);
              border-width: 2px;
              width: 24px;
              height: 24px;
            }

            .mat-radio-inner-circle {
              width: 24px;
              height: 24px;
              transform: scale(0.5);
              background-color: var(--navy-blue);
            }

            .mat-radio-ripple {
              display: none;
            }
          }

          .mat-radio-label-content {
            color: var(--text-grey);
            font-size: 16px;
            font-weight: 500;
          }

          &.mat-radio-checked {
            .mat-radio-outer-circle {
              border-color: var(--navy-blue);
              border-width: 2px;
            }

            .mat-radio-inner-circle {
              background-color: var(--navy-blue);
              transform: scale(0.5);
            }

            .mat-radio-label-content {
              color: var(--navy-blue);
            }
          }
        }
      }
    }
  }
}

// Status Badge Styling
.status-badge-container {
  .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid transparent;

    // &.status-1 { // Draft
    //   background: #fff3cd;
    //   color: #856404;
    //   border-color: #ffeaa7;
    // }

    // &.status-2 { // Pending
    //   background: #d1ecf1;
    //   color: #0c5460;
    //   border-color: #bee5eb;
    // }

    // &.status-3 { // Approved
    //   background: #d4edda;
    //   color: #155724;
    //   border-color: #c3e6cb;
    // }

    // &.status-4 { // Waiting for confirmation
    //   background: #ffeaa7;
    //   color: #b8860b;
    //   border-color: #ffd93d;
    // }

    // &.status-5 { // Confirmed
    //   background: #d4edda;
    //   color: #155724;
    //   border-color: #c3e6cb;
    // }

    // &.status-6 { // Not approved
    //   background: #f8d7da;
    //   color: #721c24;
    //   border-color: #f5c6cb;
    // }

    // &.status-7 { // Rejected
    //   background: #f8d7da;
    //   color: #721c24;
    //   border-color: #f5c6cb;
    // }

    // &.status-8 { // Voting in progress
    //   background: #e2e3e5;
    //   color: #383d41;
    //   border-color: #d6d8db;
    // }

    // &.status-9 { // Completing data
    //   background: #d1ecf1;
    //   color: #0c5460;
    //   border-color: #bee5eb;
    // }


    &.status-1 { // Draft
      background: rgba(117, 85, 172, 0.1);
      color: #7555ac;
    }

    &.status-2 { // Pending
      background: #fff3cd;
      color: #856404;
    }

    &.status-3 { // Approved
      background: #f1faf1;
      color: #0e700e;
    }

    &.status-4 { // Waiting for confirmation
      background: rgba(226, 180, 138, 0.34);
      color: #d16440;
    }

    &.status-5 { // Confirmed
      background: rgba(97, 253, 97, 0.14);
      color: #27ae60;
    }

    &.status-6 { // Not approved
      background: rgba(197, 15, 31, 0.1);
      color: #c50f1f;
    }

    &.status-7 { // Rejected
      color: #828282;
      background: #eaeef1;
    }

    &.status-8 { // Voting in progress
      background: rgba(47, 128, 237, 0.1);
      color: #2f80ed;
    }


    &.status-9 { // Completing data
      background: rgba(157, 112, 9, 0.27);
      color: #9d7009;
    }


    &.status-default {
      background: #f8f9fa;
      color: #6c757d;
      border-color: #dee2e6;
    }
  }
}

.header label {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #00205a !important;
  margin-bottom: 10px !important;
}

::ng-deep{
  .ng-select-container .ng-value-container{
    padding-top: 0px !important;
  }
}
