import { Component, Inject, OnInit, inject } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CustomButtonComponent } from '../../../../shared/components/custom-button/custom-button.component';
import { IconEnum } from '@core/enums/icon-enum';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { LanguageService } from '@core/gl-services/language-services/language.service';
import {
  AuthenticationServiceProxy,
  IJwtAuthResponseBaseResponse,
  SignInCommand,
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { AuthService } from '../../services/auth-service/auth.service';
import { TokenService } from '../../services/token.service';
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    CustomButtonComponent,
  ],
})
export class LoginComponent {
  loginForm: FormGroup;
  showPassword = false;
  currentLang:any;
  lang:any;



  iconName = IconEnum.arrowRight;

  constructor(
    private formBuilder: FormBuilder,
    private apiClient: AuthenticationServiceProxy,
    private router: Router,
    private languageService: LanguageService,
    private errorModalService: ErrorModalService,
    private authService: AuthService,
    private tokenService: TokenService,
    private translateService: TranslateService
  ) {
    this.loginForm = this.formBuilder.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required]],
    });
     this.lang = JSON.parse(localStorage.getItem('lang') || '""');
    this.currentLang = this.lang === 'en' ? LanguageEnum.en : LanguageEnum.ar;
  }


  changeLanguage(): void {
    this.currentLang =
      this.currentLang === LanguageEnum.en ? LanguageEnum.ar : LanguageEnum.en;
      this.languageService.switchLang(this.currentLang,true);
  }

  selectLanguage(lang: string, event: Event): void {
    event.preventDefault();
    const newLang = lang === 'en' ? LanguageEnum.en : LanguageEnum.ar;
    this.currentLang = newLang;
    this.languageService.switchLang(this.currentLang, true);
  }

  getCurrentLanguageFlag(): string {
    return this.currentLang === LanguageEnum.en ? 'assets/images/en.png' : 'assets/images/ar.png';
  }

  getCurrentLanguageText(): string {
    return this.currentLang === LanguageEnum.en ? 'English' : 'عربي';
  }

  onSubmit(): void {
    debugger
    if (this.loginForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.loginForm.controls).forEach((key) => {
        const control = this.loginForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    const signInCommand = new SignInCommand({
      userName: this.loginForm.get('username')?.value,
      password: this.loginForm.get('password')?.value,
    });

    this.apiClient.signIn(signInCommand).subscribe({
      next: (response: IJwtAuthResponseBaseResponse) => {
        if (response.successed && response.data) {
          this.authService.login({
            accessToken: response.data.accessToken!,
            refreshToken: response.data.refreshToken!,
          });
         this.tokenService.setToken(response.data.accessToken!);


          if (response?.data?.userId != null) {
            localStorage.setItem('userId', response.data.userId.toString());
          }
          if (response.data?.isFirstLogin) {
            this.router.navigate(['/auth/change-password']);
          } else {
            this.errorModalService.showSuccess(
              this.translateService.instant('LOGIN_PAGE.LOGIN_SUCCESS')
            );
            this.router.navigate(['/admin/dashboard']);
          }

        } else {
          this.errorModalService.showError(
            this.translateService.instant('LOGIN_PAGE.LOGIN_FAILED')
          );
        }
      },
      error: (error) => {
        console.error('Login error:', error);
        // Error handling is done by the error interceptor
      }
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }
}
