# User Management Components

This directory contains all components related to user management functionality.

## Component Structure

### 1. View User Profile (`view-user-profile/`)
**Purpose**: Display user profile details in read-only mode
**Route**: `/admin/user-management/details/:id`
**Features**:
- View user information as labels (not input fields)
- Download CV functionality
- Professional display layout
- Back navigation to user management

### 2. Update User Profile (`update-user-profile/`)
**Purpose**: Allow current user to edit their own profile
**Route**: `/admin/user-management/profile`
**Features**:
- Editable form fields with validation
- File upload for CV and personal photo
- Change password link
- Save/Cancel functionality

### 3. Edit User (`edit-user/`)
**Purpose**: Admin functionality to edit any user's details
**Route**: `/admin/user-management/edit/:id`
**Features**:
- Admin-level user editing
- Role management
- Status management

### 4. User Profile (Legacy - `user-profile/`)
**Status**: Legacy component that previously handled both view and edit modes
**Note**: This component has been separated into dedicated view and update components

## Routing Configuration

```typescript
// View user profile details (read-only)
{
  path: 'details/:id',
  loadComponent: () => import('./components/view-user-profile/view-user-profile.component')
}

// Update current user profile (editable)
{
  path: 'profile',
  loadComponent: () => import('./components/update-user-profile/update-user-profile.component')
}

// Admin edit user
{
  path: 'edit/:id',
  loadComponent: () => import('./components/edit-user/edit-user.component')
}
```

## Component Separation Benefits

### 1. **Clear Separation of Concerns**
- **View Component**: Focused solely on displaying user information
- **Update Component**: Focused solely on editing current user's profile

### 2. **Better User Experience**
- **View Mode**: Clean, professional display without form controls
- **Edit Mode**: Full form functionality with validation and file uploads

### 3. **Improved Maintainability**
- Smaller, focused components are easier to maintain
- Separate styling and logic for each use case
- Reduced complexity in each component

### 4. **Enhanced Security**
- View component has no edit capabilities
- Update component only allows current user to edit their own profile
- Clear separation between viewing others' profiles and editing own profile

## Usage Examples

### Viewing a User Profile
```typescript
// Navigate to view user profile
this.router.navigate(['/admin/user-management/details', userId]);
```

### Updating Current User Profile
```typescript
// Navigate to update own profile
this.router.navigate(['/admin/user-management/profile']);
```

## Translation Keys

Both components share the same translation namespace `USER_PROFILE` but use different keys:

- `USER_PROFILE.VIEW_TITLE` - For view component page title
- `USER_PROFILE.PAGE_TITLE` - For update component page title

## Styling

Each component has its own SCSS file with:
- Component-specific styles
- RTL support for Arabic language
- Responsive design for mobile devices
- Consistent design system integration

## API Integration

Both components use the same API endpoints but with different parameters:

- **View Component**: `getUserProfile(userId)` - Gets specific user's profile
- **Update Component**: `getUserProfile(undefined)` - Gets current user's profile
- **Update Component**: `updateUserProfile(formData)` - Updates current user's profile
