// shared/services/date-conversion.service.ts
import { Injectable } from '@angular/core';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import momentHijri from 'moment-hijri';

@Injectable({
  providedIn: 'root'
})
export class DateConversionService {

  constructor() {}

   convertGregorianToHijri(gregorian: NgbDateStruct): NgbDateStruct {
    const formattedDate = `${gregorian.year}-${String(gregorian.month).padStart(
      2,
      '0'
    )}-${String(gregorian.day).padStart(2, '0')}`;
    const hijriDate = momentHijri(formattedDate, 'YYYY-MM-DD').format(
      'iYYYY-iM-iD'
    );
    const [year, month, day] = hijriDate.split('-').map(Number);
    return { year, month, day };
  }
   mapStringToSelectedDate(dateString: string):NgbDateStruct| undefined {
    const [day, month, year] = dateString.split('-').map(Number);
    if (isNaN(day) || isNaN(month) || isNaN(year)) {
      return undefined; // Return undefined if the date format is invalid
    }
    return { day, month, year };
  }
}
