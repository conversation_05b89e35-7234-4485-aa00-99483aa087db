import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { dateRangeValidator } from '@shared/services/date-validation';
import { CustomButtonComponent } from '../../../../../shared/components/custom-button/custom-button.component';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { StrategyService } from '@core/services/strategy.service';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { DateConversionService } from '@shared/services/date.service';

@Component({
  selector: 'app-filter-dialog',
  templateUrl: './filter-dialog.component.html',
  styleUrls: ['./filter-dialog.component.scss'],
  standalone: true,
  imports: [FormBuilderComponent, CustomButtonComponent, TranslateModule],
})
export class FilterDialogComponent {
  IconEnum = IconEnum;
  buttonEnum = ButtonTypeEnum;

  formControls: IControlOption[] = [
    {
      type: InputType.Text,
      formControlName: 'search',
      id: 'search',
      name: 'search',
      label: 'INVESTMENT_FUNDS.FORM.FUND_NAME',
      placeholder: 'INVESTMENT_FUNDS.FORM.FUND_NAME_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-12',
    },
    {
      type: InputType.Date,
      formControlName: 'creationDateFrom',
      id: 'creationDateFrom',
      name: 'creationDateFrom',
      label: 'INVESTMENT_FUNDS.FORM.CREATION_DATE_FROM',
      placeholder: 'INVESTMENT_FUNDS.FORM.CREATION_DATE_PLACEHOLDER',
      class: 'col-md-12',
    },
    {
      type: InputType.Date,
      formControlName: 'creationDateTo',
      id: 'creationDateTo',
      name: 'creationDateTo',
      label: 'INVESTMENT_FUNDS.FORM.CREATION_DATE_TO',
      placeholder: 'INVESTMENT_FUNDS.FORM.CREATION_DATE_PLACEHOLDER',
      class: 'col-md-12',
    },
    {
      type: InputType.Dropdown,
      formControlName: 'strategyId',
      id: 'strategyId',
      name: 'strategyId',
      label: 'INVESTMENT_FUNDS.FORM.STRATEGY',
      placeholder: 'INVESTMENT_FUNDS.FORM.STRATEGY_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-12',
      options: [],
    },
  ];

  formGroup!: FormGroup;
  isFormSubmitted!: boolean;

  constructor(
    public dialogRef: MatDialogRef<FilterDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private strategyService: StrategyService,
    private formBuilder: FormBuilder,
    private DateConversionService: DateConversionService
  ) {
    this.getStrategyList();
    this.setDateMaxAndMin();
    this.initForm();
  }
  getStrategyList() {
    this.strategyService.strategyList(0, 0, '', '').subscribe((res: any) => {
      const field = this.formControls.find(
        (f) => f.formControlName === 'strategyId'
      );
      if (field) {
        field.options = res.data.map((user: any) => ({
          id: user.id,
          name: user.nameAr,
        }));
      }
    });
  }
  applyFilters() {
    this.isFormSubmitted = true;
    if (this.formGroup.valid) this.dialogRef.close(this.formGroup.value);
  }
  resetFilters() {
    this.formGroup.reset();
    this.applyFilters();
  }
  initForm() {
    const formGroup: any = {};
    this.formControls.forEach((control) => {
      const validators = [];
      if (control.formControlName == '') return;
      if (control.max) {
        validators.push(Validators.max(control.max));
      }
      if (control.min) {
        validators.push(Validators.min(control.min));
      }
      formGroup[control.formControlName] = [null, validators];
    });

    this.formGroup = this.formBuilder.group(formGroup);
    if (this.data) this.formGroup.setValue(this.data);
  }

  closeDialog() {
    this.dialogRef.close();
  }
  dateSelected(event: { event: any; control: IControlOption }) {
    const controlName = event.control.formControlName;
    const selectedDate: NgbDateStruct = event.event?.gregorian;

    if (!selectedDate) return;

    const otherControlName =
      controlName === 'creationDateFrom'
        ? 'creationDateTo'
        : 'creationDateFrom';
    const otherDate = this.formGroup.get(otherControlName)
      ?.value as NgbDateStruct;

    const selected = new Date(
      selectedDate.year,
      selectedDate.month - 1,
      selectedDate.day
    );
    let compareToValid = true;

    if (otherDate) {
      const other = new Date(
        otherDate.year,
        otherDate.month - 1,
        otherDate.day
      );
      compareToValid =
        controlName === 'creationDateFrom'
          ? selected <= other
          : selected >= other;
    }

    if (compareToValid) {
      this.formGroup.get(controlName)?.setValue(selectedDate);
    } else {
      this.formGroup.get(controlName)?.setErrors({ dateRangeInvalid: true });
    }
  }
  maxGreg?: NgbDateStruct;
  maxHijri?: NgbDateStruct;
  setDateMaxAndMin() {
    const today = new Date();
    const minGreg: NgbDateStruct = { year: 2010, month: 1, day: 1 };
    const minHijri: NgbDateStruct =
      this.DateConversionService.convertGregorianToHijri(minGreg);

    this.maxGreg = {
      year: today.getFullYear(),
      month: today.getMonth() + 1,
      day: today.getDate(),
    };

    this.maxHijri = this.DateConversionService.convertGregorianToHijri(
      this.maxGreg
    );
    const field = this.formControls.find(
      (f) => f.formControlName === 'creationDateFrom'
    );
    if (field) {
      field.maxGreg = this.maxGreg;
      field.maxHijri = this.maxHijri;
      field.minHijri = minHijri;
      field.minGreg = minGreg;
    }
  }
}
