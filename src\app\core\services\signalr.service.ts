import { Injectable } from '@angular/core';
import * as signalR from '@microsoft/signalr';
import { environment } from '../../../environments/environment';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SignalRService {
  private hubConnection: signalR.HubConnection;
  private notificationSubject = new BehaviorSubject<any>(null);
  public notification$ = this.notificationSubject.asObservable();

  constructor() {
    this.hubConnection = new signalR.HubConnectionBuilder()
      .withUrl(`${environment.apiUrl}/notificationHub`)  // Update this URL to match your backend hub URL
      .withAutomaticReconnect()
      .build();

    this.start();
  }

  private async start() {
    try {
      await this.hubConnection.start();
      console.log('SignalR Connected');
      this.registerHandlers();
    } catch (err) {
      console.error('Error while starting SignalR connection:', err);
      setTimeout(() => this.start(), 5000);
    }
  }

  private registerHandlers() {
    this.hubConnection.on('ReceiveNotification', (notification: any) => {
      this.notificationSubject.next(notification);
    });

    // Add more handlers as needed
    this.hubConnection.on('ReceiveMessage', (message: any) => {
      console.log('Received message:', message);
    });
  }

  public async sendMessage(message: any) {
    try {
      await this.hubConnection.invoke('SendMessage', message);
    } catch (err) {
      console.error('Error while sending message:', err);
    }
  }

  public disconnect() {
    if (this.hubConnection) {
      this.hubConnection.stop();
    }
  }
} 