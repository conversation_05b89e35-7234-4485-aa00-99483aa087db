# Multi-Role Selection Logic Implementation

## Overview

This document describes the implementation of multi-role selection logic for the user management component, specifically for the `create-user.component.ts` file.

## Business Requirements

The system should enable multiple role selection **ONLY** under these specific conditions:

1. The selected roles are exactly **'Fund Manager' AND 'Board Member'** (both selected together)
2. OR the selected roles are exactly **'Associate Fund Manager' AND 'Board Member'** (both selected together)

In all other cases, the system restricts users to selecting only a single role at a time.

## Implementation Details

### Core Method: `shouldEnableMultiSelect()`

The `shouldEnableMultiSelect()` method is the main logic controller that:

- **Input**: Accepts an array of selected roles as strings
- **Output**: Returns `true` only when one of the two valid role combinations is selected
- **Validation**: Returns `false` for any other combination, including single role selections or invalid multi-role combinations

```typescript
private shouldEnableMultiSelect(selectedRoles: string[]): boolean {
  // Returns true only for valid 2-role combinations:
  // 1. Fund Manager + Board Member
  // 2. Associate Fund Manager + Board Member
}
```

### Supporting Method: `normalizeRoleName()`

The `normalizeRoleName()` method handles:

- **Case Insensitivity**: Converts all role names to lowercase for comparison
- **Format Variations**: Handles different role name formats:
  - Spaces: "Fund Manager" → "fundmanager"
  - Underscores: "fund_manager" → "fundmanager"  
  - No spaces: "fundmanager" → "fundmanager"
- **Exact String Matching**: Ensures precise role identification

## Valid Role Combinations

| Combination | Fund Manager | Associate Fund Manager | Board Member | Multi-Select Enabled |
|-------------|--------------|------------------------|--------------|---------------------|
| 1           | ✅           | ❌                     | ✅           | ✅ **TRUE**         |
| 2           | ❌           | ✅                     | ✅           | ✅ **TRUE**         |
| Any Other   | Various      | Various                | Various      | ❌ **FALSE**        |

## Test Cases Covered

### ✅ Valid Scenarios (Multi-Select Enabled)
- Fund Manager + Board Member
- Associate Fund Manager + Board Member
- Case variations (uppercase, lowercase, mixed)
- Format variations (spaces, underscores, no spaces)

### ❌ Invalid Scenarios (Single-Select Only)
- Empty selection
- Single role selection (any role)
- Invalid multi-role combinations
- More than 2 roles selected
- Unknown/unmapped role names

## Integration Points

The logic integrates with the existing form control system:

1. **Form Control Update**: Updates `maxLength` property dynamically
2. **Role Conflict Checking**: Works with existing role conflict detection
3. **UI Feedback**: Provides immediate feedback to users about selection limits

## Error Handling

- **Null/Undefined Input**: Returns `false` safely
- **Empty Arrays**: Returns `false` 
- **Unknown Roles**: Filtered out during normalization
- **Invalid Combinations**: Automatically restricts to single selection

## Usage Example

```typescript
// Valid combinations - enables multi-select
const validRoles1 = ['Fund Manager', 'Board Member'];
const validRoles2 = ['Associate Fund Manager', 'Board Member'];

// Invalid combinations - restricts to single-select
const invalidRoles1 = ['Fund Manager', 'Legal Council'];
const invalidRoles2 = ['Board Member']; // Single role
const invalidRoles3 = ['Fund Manager', 'Board Member', 'Legal Council']; // Too many roles
```

## Future Considerations

- **Role Name Changes**: Update the `normalizeRoleName()` mapping if role names change
- **New Valid Combinations**: Add to the `validCombinations` array if business rules expand
- **Localization**: Consider internationalization if role names need translation support

## Testing

Comprehensive unit tests are included in `create-user.component.spec.ts` covering:
- All valid and invalid combinations
- Edge cases (null, empty, unknown roles)
- Case sensitivity and format variations
- Integration with the form control system
