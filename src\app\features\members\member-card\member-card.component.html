<div class="member-card">
  <div class="card-header">
      <p class="title">
        {{'INVESTMENT_FUNDS.MEMBERS.MEMBER'| translate}}
        <span *ngIf="member.isChairman"> / {{member.isChairman ? ('INVESTMENT_FUNDS.MEMBERS.CHAIRMAN' | translate):''}}</span>

      </p>
      <div class="card-actions">
        <button class="action-btn"
                *ngIf="canEdit"
                (click)="onEdit()"
                [title]="'COMMON.EDIT' | translate">
                <img src="assets/images/edit.png" alt="edit" />
              </button>

        <button class="action-btn"
                *ngIf="canDelete"
                (click)="onDelete()"
                [title]="'COMMON.DELETE' | translate">
                <img src="assets/images/trash.png"  alt="">
              </button>
      </div>
    </div>

    <div class="member-info">
      <div class="member-avatar">
        <img src="assets/images/avatar-member.png" alt="Member Avatar" class="avatar-img">
      </div>
      <div class="member-details">
        <h4 class="member-name">{{ member.memberName || 'Unknown Member' }}</h4>
        <p class="member-role">{{ roleDisplay | translate }}

          <span *ngIf="member.isChairman"> - {{member.isChairman ? ('INVESTMENT_FUNDS.MEMBERS.CHAIRMAN' | translate):''}}</span>
        </p>
      </div>
    </div>

    <!-- <div class="member-status">
      <span class="status-badge" [ngClass]="{'active': member.isActive, 'inactive': !member.isActive}">
        {{ statusDisplay | translate }}
      </span>
    </div> -->

  <div class="card-body">
    <div class="member-attributes">
      <!-- <div class="attribute-item">
        <span class="attribute-label">{{ 'INVESTMENT_FUNDS.MEMBERS.TYPE_MEMBER' | translate }}:</span>
        <span class="attribute-value">{{ memberTypeDisplay | translate }}</span>
      </div> -->

      <!-- <div class="attribute-item" *ngIf="member.isChairman">
        <span class="attribute-label">{{ 'INVESTMENT_FUNDS.MEMBERS.IS_CHAIRMAN' | translate }}:</span>
        <span class="attribute-value chairman-badge">
          <img src="assets/images/alert.png" alt="Chairman" class="chairman-icon">
          {{ 'INVESTMENT_FUNDS.MEMBERS.CHAIRMAN' | translate }}
        </span>
      </div> -->


      <div class="member-meta">
        <div class="meta-item" *ngIf="tokenService.hasPermission('BoardMember.Create')">
          <span class="meta-label">{{ 'INVESTMENT_FUNDS.MEMBERS.LAST_UPDATED_DATE' | translate }}:</span>
          <p class="gregorian mb-0">{{member.lastUpdateDate?.toJSDate()| date: 'd/M/y'}}</p>
          <p class="hijri">{{formatDateToString(member.lastUpdateDate)| dateHijriConverter}}</p>
        </div>
        <div class="status"  [ngClass]="{'active': member.isActive, 'inactive': !member.isActive}">
          <span class="circle" [ngStyle]="{ 'background-color': member.isActive ? '#27ae60' : '#828282' }"></span>

          <span class="">
            {{ statusDisplay | translate }}
          </span>
          <!-- <span class="status" [ngClass]="getStatusClass(member.isActive)">
            {{ statusDisplay  }}
          </span> -->

        </div>
      </div>
<!--
      <div class="attribute-item" *ngIf="formattedLastUpdateDate">
        <span class="attribute-label">{{ 'INVESTMENT_FUNDS.MEMBERS.LAST_UPDATED' | translate }}:</span>
        <span class="attribute-value">{{ formattedLastUpdateDate }}</span>
      </div> -->
    </div>
  </div>

  <!-- <div class="card-actions" *ngIf="canEdit || canDelete">
    <button
      type="button"
      class="btn btn-outline-primary btn-sm"
      *ngIf="canEdit"
      (click)="onEdit()"
      [title]="'COMMON.EDIT' | translate">
      <img src="assets/images/edit-table-icon.png" alt="Edit" class="action-icon">
      {{ 'COMMON.EDIT' | translate }}
    </button>

    <button
      type="button"
      class="btn btn-outline-danger btn-sm"
      *ngIf="canDelete"
      (click)="onDelete()"
      [title]="'COMMON.DELETE' | translate">
      <img src="assets/images/x.svg" alt="Delete" class="action-icon">
      {{ 'COMMON.DELETE' | translate }}
    </button>
  </div> -->
</div>
