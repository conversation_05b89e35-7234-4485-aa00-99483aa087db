import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface SpinnerState {
  isLoading: boolean;
  activeRequests: number;
}

@Injectable({
  providedIn: 'root'
})
export class SpinnerService {

  private readonly isLoadingSubject = new BehaviorSubject<boolean>(false);
  private readonly stateSubject = new BehaviorSubject<SpinnerState>({
    isLoading: false,
    activeRequests: 0
  });

  public readonly isLoading$ = this.isLoadingSubject.asObservable();
  public readonly state$ = this.stateSubject.asObservable();

  private apiReqs: number = 0;
  private readonly enableLogging = false; // Set to true for debugging

  constructor() {
    if (this.enableLogging) {
      console.log('SpinnerService initialized');
    }
  }

  /**
   * Shows the loading spinner and increments the request counter
   */
  show(): void {
    if (this.enableLogging) {
      console.log(`SpinnerService.show() called. Current requests: ${this.apiReqs}`);
    }

    if (this.apiReqs === 0) {
      this.isLoadingSubject.next(true);
      if (this.enableLogging) {
        console.log('Spinner shown');
      }
    }

    this.apiReqs = Math.max(0, this.apiReqs + 1);
    this.updateState();
  }

  /**
   * Hides the loading spinner when all requests are complete
   */
  hide(): void {
    if (this.enableLogging) {
      console.log(`SpinnerService.hide() called. Current requests: ${this.apiReqs}`);
    }

    this.apiReqs = Math.max(0, this.apiReqs - 1);

    if (this.apiReqs === 0) {
      this.isLoadingSubject.next(false);
      if (this.enableLogging) {
        console.log('Spinner hidden');
      }
    }

    this.updateState();
  }

  /**
   * Force hide the spinner and reset counter (use with caution)
   */
  forceHide(): void {
    if (this.enableLogging) {
      console.log('SpinnerService.forceHide() called - resetting state');
    }

    this.apiReqs = 0;
    this.isLoadingSubject.next(false);
    this.updateState();
  }

  /**
   * Get current loading state
   */
  get isLoading(): boolean {
    return this.isLoadingSubject.value;
  }

  /**
   * Get current active requests count
   */
  get activeRequestsCount(): number {
    return this.apiReqs;
  }

  /**
   * Get current state for debugging
   */
  getDebugInfo(): SpinnerState {
    return {
      isLoading: this.isLoading,
      activeRequests: this.activeRequestsCount
    };
  }

  private updateState(): void {
    this.stateSubject.next({
      isLoading: this.isLoadingSubject.value,
      activeRequests: this.apiReqs
    });
  }
}
