import { Component, EventEmitter, Input, Output, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs/operators';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { TranslateModule } from '@ngx-translate/core';

interface Breadcrumb {
  label: string;
  url: string;
}

@Component({
  selector: 'app-breadcrumb',
  standalone: true,
  imports: [CommonModule,TranslateModule],
  templateUrl: './breadcrumb.component.html',
  styleUrl: './breadcrumb.component.scss',

})
export class BreadcrumbComponent {
  @Input() breadcrumbs: IBreadcrumbItem[] | undefined;
  @Input() size: SizeEnum | undefined;
  @Input() divider: string | undefined;
  @Output() onClickEvent=new EventEmitter<IBreadcrumbItem>();

  getBreadcrumbSize() {
    return {
      'breadcrumb-medium': this.size === SizeEnum.Medium,
      'breadcrumb-small': this.size === SizeEnum.Small,
      'breadcrumb-large': this.size === SizeEnum.Large,
    };
  }

  onBreadcrumbClick(breadcrumb: IBreadcrumbItem): void {
    if (!breadcrumb.disabled) {
      this.onClickEvent.emit(breadcrumb);
    }
  }
}
