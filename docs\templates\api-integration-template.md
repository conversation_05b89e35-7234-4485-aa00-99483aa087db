# 🔌 API Integration Template
## [API_INTEGRATION_NAME] - Backend Service Integration

> **[Brief description of the API integration and its purpose within the Jadwa Investment Web Application]**

---

## 🌐 API Integration Overview

### 📊 Integration Summary
| Field | Value |
|-------|-------|
| **Integration ID** | API-[YYYY]-[###] |
| **Integration Name** | [Descriptive name] |
| **API Controller** | [Controller name] |
| **Service Domain** | [Authentication/Funds/Strategies/Users/<USER>/Authorization] |
| **Priority** | [High/Medium/Low] |
| **Complexity** | [Simple/Medium/Complex] |
| **Estimated Effort** | [Hours/Days] |
| **Dependencies** | [Related services/endpoints] |

### 🎯 Integration Purpose
- **Business Objective**: [What business need does this integration serve?]
- **Data Flow**: [Describe the data flow between frontend and backend]
- **User Impact**: [How does this integration affect user experience?]
- **System Impact**: [How does this integration affect system architecture?]

---

## 🔄 API Integration Architecture

The following diagram illustrates the comprehensive API integration flow and service layer architecture used in the Jadwa web application:
 

```mermaid
graph TD
    subgraph "Frontend Layer"
        COMP[Angular Component]
        SERV[Custom Service]
        GUARD[Route Guard]
    end

    subgraph "Service Layer"
        PROXY[NSwag Service Proxy]
        INT[HTTP Interceptors]
        CACHE[Cache Service]
        TRANS[Transform Service]
        VALID[Validation Service]
    end

    subgraph "HTTP Layer"
        TOKEN[Token Interceptor]
        ERROR[Error Interceptor]
        RETRY[Retry Logic]
        TIMEOUT[Timeout Handler]
    end

    subgraph "External API"
        SWAGGER[Swagger Endpoint]
        AUTH[JWT Auth Server]
        API[REST API Endpoints]
    end

    subgraph "Data Flow"
        REQ[HTTP Request]
        RESP[HTTP Response]
        ERR[Error Response]
    end

    COMP --> SERV
    SERV --> PROXY
    GUARD --> TOKEN

    PROXY --> INT
    INT --> TOKEN
    INT --> ERROR
    INT --> RETRY
    INT --> TIMEOUT

    SERV --> CACHE
    SERV --> TRANS
    SERV --> VALID

    TOKEN --> REQ
    REQ --> API
    API --> RESP
    API --> ERR

    RESP --> TRANS
    ERR --> ERROR

    TRANS --> CACHE
    ERROR --> COMP
    CACHE --> COMP

    TOKEN --> AUTH
    PROXY --> SWAGGER

    style COMP fill:#e3f2fd
    style SERV fill:#e8f5e8
    style PROXY fill:#f3e5f5
    style API fill:#ffcdd2
    style ERR fill:#ffebee
```
 
### 📊 API Request/Response Lifecycle

The sequence diagram below shows the complete lifecycle of an API request, including authentication, error handling, and retry logic:

```mermaid
sequenceDiagram
    participant C as Component
    participant S as Custom Service
    participant P as Service Proxy
    participant I as Interceptors
    participant A as API Server
    participant E as Error Handler

    Note over C,E: API Request Lifecycle

    C->>S: Call service method
    S->>S: Validate input data
    S->>P: Call proxy method
    P->>I: HTTP request

    Note over I: Token Interceptor
    I->>I: Add JWT token
    I->>I: Add headers

    I->>A: HTTP request with auth

    alt Successful Response
        A->>I: 200 OK + data
        I->>P: Response data
        P->>S: Typed response
        S->>S: Transform data
        S->>C: Observable result
        C->>C: Update UI
    else Authentication Error
        A->>I: 401 Unauthorized
        I->>I: Token refresh attempt
        alt Token Refresh Success
            I->>A: Retry original request
            A->>I: 200 OK + data
            I->>P: Response data
            P->>S: Typed response
            S->>C: Observable result
        else Token Refresh Failed
            I->>E: Authentication error
            E->>C: Redirect to login
        end
    else Validation Error
        A->>I: 422 Unprocessable
        I->>E: Validation error
        E->>C: Show validation messages
    else Server Error
        A->>I: 500 Internal Error
        I->>E: Server error
        E->>C: Show error message
        E->>E: Log error details
    else Network Error
        I->>I: Network timeout/failure
        I->>I: Retry logic (3 attempts)
        alt Retry Success
            I->>A: Retry request
            A->>I: Response
            I->>P: Response data
            P->>S: Typed response
            S->>C: Observable result
        else Retry Failed
            I->>E: Network error
            E->>C: Show network error
        end
    end

    Note over C,E: Error Handling & Recovery
```

---

## 📋 Swagger API Specification

### 🌍 Base Configuration
**API Base URL**: `http://************:44301/swagger/v2/swagger.json`

**Environment Configuration**
```typescript
// Environment-specific API configuration
export const environment = {
  production: false,
  apiUrl: 'http://************:44301', // Base API URL
  swaggerUrl: 'http://************:44301/swagger/v2/swagger.json'
};
```

### 🔗 Endpoint Specifications

**Endpoint 1: [Primary Endpoint Name]**
- **HTTP Method**: `[GET/POST/PUT/DELETE]`
- **Path**: `/api/[Controller]/[Action]`
- **Tags**: `["[Controller]"]`
- **Authentication**: Required/Optional
- **Description**: [Detailed description of endpoint functionality]

**Request Parameters:**
| Parameter | Type | Location | Required | Description |
|-----------|------|----------|----------|-------------|
| `[param1]` | `[type]` | `[query/path/body]` | `[Yes/No]` | `[Description]` |
| `[param2]` | `[type]` | `[query/path/body]` | `[Yes/No]` | `[Description]` |

**Request Body Schema:**
```typescript
export interface [RequestModelName] {
  [property1]: [type];
  [property2]?: [type]; // Optional property
  // Add all required properties from Swagger schema
}
```

**Response Schema:**
```typescript
export interface [ResponseModelName] {
  statusCode: HttpStatusCode;
  successed: boolean;
  message: string;
  data: [DataType];
  errors: any[];
}

// Paginated response for list endpoints
export interface [DataType]PaginatedResult {
  statusCode: HttpStatusCode;
  successed: boolean;
  message: string;
  data: [DataType][];
  errors: any[];
  currentPage: number;
  totalCount: number;
  totalPages: number;
  pageSize: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}
```

**Response Status Codes:**
- `200 OK`: Successful operation
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `422 Unprocessable Content`: Validation errors
- `500 Internal Server Error`: Server error

---

## ⚙️ NSwag Client Generation

### 🤖 Auto-Generated Service Proxy

**Service Proxy Interface**
```typescript
// Auto-generated by NSwag from Swagger specification
@Injectable({ providedIn: 'root' })
export class [Controller]ServiceProxy {
  private http: HttpClient;
  private baseUrl: string;
  protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

  constructor(
    @Inject(HttpClient) http: HttpClient,
    @Optional() @Inject(API_BASE_URL) baseUrl?: string
  ) {
    this.http = http;
    this.baseUrl = baseUrl !== undefined && baseUrl !== null ? baseUrl : "";
  }

  // Auto-generated methods based on Swagger endpoints
  [methodName]([parameters]): Observable<[ResponseType]> {
    let url_ = this.baseUrl + "/api/[Controller]/[Action]";
    url_ = url_.replace(/[?&]$/, "");

    const options_: any = {
      observe: "response",
      responseType: "blob",
      headers: new HttpHeaders({
        "Accept": "application/json"
      })
    };

    return this.http.request("[method]", url_, options_).pipe(
      mergeMap((response_: any) => {
        return this.process[MethodName](response_);
      })
    ).pipe(
      catchError((response_: any) => {
        if (response_ instanceof HttpResponseBase) {
          try {
            return this.process[MethodName](response_ as any);
          } catch (e) {
            return throwError(e) as any as Observable<[ResponseType]>;
          }
        } else {
          return throwError(response_) as any as Observable<[ResponseType]>;
        }
      })
    );
  }
}
```

### 🔧 Service Proxy Configuration

**API Module Configuration**
```typescript
// app.config.ts or api.module.ts
import {
  API_BASE_URL,
  [Controller]ServiceProxy
} from './core/api/api.generated';

export const appConfig: ApplicationConfig = {
  providers: [
    // API Base URL configuration
    { provide: API_BASE_URL, useValue: environment.apiUrl },
    
    // Auto-generated service proxies
    [Controller]ServiceProxy,
    
    // Other providers...
  ]
};
```

---

## 🏗️ Custom Service Layer Implementation

### 📦 Business Logic Service

**Custom Service Wrapper**
```typescript
@Injectable({ providedIn: 'root' })
export class [ServiceName]Service {
  constructor(
    private http: HttpClient,
    private [controller]Proxy: [Controller]ServiceProxy,
    private errorModalService: ErrorModalService
  ) {}

  // CRUD Operations with business logic
  getList(
    pageNumber: number = 1,
    pageSize: number = 10,
    search: string = '',
    orderBy: string = 'Id desc',
    additionalFilters?: [FilterType]
  ): Observable<PaginatedResponse<[DataType]>> {
    return this.[controller]Proxy.list(
      pageNumber,
      pageSize,
      search,
      orderBy,
      // Add additional parameters as needed
    ).pipe(
      map(response => ({
        ...response,
        data: response.data || []
      })),
      catchError(this.handleError.bind(this)),
      shareReplay(1) // Cache the result
    );
  }

  getById(id: number): Observable<[DataType]> {
    return this.[controller]Proxy.getById(id).pipe(
      map(response => response.data),
      catchError(this.handleError.bind(this))
    );
  }

  create(data: Create[EntityName]Command): Observable<BaseResponse> {
    return this.[controller]Proxy.create(data).pipe(
      tap(response => {
        if (response.successed) {
          this.errorModalService.showSuccess(
            'Successfully created [entity name]'
          );
        }
      }),
      catchError(this.handleError.bind(this))
    );
  }

  update(data: Update[EntityName]Command): Observable<BaseResponse> {
    return this.[controller]Proxy.update(data).pipe(
      tap(response => {
        if (response.successed) {
          this.errorModalService.showSuccess(
            'Successfully updated [entity name]'
          );
        }
      }),
      catchError(this.handleError.bind(this))
    );
  }

  delete(id: number): Observable<BaseResponse> {
    return this.[controller]Proxy.delete(id).pipe(
      tap(response => {
        if (response.successed) {
          this.errorModalService.showSuccess(
            'Successfully deleted [entity name]'
          );
        }
      }),
      catchError(this.handleError.bind(this))
    );
  }

  // Custom business logic methods
  [customMethod]([parameters]): Observable<[ReturnType]> {
    // Implement custom business logic
    return this.[controller]Proxy.[customEndpoint]([parameters]).pipe(
      map(response => this.transformData(response)),
      catchError(this.handleError.bind(this))
    );
  }

  // Private helper methods
  private transformData(response: any): [ReturnType] {
    // Transform API response to match frontend needs
    return response.data;
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    // Error handling is primarily managed by global interceptor
    // Additional service-specific error handling can be added here
    console.error(`[ServiceName]Service error:`, error);
    return throwError(() => error);
  }
}
```

### 🔄 Observable Patterns and State Management

**Service with State Management**
```typescript
@Injectable({ providedIn: 'root' })
export class [ServiceName]StateService {
  // BehaviorSubject for state management
  private [entityName]ListSubject = new BehaviorSubject<[DataType][]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private selectedItemSubject = new BehaviorSubject<[DataType] | null>(null);

  // Public observables
  public [entityName]List$ = this.[entityName]ListSubject.asObservable();
  public loading$ = this.loadingSubject.asObservable();
  public selectedItem$ = this.selectedItemSubject.asObservable();

  constructor(private [serviceName]Service: [ServiceName]Service) {}

  // State management methods
  loadList(filters?: [FilterType]): void {
    this.loadingSubject.next(true);
    
    this.[serviceName]Service.getList(
      1, 10, '', 'Id desc', filters
    ).subscribe({
      next: (response) => {
        this.[entityName]ListSubject.next(response.data);
        this.loadingSubject.next(false);
      },
      error: (error) => {
        this.loadingSubject.next(false);
        // Error handled by interceptor
      }
    });
  }

  selectItem(item: [DataType] | null): void {
    this.selectedItemSubject.next(item);
  }

  refreshList(): void {
    this.loadList();
  }

  // Utility methods
  getCurrentList(): [DataType][] {
    return this.[entityName]ListSubject.value;
  }

  isLoading(): boolean {
    return this.loadingSubject.value;
  }
}
```

---

## 🔐 Authentication and Security

### 🎫 JWT Token Integration

**Token Interceptor Implementation**
```typescript
export const tokenInterceptor: HttpInterceptorFn = (req, next) => {
  const token = localStorage.getItem('auth_token');
  const lang = localStorage.getItem('lang') || 'ar';

  if (token && !req.url.includes('/auth/')) {
    req = req.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`,
        'Accept-Language': lang === 'en' ? 'en-US' : 'ar-EG',
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json'
      }
    });
  }

  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      if (error.status === 401) {
        // Handle unauthorized access
        const authService = inject(AuthService);
        authService.logout();
      }
      return throwError(() => error);
    })
  );
};
```

### 🛡️ Permission-Based Access

**Service with Permission Checks**
```typescript
@Injectable({ providedIn: 'root' })
export class Secured[ServiceName]Service {
  constructor(
    private [serviceName]Service: [ServiceName]Service,
    private tokenService: TokenService
  ) {}

  // Permission-aware methods
  canCreate(): boolean {
    return this.tokenService.hasPermission('[ENTITY_NAME].CREATE');
  }

  canEdit(): boolean {
    return this.tokenService.hasPermission('[ENTITY_NAME].EDIT');
  }

  canDelete(): boolean {
    return this.tokenService.hasPermission('[ENTITY_NAME].DELETE');
  }

  canView(): boolean {
    return this.tokenService.hasPermission('[ENTITY_NAME].VIEW');
  }

  // Secured operations
  secureCreate(data: Create[EntityName]Command): Observable<BaseResponse> {
    if (!this.canCreate()) {
      return throwError(() => new Error('Insufficient permissions'));
    }
    return this.[serviceName]Service.create(data);
  }

  secureUpdate(data: Update[EntityName]Command): Observable<BaseResponse> {
    if (!this.canEdit()) {
      return throwError(() => new Error('Insufficient permissions'));
    }
    return this.[serviceName]Service.update(data);
  }

  secureDelete(id: number): Observable<BaseResponse> {
    if (!this.canDelete()) {
      return throwError(() => new Error('Insufficient permissions'));
    }
    return this.[serviceName]Service.delete(id);
  }
}
```

---

## ⚠️ Error Handling Strategy

### 🚨 Global Error Interceptor

**Error Interceptor Implementation**
```typescript
export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const errorModalService = inject(ErrorModalService);
  
  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      let errorMessage = 'An unexpected error occurred';
      
      // Handle different error types
      switch (error.status) {
        case 400:
          errorMessage = error.error?.message || 'Bad request';
          break;
        case 401:
          errorMessage = 'Authentication required';
          // Don't show error modal for 401, handle in token interceptor
          return throwError(() => error);
        case 403:
          errorMessage = 'Access forbidden';
          break;
        case 404:
          errorMessage = 'Resource not found';
          break;
        case 422:
          errorMessage = this.extractValidationErrors(error.error);
          break;
        case 500:
          errorMessage = 'Internal server error';
          break;
        default:
          errorMessage = error.error?.message || error.message;
      }
      
      // Show error to user
      errorModalService.showError(errorMessage);
      
      return throwError(() => error);
    })
  );
};

function extractValidationErrors(errorResponse: any): string {
  if (errorResponse?.errors) {
    const errors = Object.values(errorResponse.errors).flat();
    return errors.join(', ');
  }
  return errorResponse?.message || 'Validation failed';
}
```

### 🔄 Retry Logic and Resilience

**Service with Retry Logic**
```typescript
@Injectable({ providedIn: 'root' })
export class Resilient[ServiceName]Service {
  constructor(private [serviceName]Service: [ServiceName]Service) {}

  getListWithRetry(
    pageNumber: number,
    pageSize: number,
    search: string = ''
  ): Observable<PaginatedResponse<[DataType]>> {
    return this.[serviceName]Service.getList(pageNumber, pageSize, search).pipe(
      retry({
        count: 3,
        delay: (error, retryCount) => {
          // Exponential backoff: 1s, 2s, 4s
          const delayMs = Math.pow(2, retryCount - 1) * 1000;
          return timer(delayMs);
        },
        resetOnSuccess: true
      }),
      timeout(30000), // 30 second timeout
      catchError((error) => {
        console.error('Failed after retries:', error);
        return throwError(() => error);
      })
    );
  }
}
```

---

## 📊 Data Transformation and Validation

### 🔄 Data Transformation Patterns

**Data Transformation Service**
```typescript
@Injectable({ providedIn: 'root' })
export class [EntityName]TransformService {
  
  // Transform API response to frontend model
  transformFromApi(apiData: [ApiDataType]): [FrontendDataType] {
    return {
      id: apiData.id,
      name: apiData.name,
      displayName: this.formatDisplayName(apiData.name),
      createdAt: this.formatDate(apiData.createdAt),
      updatedAt: apiData.updatedAt ? this.formatDate(apiData.updatedAt) : null,
      // Transform other properties as needed
    };
  }

  // Transform frontend model to API request
  transformToApi(frontendData: [FrontendDataType]): [ApiRequestType] {
    return {
      id: frontendData.id,
      name: frontendData.name.trim(),
      // Transform other properties as needed
    };
  }

  // Transform list data
  transformList(apiList: [ApiDataType][]): [FrontendDataType][] {
    return apiList.map(item => this.transformFromApi(item));
  }

  // Private helper methods
  private formatDate(dateString: string): string {
    // Format date according to locale
    return new Date(dateString).toLocaleDateString();
  }

  private formatDisplayName(name: string): string {
    // Apply business rules for display formatting
    return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
  }
}
```

### ✅ Data Validation

**Validation Service**
```typescript
@Injectable({ providedIn: 'root' })
export class [EntityName]ValidationService {
  
  validateCreateRequest(data: Create[EntityName]Command): ValidationResult {
    const errors: string[] = [];
    
    // Required field validation
    if (!data.name || data.name.trim().length === 0) {
      errors.push('Name is required');
    }
    
    // Business rule validation
    if (data.name && data.name.length > 100) {
      errors.push('Name must be less than 100 characters');
    }
    
    // Custom validation rules
    if (!this.isValidBusinessRule(data)) {
      errors.push('Business rule validation failed');
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  validateUpdateRequest(data: Update[EntityName]Command): ValidationResult {
    const errors: string[] = [];
    
    // ID validation
    if (!data.id || data.id <= 0) {
      errors.push('Valid ID is required for update');
    }
    
    // Reuse create validation
    const createValidation = this.validateCreateRequest(data);
    errors.push(...createValidation.errors);
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  private isValidBusinessRule(data: any): boolean {
    // Implement business-specific validation logic
    return true;
  }
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}
```

---

## 🧪 Testing Strategy

### 🔬 Service Testing

**Service Unit Tests**
```typescript
describe('[ServiceName]Service', () => {
  let service: [ServiceName]Service;
  let httpMock: HttpTestingController;
  let mockProxy: jasmine.SpyObj<[Controller]ServiceProxy>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('[Controller]ServiceProxy', [
      'list', 'getById', 'create', 'update', 'delete'
    ]);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        [ServiceName]Service,
        { provide: [Controller]ServiceProxy, useValue: spy }
      ]
    });

    service = TestBed.inject([ServiceName]Service);
    httpMock = TestBed.inject(HttpTestingController);
    mockProxy = TestBed.inject([Controller]ServiceProxy) as jasmine.SpyObj<[Controller]ServiceProxy>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get list of items', () => {
    const mockResponse = {
      statusCode: 200,
      successed: true,
      message: 'Success',
      data: [{ id: 1, name: 'Test Item' }],
      totalCount: 1
    };

    mockProxy.list.and.returnValue(of(mockResponse));

    service.getList(1, 10, '').subscribe(response => {
      expect(response.data).toEqual(mockResponse.data);
      expect(response.totalCount).toBe(1);
    });

    expect(mockProxy.list).toHaveBeenCalledWith(1, 10, '', 'Id desc');
  });

  it('should handle errors gracefully', () => {
    const errorResponse = new HttpErrorResponse({
      error: { message: 'Test error' },
      status: 500,
      statusText: 'Internal Server Error'
    });

    mockProxy.list.and.returnValue(throwError(() => errorResponse));

    service.getList(1, 10, '').subscribe({
      next: () => fail('Should have failed'),
      error: (error) => {
        expect(error).toBeTruthy();
        expect(error.status).toBe(500);
      }
    });
  });
});
```

### 🔄 Integration Testing

**API Integration Tests**
```typescript
describe('[ServiceName] API Integration', () => {
  let service: [ServiceName]Service;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        [ServiceName]Service,
        { provide: API_BASE_URL, useValue: 'http://test-api.com' }
      ]
    });

    service = TestBed.inject([ServiceName]Service);
    httpMock = TestBed.inject(HttpTestingController);
  });

  it('should make correct HTTP request for list', () => {
    const mockData = [{ id: 1, name: 'Test' }];

    service.getList(1, 10, 'search').subscribe();

    const req = httpMock.expectOne(
      'http://test-api.com/api/[Controller]/List?PageNumber=1&PageSize=10&Search=search&OrderBy=Id%20desc'
    );
    expect(req.request.method).toBe('GET');
    expect(req.request.headers.get('Authorization')).toBeTruthy();

    req.flush({
      statusCode: 200,
      successed: true,
      data: mockData,
      totalCount: 1
    });
  });
});
```

---

## 📋 Implementation Checklist

### 🔧 Development Tasks
- [ ] Review Swagger API specification
- [ ] Generate NSwag TypeScript clients
- [ ] Create custom service wrapper
- [ ] Implement data models and interfaces
- [ ] Add authentication and authorization
- [ ] Implement error handling
- [ ] Add data transformation logic
- [ ] Create validation services
- [ ] Implement state management (if needed)

### 🧪 Testing Tasks
- [ ] Write unit tests for services
- [ ] Create integration tests for API calls
- [ ] Test error handling scenarios
- [ ] Validate authentication flows
- [ ] Test permission-based access
- [ ] Performance testing for API calls
- [ ] Test data transformation logic

### 📚 Documentation Tasks
- [ ] Document API endpoints used
- [ ] Update service architecture documentation
- [ ] Create API integration guide
- [ ] Document error handling patterns
- [ ] Update deployment configuration

---

*Template Version: 1.0*
*Last Updated: 2025-06-24*
*Created by: Jadwa Development Team*
