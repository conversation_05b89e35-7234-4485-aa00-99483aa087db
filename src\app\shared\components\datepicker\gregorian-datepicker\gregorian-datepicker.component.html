<div class="input-group" dir="ltr">
  <!-- <input
      autocomplete="off"
      class="form-control text-right gregorian-date"
      [ngModelOptions]="{ standalone: true }"
      (mousedown)="d.toggle()"
      name="dp"
      [(ngModel)]="model"
      ngbDatepicker
      #d="ngbDatepicker"
      [placeholder]="placeholder"
      [disabled]="disabled || false"
      [minDate]="minDate " 
      [maxDate]="maxDate"
      [ngClass]="{ 'is-invalid': isInvalid }"
      (dateSelect)="onDateSelect()"
      (blur)="onDateSelectBlur()"
    /> -->
  <ngb-datepicker   
 [minDate]="minDate || null"
  [maxDate]="maxDate || null"
  #dp [(ngModel)]="model" (navigate)="date = $event.next" (dateSelect)="onDateSelect()"
    (blur)="onDateSelectBlur()" />

</div>