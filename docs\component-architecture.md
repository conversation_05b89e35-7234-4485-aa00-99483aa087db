# 🎨 Component Architecture
## Detailed Documentation

> **A comprehensive guide to the component structure, UI patterns, and frontend architecture of the Jadwa Investment Web Application**

---

## 📋 Overview

This document provides **detailed information** about the component architecture, UI patterns, and frontend structure of the Jadwa Investment Web Application.

### 🎯 Component Architecture Goals
- **🧩 Modularity**: Reusable and maintainable component structure
- **🎨 Consistency**: Unified design system and UI patterns
- **⚡ Performance**: Optimized rendering and change detection
- **♿ Accessibility**: WCAG compliant and inclusive design
- **🌍 Internationalization**: Multi-language and RTL support

---

## 📑 Table of Contents

| Section | Description | Key Topics |
|---------|-------------|------------|
| [🏗️ Component Hierarchy](#️-component-hierarchy) | Application component structure | Smart/dumb components, hierarchy |
| [🏠 Layout Components](#-layout-components) | Shell and navigation components | App shell, layouts, navigation |
| [🎯 Feature Components](#-feature-components) | Business functionality components | Domain-specific components |
| [🔧 Shared Components](#-shared-components) | Reusable UI components | Form controls, tables, buttons |
| [🔄 Communication Patterns](#-component-communication-patterns) | Component interaction methods | Input/output, services, events |
| [💾 State Management](#-state-management) | Component state handling | Local state, shared state |
| [🎨 UI/UX Patterns](#-uiux-patterns) | Design patterns and guidelines | Responsive design, themes |

---

## 🏗️ Component Hierarchy

### 🏛️ Application Structure

The application follows a **hierarchical component architecture** with clear separation of responsibilities:

```mermaid
graph TD
    subgraph "Root Level"
        A[AppComponent]
    end
    
    subgraph "Layout Level"
        B[AuthLayoutComponent]
        C[AdminLayoutComponent]
    end
    
    subgraph "Layout Children"
        D[AdminLayoutHeaderComponent]
        E[AdminLayoutSideNavComponent]
        F[BreadcrumbComponent]
    end
    
    subgraph "Feature Level"
        G[LoginComponent]
        H[DashboardComponent]
        I[InvestmentFundsComponent]
        J[FundStrategiesComponent]
    end
    
    subgraph "Feature Children"
        K[FundDetailsComponent]
        L[CreateFundComponent]
        M[UpdateFundComponent]
        N[VotingCardComponent]
    end
    
    A --> B
    A --> C
    C --> D
    C --> E
    C --> F
    
    B --> G
    C --> H
    C --> I
    C --> J
    
    I --> K
    I --> L
    I --> M
    H --> N
```

### Component Types Classification

```mermaid
graph LR
    subgraph "Smart Components"
        A[Container Components]
        B[Feature Components]
        C[Page Components]
    end
    
    subgraph "Dumb Components"
        D[Presentation Components]
        E[UI Components]
        F[Shared Components]
    end
    
    subgraph "Layout Components"
        G[Shell Components]
        H[Navigation Components]
        I[Header/Footer]
    end
    
    A --> D
    B --> E
    C --> F
    G --> A
    G --> B
    G --> C
```

## Layout Components

### App Component (Root)
```typescript
@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, ReusableArabdtSpinnerComponent, ArabdtThemeSwicherDirective, CommonModule, AlertComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent {
  alert: { message: string; type: any } | null = null;
  currentTheme: 'dark' | 'light' = 'light';
  
  constructor(
    private languageService: LanguageService,
    private errorModalService: ErrorModalService
  ) {
    languageService.initLang();
    this.errorModalService.alert$.subscribe((alert: any) => this.alert = alert);
  }
}
```

### Admin Layout Component
```typescript
@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    AdminLayoutHeaderComponent,
    BreadcrumbComponent,
    TableComponent,
    AdminLayoutSideNavComponent,
    DateHijriConverterPipe
  ],
  templateUrl: './admin-layout.component.html',
  styleUrls: ['./admin-layout.component.scss']
})
export class AdminLayoutComponent implements OnInit {
  isSidenavOpen = true;
  notification: any = null;
  
  // Firebase messaging integration
  // Sidenav toggle functionality
  // Notification handling
}
```

### Layout Structure
```mermaid
graph TB
    subgraph "Admin Layout"
        A[Header Component]
        B[Side Navigation]
        C[Main Content Area]
        D[Breadcrumb]
    end
    
    subgraph "Header Features"
        E[User Profile]
        F[Language Switcher]
        G[Theme Toggle]
        H[Notifications]
    end
    
    subgraph "Navigation Features"
        I[Menu Items]
        J[Active State]
        K[Collapse/Expand]
    end
    
    A --> E
    A --> F
    A --> G
    A --> H
    
    B --> I
    B --> J
    B --> K
    
    C --> D
```

## Feature Components

### Dashboard Component
```typescript
@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, TranslateModule, VotingCardComponent],
  templateUrl: './dashboard.component.html',
})
export class DashboardComponent {
  // Dashboard-specific logic
  // Widget management
  // Data aggregation
}
```

### Investment Funds Component
```typescript
@Component({
  selector: 'app-investment-funds',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    TableComponent,
    PageHeaderComponent,
    CustomButtonComponent
  ],
  templateUrl: './investment-funds.component.html',
  styleUrls: ['./investment-funds.component.scss']
})
export class InvestmentFundsComponent implements OnInit {
  // Fund list management
  // CRUD operations
  // Table configuration
  // Search and filtering
}
```

### Fund Details Component
```typescript
@Component({
  selector: 'app-fund-details',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FundCardInfoComponent,
    FundHistoryComponent,
    AlertComponent,
    DateHijriConverterPipe
  ],
  templateUrl: './fund-details.component.html',
  styleUrls: ['./fund-details.component.scss'],
})
export class FundDetailsComponent implements OnInit {
  fundDetails: FundDetailsDto | null = null;
  fundCards: any[] = [];
  
  // Fund detail management
  // Card configuration
  // History tracking
}
```

### Component Lifecycle Pattern
```mermaid
sequenceDiagram
    participant C as Component
    participant S as Service
    participant A as API
    
    Note over C: ngOnInit
    C->>S: Subscribe to data
    S->>A: Fetch initial data
    A->>S: Return data
    S->>C: Emit data
    C->>C: Update UI
    
    Note over C: User Interaction
    C->>S: Trigger action
    S->>A: API call
    A->>S: Response
    S->>C: Update observable
    C->>C: Refresh UI
    
    Note over C: ngOnDestroy
    C->>C: Unsubscribe observables
```

## Shared Components

### Table Component
```typescript
@Component({
  selector: 'app-table',
  standalone: true,
  imports: [CommonModule, TranslateModule, MatTableModule, MatPaginatorModule],
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.scss']
})
export class TableComponent<T> implements OnInit {
  @Input() columns: TableColumn[] = [];
  @Input() data: T[] = [];
  @Input() totalCount: number = 0;
  @Input() pageSize: number = 10;
  @Output() actionClicked = new EventEmitter<TableActionEvent>();
  @Output() pageChanged = new EventEmitter<PageEvent>();
  
  // Generic table functionality
  // Pagination support
  // Action handling
  // Sorting capabilities
}
```

### Custom Button Component
```typescript
@Component({
  selector: 'app-custom-button',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './custom-button.component.html',
  styleUrls: ['./custom-button.component.scss']
})
export class CustomButtonComponent {
  @Input() iconName: IconEnum | undefined;
  @Input() btnName!: string;
  @Input() class!: string;
  @Input() buttonType: ButtonTypeEnum = ButtonTypeEnum.Primary;
  @Output() click = new EventEmitter<any>();
  
  // Button variants
  // Icon support
  // Event emission
}
```

### Form Builder Component
```typescript
@Component({
  selector: 'app-form-builder',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, TranslateModule, NgSelectModule],
  templateUrl: './form-builder.component.html',
  styleUrls: ['./form-builder.component.scss']
})
export class FormBuilderComponent implements OnInit {
  @Input() formConfig: FormFieldConfig[] = [];
  @Input() formData: any = {};
  @Output() formSubmit = new EventEmitter<any>();
  @Output() formChange = new EventEmitter<any>();
  
  // Dynamic form generation
  // Validation handling
  // Field type support
}
```

### Shared Component Architecture
```mermaid
graph TB
    subgraph "Input Components"
        A[Form Builder]
        B[Custom Button]
        C[Date Picker]
        D[File Upload]
    end
    
    subgraph "Display Components"
        E[Table Component]
        F[Card Component]
        G[Alert Component]
        H[Page Header]
    end
    
    subgraph "Navigation Components"
        I[Breadcrumb]
        J[Pagination]
        K[Tabs]
    end
    
    subgraph "Utility Components"
        L[Loading Spinner]
        M[Modal Dialog]
        N[Tooltip]
    end
```

## Component Communication Patterns

### Parent-Child Communication
```typescript
// Parent Component
@Component({
  template: `
    <app-child 
      [inputData]="parentData"
      (outputEvent)="handleChildEvent($event)">
    </app-child>
  `
})
export class ParentComponent {
  parentData = { /* data */ };
  
  handleChildEvent(data: any) {
    // Handle child event
  }
}

// Child Component
@Component({
  selector: 'app-child'
})
export class ChildComponent {
  @Input() inputData: any;
  @Output() outputEvent = new EventEmitter<any>();
  
  emitEvent() {
    this.outputEvent.emit(this.data);
  }
}
```

### Service-Based Communication
```typescript
// Shared Service
@Injectable({ providedIn: 'root' })
export class SharedDataService {
  private dataSubject = new BehaviorSubject<any>(null);
  data$ = this.dataSubject.asObservable();
  
  updateData(data: any) {
    this.dataSubject.next(data);
  }
}

// Component A
export class ComponentA {
  constructor(private sharedService: SharedDataService) {}
  
  updateSharedData() {
    this.sharedService.updateData(this.newData);
  }
}

// Component B
export class ComponentB {
  constructor(private sharedService: SharedDataService) {}
  
  ngOnInit() {
    this.sharedService.data$.subscribe(data => {
      this.handleDataUpdate(data);
    });
  }
}
```

## State Management

### Component State Pattern
```typescript
export class ComponentStateExample implements OnInit, OnDestroy {
  // Local state
  private destroy$ = new Subject<void>();
  loading = false;
  data: any[] = [];
  selectedItem: any = null;
  
  // Form state
  form = this.fb.group({
    name: ['', Validators.required],
    email: ['', [Validators.required, Validators.email]]
  });
  
  ngOnInit() {
    this.loadData();
    this.setupFormSubscriptions();
  }
  
  private loadData() {
    this.loading = true;
    this.dataService.getData()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.data = data;
          this.loading = false;
        },
        error: (error) => {
          this.handleError(error);
          this.loading = false;
        }
      });
  }
  
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
```

### State Management Flow
```mermaid
graph LR
    subgraph "Component State"
        A[Local Variables]
        B[Form State]
        C[UI State]
    end
    
    subgraph "Service State"
        D[BehaviorSubject]
        E[Observable Streams]
        F[Cached Data]
    end
    
    subgraph "Persistent State"
        G[LocalStorage]
        H[SessionStorage]
        I[Cookies]
    end
    
    A --> D
    B --> E
    C --> F
    
    D --> G
    E --> H
    F --> I
```

## UI/UX Patterns

### Responsive Design Pattern
```scss
// Mobile-first approach
.component {
  // Mobile styles (default)
  padding: 1rem;
  
  // Tablet styles
  @media (min-width: 768px) {
    padding: 1.5rem;
  }
  
  // Desktop styles
  @media (min-width: 1024px) {
    padding: 2rem;
  }
}
```

### Theme Support
```scss
// Theme variables
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --background-color: #ffffff;
  --text-color: #333333;
}

[data-theme="dark"] {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --background-color: #1a1a1a;
  --text-color: #ffffff;
}
```

### RTL Support
```scss
// RTL/LTR support
.component {
  margin-left: 1rem;
  
  [dir="rtl"] & {
    margin-left: 0;
    margin-right: 1rem;
  }
}
```

### Loading States
```typescript
// Loading state management
export class LoadingStateExample {
  loading = false;
  error: string | null = null;
  
  async loadData() {
    this.loading = true;
    this.error = null;
    
    try {
      const data = await this.dataService.getData().toPromise();
      this.handleSuccess(data);
    } catch (error) {
      this.error = 'Failed to load data';
    } finally {
      this.loading = false;
    }
  }
}
```

### Error Handling UI
```html
<!-- Error display pattern -->
<div class="content-container">
  <div *ngIf="loading" class="loading-spinner">
    <app-spinner></app-spinner>
  </div>
  
  <div *ngIf="error" class="error-message">
    <app-alert type="error" [message]="error"></app-alert>
  </div>
  
  <div *ngIf="!loading && !error" class="content">
    <!-- Main content -->
  </div>
</div>
```

---

## Best Practices

### Component Design Principles
1. **Single Responsibility**: Each component has one clear purpose
2. **Reusability**: Components designed for reuse across features
3. **Composition**: Complex UIs built from simple components
4. **Separation of Concerns**: Logic separated from presentation
5. **Type Safety**: Strong typing with TypeScript
6. **Performance**: OnPush change detection where appropriate

### Performance Optimization
- **Lazy Loading**: Components loaded on demand
- **OnPush Strategy**: Optimized change detection
- **TrackBy Functions**: Efficient list rendering
- **Subscription Management**: Proper cleanup to prevent memory leaks
- **Async Pipe**: Automatic subscription management
