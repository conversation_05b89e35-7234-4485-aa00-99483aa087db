import { Validators } from '@angular/forms';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { passwordValidator, passwordStrengthValidator } from '@shared/validators/password.validator';

/**
 * Helper function to create a password control with proper validation
 */
export function createPasswordControl(options: {
  formControlName: string;
  id: string;
  name: string;
  label: string;
  placeholder?: string;
  isRequired?: boolean;
  class?: string;
  minLength?: number;
  maxLength?: number;
  showStrengthIndicator?: boolean;
  passwordStrength?: 'weak' | 'medium' | 'strong';
}): IControlOption {
  return {
    formControlName: options.formControlName,
    type: InputType.Password,
    id: options.id,
    name: options.name,
    label: options.label,
    placeholder: options.placeholder || 'Enter password...',
    isRequired: options.isRequired ?? true,
    class: options.class || 'col-md-6',
    minLength: options.minLength || 8,
    maxLength: options.maxLength || 50,
    showStrengthIndicator: options.showStrengthIndicator ?? true,
    passwordStrength: options.passwordStrength || 'strong'
  };
}

/**
 * Helper function to get password validators based on strength level
 */
export function getPasswordValidators(
  isRequired: boolean = true,
  strength: 'weak' | 'medium' | 'strong' = 'strong'
) {
  const validators = [];
  
  if (isRequired) {
    validators.push(Validators.required);
  }
  
  validators.push(passwordStrengthValidator(strength));
  
  return validators;
}

/**
 * Helper function to create a confirm password control
 */
export function createConfirmPasswordControl(options: {
  formControlName: string;
  id: string;
  name: string;
  label: string;
  placeholder?: string;
  isRequired?: boolean;
  class?: string;
}): IControlOption {
  return {
    formControlName: options.formControlName,
    type: InputType.Password,
    id: options.id,
    name: options.name,
    label: options.label,
    placeholder: options.placeholder || 'Confirm password...',
    isRequired: options.isRequired ?? true,
    class: options.class || 'col-md-6',
    showStrengthIndicator: false // Don't show strength indicator for confirm password
  };
}

/**
 * Custom validator to check if password and confirm password match
 */
export function passwordMatchValidator(passwordField: string, confirmPasswordField: string) {
  return (formGroup: any) => {
    const password = formGroup.get(passwordField);
    const confirmPassword = formGroup.get(confirmPasswordField);

    if (!password || !confirmPassword) {
      return null;
    }

    if (password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    } else {
      // Clear the error if passwords match
      if (confirmPassword.errors?.['passwordMismatch']) {
        delete confirmPassword.errors['passwordMismatch'];
        if (Object.keys(confirmPassword.errors).length === 0) {
          confirmPassword.setErrors(null);
        }
      }
    }

    return null;
  };
}
