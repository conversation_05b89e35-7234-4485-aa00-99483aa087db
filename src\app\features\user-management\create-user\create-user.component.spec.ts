import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';

import { CreateUserComponent } from './create-user.component';
import { UserManagementService } from '@shared/services/users/user-management.service';
import { ErrorModalService } from '@core/services/error-modal.service';

describe('CreateUserComponent - Multi-Role Selection Logic', () => {
  let component: CreateUserComponent;
  let fixture: ComponentFixture<CreateUserComponent>;
  let mockUserManagementService: jasmine.SpyObj<UserManagementService>;
  let mockErrorModalService: jasmine.SpyObj<ErrorModalService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockTranslateService: jasmine.SpyObj<TranslateService>;

  beforeEach(async () => {
    const userManagementServiceSpy = jasmine.createSpyObj('UserManagementService', [
      'getRolesList',
      'CheckRoleAvailability',
      'checkUserWithUniqueRole',
      'createUser',
      'deactivateUser'
    ]);
    const errorModalServiceSpy = jasmine.createSpyObj('ErrorModalService', ['showError', 'showSuccess']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const translateServiceSpy = jasmine.createSpyObj('TranslateService', ['instant']);

    await TestBed.configureTestingModule({
      imports: [
        CreateUserComponent,
        ReactiveFormsModule,
        TranslateModule.forRoot()
      ],
      providers: [
        FormBuilder,
        { provide: UserManagementService, useValue: userManagementServiceSpy },
        { provide: ErrorModalService, useValue: errorModalServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: TranslateService, useValue: translateServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CreateUserComponent);
    component = fixture.componentInstance;
    mockUserManagementService = TestBed.inject(UserManagementService) as jasmine.SpyObj<UserManagementService>;
    mockErrorModalService = TestBed.inject(ErrorModalService) as jasmine.SpyObj<ErrorModalService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockTranslateService = TestBed.inject(TranslateService) as jasmine.SpyObj<TranslateService>;

    // Setup default mocks
    mockUserManagementService.getRolesList.and.returnValue(of({ data: [] }));
    mockUserManagementService.CheckRoleAvailability.and.returnValue(of({ data: {} }));
  });

  describe('shouldEnableMultiSelect', () => {
    it('should return false for empty array', () => {
      const result = (component as any).shouldEnableMultiSelect([]);
      expect(result).toBeFalse();
    });

    it('should return false for null or undefined input', () => {
      const result1 = (component as any).shouldEnableMultiSelect(null);
      const result2 = (component as any).shouldEnableMultiSelect(undefined);
      expect(result1).toBeFalse();
      expect(result2).toBeFalse();
    });

    it('should return false for single role selection', () => {
      const result1 = (component as any).shouldEnableMultiSelect(['Fund Manager']);
      const result2 = (component as any).shouldEnableMultiSelect(['Board Member']);
      const result3 = (component as any).shouldEnableMultiSelect(['Associate Fund Manager']);
      expect(result1).toBeFalse();
      expect(result2).toBeFalse();
      expect(result3).toBeFalse();
    });

    it('should return true for Fund Manager + Board Member combination', () => {
      const result1 = (component as any).shouldEnableMultiSelect(['Fund Manager', 'Board Member']);
      const result2 = (component as any).shouldEnableMultiSelect(['Board Member', 'Fund Manager']);
      expect(result1).toBeTrue();
      expect(result2).toBeTrue();
    });

    it('should return true for Associate Fund Manager + Board Member combination', () => {
      const result1 = (component as any).shouldEnableMultiSelect(['Associate Fund Manager', 'Board Member']);
      const result2 = (component as any).shouldEnableMultiSelect(['Board Member', 'Associate Fund Manager']);
      expect(result1).toBeTrue();
      expect(result2).toBeTrue();
    });

    it('should return false for invalid multi-role combinations', () => {
      const result1 = (component as any).shouldEnableMultiSelect(['Fund Manager', 'Legal Council']);
      const result2 = (component as any).shouldEnableMultiSelect(['Associate Fund Manager', 'Finance Controller']);
      const result3 = (component as any).shouldEnableMultiSelect(['Board Member', 'Board Secretary']);
      expect(result1).toBeFalse();
      expect(result2).toBeFalse();
      expect(result3).toBeFalse();
    });

    it('should return false for more than 2 roles', () => {
      const result = (component as any).shouldEnableMultiSelect(['Fund Manager', 'Board Member', 'Legal Council']);
      expect(result).toBeFalse();
    });

    it('should handle case-insensitive role names', () => {
      const result1 = (component as any).shouldEnableMultiSelect(['fund manager', 'board member']);
      const result2 = (component as any).shouldEnableMultiSelect(['ASSOCIATE FUND MANAGER', 'BOARD MEMBER']);
      expect(result1).toBeTrue();
      expect(result2).toBeTrue();
    });

    it('should handle different role name formats', () => {
      const result1 = (component as any).shouldEnableMultiSelect(['fund_manager', 'board_member']);
      const result2 = (component as any).shouldEnableMultiSelect(['fundmanager', 'boardmember']);
      expect(result1).toBeTrue();
      expect(result2).toBeTrue();
    });
  });

  describe('normalizeRoleName', () => {
    it('should handle null and undefined inputs', () => {
      const result1 = (component as any).normalizeRoleName(null);
      const result2 = (component as any).normalizeRoleName(undefined);
      const result3 = (component as any).normalizeRoleName('');
      expect(result1).toBe('');
      expect(result2).toBe('');
      expect(result3).toBe('');
    });

    it('should normalize Fund Manager variations', () => {
      const variations = ['Fund Manager', 'fund manager', 'FUND MANAGER', 'fund_manager', 'fundmanager'];
      variations.forEach(variation => {
        const result = (component as any).normalizeRoleName(variation);
        expect(result).toBe('fundmanager');
      });
    });

    it('should normalize Associate Fund Manager variations', () => {
      const variations = [
        'Associate Fund Manager', 
        'associate fund manager', 
        'ASSOCIATE FUND MANAGER', 
        'associate_fund_manager',
        'associatedfundmanager',
        'associatefundmanager'
      ];
      variations.forEach(variation => {
        const result = (component as any).normalizeRoleName(variation);
        expect(result).toBe('associatedfundmanager');
      });
    });

    it('should normalize Board Member variations', () => {
      const variations = ['Board Member', 'board member', 'BOARD MEMBER', 'board_member', 'boardmember'];
      variations.forEach(variation => {
        const result = (component as any).normalizeRoleName(variation);
        expect(result).toBe('boardmember');
      });
    });

    it('should return empty string for unknown roles', () => {
      const result = (component as any).normalizeRoleName('Unknown Role');
      expect(result).toBe('');
    });
  });
});
