@import "../../../../../assets/scss/variables";

.create-resolution-page {
  // Align with fund components styling
  .form-container {
    padding: 2rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 0.5px solid #dce0e3;
    .header {
      color:  #00205a;
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
    }
    .hr-first{
      margin: 24px 0 5px;
      border: 1px solid #E5E7EB;
    }
    .hr-last{
      border: 1px solid #E5E7EB;
      margin: 32px 0 16px;
    }
    .form-group {
      margin-bottom: 1.5rem;

      &.col-12 {
        width: 100%;
      }

      &.col-md-6 {
        width: 48%;
        margin-inline-end: 2%;
        &:nth-child(2n) {
          margin-inline-end: 0;
        }
      }
    }
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
  }

  // RTL support
  [dir="rtl"] & {
    .actions {
      justify-content: flex-start;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .form-container {
      padding: 1rem;
    }

    .actions {
      flex-direction: column;
      gap: 0.5rem;

      ::ng-deep app-custom-button {
        width: 100%;
      }
    }
  }

  @media (max-width: 480px) {
    .form-container {
      padding: 0.75rem;
    }
  }
}
