import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { AdvancedSearchDialogComponent } from './advanced-search-dialog.component';
import { ResolutionsServiceProxy, TypesServiceProxy } from '@core/api/api.generated';

describe('AdvancedSearchDialogComponent', () => {
  let component: AdvancedSearchDialogComponent;
  let fixture: ComponentFixture<AdvancedSearchDialogComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<AdvancedSearchDialogComponent>>;
  let mockResolutionsProxy: jasmine.SpyObj<ResolutionsServiceProxy>;
  let mockTypesProxy: jasmine.SpyObj<TypesServiceProxy>;

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    mockResolutionsProxy = jasmine.createSpyObj('ResolutionsServiceProxy', ['statuses']);
    mockTypesProxy = jasmine.createSpyObj('TypesServiceProxy', ['all']);

    // Setup mock responses
    mockResolutionsProxy.statuses.and.returnValue(of({
      successed: true,
      data: [],
      statusCode: 200,
      message: '',
      errors: []
    } as any));

    mockTypesProxy.all.and.returnValue(of({
      successed: true,
      data: [],
      statusCode: 200,
      message: '',
      errors: []
    } as any));

    await TestBed.configureTestingModule({
      imports: [
        AdvancedSearchDialogComponent,
        TranslateModule.forRoot(),
        NoopAnimationsModule
      ],
      providers: [
        FormBuilder,
        { provide: MatDialogRef, useValue: mockDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: {} },
        { provide: ResolutionsServiceProxy, useValue: mockResolutionsProxy },
        { provide: TypesServiceProxy, useValue: mockTypesProxy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AdvancedSearchDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    expect(component.formGroup).toBeDefined();
    expect(component.formGroup.get('search')?.value).toBe('');
    expect(component.formGroup.get('status')?.value).toBe('');
    expect(component.formGroup.get('resolutionType')?.value).toBe('');
  });

  it('should apply filters and close dialog with clean data', () => {
    // Set form values
    component.formGroup.patchValue({
      search: 'test search',
      status: 'approved',
      resolutionType: '',
      fromDate: '2025-01-01',
      toDate: '',
      createdBy: 'user1'
    });

    component.applyFilters();

    expect(mockDialogRef.close).toHaveBeenCalledWith({
      search: 'test search',
      status: 'approved',
      fromDate: '2025-01-01',
      createdBy: 'user1'
    });
  });

  it('should reset filters', () => {
    // Set some values first
    component.formGroup.patchValue({
      search: 'test',
      status: 8 // statusId for approved
    });

    component.resetFilters();

    expect(component.formGroup.get('search')?.value).toBe(null);
    expect(component.formGroup.get('status')?.value).toBe(null);
    expect(component.isFormSubmitted).toBe(false);
  });

  it('should close dialog without data', () => {
    component.closeDialog();
    expect(mockDialogRef.close).toHaveBeenCalledWith();
  });

  it('should have correct form controls configuration', () => {
    expect(component.formControls.length).toBe(6);
    
    const searchControl = component.formControls.find(c => c.formControlName === 'search');
    expect(searchControl?.label).toBe('INVESTMENT_FUNDS.RESOLUTIONS.SEARCH');
    
    const statusControl = component.formControls.find(c => c.formControlName === 'status');
    expect(statusControl?.options?.length).toBe(5); // All statuses + 4 specific statuses
  });
});
