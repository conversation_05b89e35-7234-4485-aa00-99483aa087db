<div class="resolutions-page" >
  <!-- Breadcrumb -->
  <div class="breadcrumb-section">
    <app-breadcrumb
      (onClickEvent)="onBreadcrumbClicked($event)"
      [breadcrumbs]="breadcrumbItems"
      [size]="breadcrumbSizeEnum.Medium"
      divider=">">
    </app-breadcrumb>
  </div>
  <!-- Page Header -->
  <div class="page-header-section">
    <!-- <app-page-header title="INVESTMENT_FUNDS.TITLE" [showCreateButton]=" isHasPermissionAdd" [showSearch]="true" [showFilter]="true"
    createButtonText="INVESTMENT_FUNDS.CREATE_NEW_FUND" searchPlaceholder="INVESTMENT_FUNDS.SEARCH_PLACEHOLDER"
    (create)="addNewResolution()" (search)="onSearch($event)" (filter)="openFilter()"></app-page-header> -->


    <app-page-header 
    [title]="'INVESTMENT_FUNDS.RESOLUTIONS.TITLE' | translate" 
    [showSearch]="true" 
    [showFilter]="true"
    [showCreateButton]="tokenService.hasPermission('Resolution.Create')" 
    [searchPlaceholder]="translateService.instant('INVESTMENT_FUNDS.RESOLUTIONS.SEARCH_PLACEHOLDER')"
    [restrictSearchToNumbers]="true"
    createButtonText="INVESTMENT_FUNDS.RESOLUTIONS.ADD"
    (search)="onSearch($event)" (filter)="openFilter()"
    (create)="addNewResolution()">
    </app-page-header>
  </div>

  <!-- Main Content -->
  <div class="content-section">
    <!-- Search and Filters Section -->


    <!-- Loading State -->
    <div class="loading-state" *ngIf="isLoading">
      <!-- <div class="loading-content">
        <i class="fas fa-spinner fa-spin loading-icon"></i>
        <p>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.LOADING' | translate }}</p>
      </div> -->
    </div>

    <!-- Error State -->
    <div class="error-state" *ngIf="hasError && !isLoading">
      <div class="error-content">
        <!-- <i class="fas fa-exclamation-triangle error-icon"></i> -->
        <h3>{{ 'COMMON.ERROR' | translate }}</h3>
        <!-- <p>{{ errorMessage | translate }}</p> -->
        <button class="btn btn-primary retry-btn" (click)="loadResolutions()">
          {{ 'COMMON.RETRY' | translate }}
        </button>
      </div>
    </div>

    <!-- Resolutions Grid -->
    <div class="resolutions-grid" *ngIf="!isLoading && !hasError">
      <!-- Resolution Card -->
      <div class="resolution-card" *ngFor="let resolution of filteredResolutions">
        <div class="card-header">
          <h3 class="resolution-title">
            <span class="title">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.CODE' | translate }}:</span>  {{ resolution.code }}</h3>
          <div class="card-actions">
            <!-- Details Button - Always visible for authorized users -->
             <!-- CompletingData = 3 /     WaitingForConfirmation = 4/      Confirmed = 5,      Rejected = 6, -->
              <!-- *ngIf="resolution.canView && tokenService.hasPermission('Resolution.View') 2=>pending / 10=>cancelled / draft =>1-->

            <button class="action-btn details-btn"
             *ngIf="resolution.canView"
                    (click)="viewResolutionDetails(resolution)"
                    [title]="'COMMON.VIEW_DETAILS' | translate">
                    <img src="assets/images/eye.png" alt="details" />
                  </button>

            <button class="action-btn"
                    *ngIf="resolution.canEdit && tokenService.hasPermission('Resolution.Edit') &&
                    (tokenService.hasRole('fundmanager') || (resolution.statusId >= 4 && resolution.statusId != 10))"
                    (click)="editResolution(resolution)"
                    [title]="'COMMON.EDIT' | translate">
                    <img src="assets/images/edit.png" alt="edit" />
                  </button>
                    <button class="action-btn"
                    *ngIf="resolution.canEdit && tokenService.hasPermission('Resolution.Edit') && ( tokenService.hasRole('legalcouncil') || tokenService.hasRole('boardsecretary'))
                    && (resolution.statusId == 2 || resolution.statusId == 3)"
                    (click)="editResolution(resolution)"
                    [title]="'COMMON.Complete' | translate">
                    <img src="assets/images/Arrow_Icon.png" class="rotate-icon" alt="edit" />
                  </button>
                  <button class="action-btn"
                  *ngIf="resolution.canCancel && tokenService.hasPermission('Resolution.Cancel')"
                  (click)="cancelResolution(resolution)"
                  [title]="'COMMON.CANCEL' | translate">
                  <img src="assets/images/x.png" class="mx-2" alt="">
                </button>
            <button class="action-btn"
                    *ngIf="resolution.canDelete && tokenService.hasPermission('Resolution.Delete')"
                    (click)="deleteResolution(resolution)"
                    [title]="'COMMON.DELETE' | translate">
                    <img src="assets/images/trash.png"  alt="">
                  </button>
          </div>
        </div>
        <div class="card-content">

          <p class="title">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.TYPE' | translate }}</p>
          <p class="resolution-type">{{(resolution.resolutionType.localizedName || resolution.resolutionType.nameAr || resolution.resolutionType.nameEn || '') }}</p>
          <p class="title" >{{ 'INVESTMENT_FUNDS.RESOLUTIONS.DESCRIPTION_CARD' | translate }}</p>
          <!-- <p class="resolution-description">{{ resolution.description }}</p> -->
          <p class="resolution-description description-text"  [title]="resolution.description"> {{ resolution.description || '-'}}</p>

          <div class="resolution-meta">
            <div class="meta-item">
              <span class="meta-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.LAST_UPDATED_DATE' | translate }}:</span>
              <p class="gregorian mb-0">{{(resolution.lastUpdated ? resolution.lastUpdated.toString() : '') | date: 'd/M/y'}}</p>
              <p class="hijri title">{{(resolution.lastUpdated ? resolution.lastUpdated.toString() : '') | dateHijriConverter}}</p>
            </div>
            <div class="status">
              <span class="status" [ngClass]="getStatusClass(resolution.statusId)">
                {{ (resolution.resolutionStatus.localizedName || resolution.resolutionStatus.nameAr || resolution.resolutionStatus.nameEn || '') | translate }}
              </span>
              <!-- <span class="meta-value status" [ngClass]="'status-' +(resolution.statusDisplay ? resolution.statusDisplay.toLowerCase() : '')">
              </span> -->
            </div>
          </div>
        </div>

        <!-- Action Buttons (Hidden Menu) -->
        <!-- <div class="card-actions" *ngIf="resolution.canEdit || resolution.canCancel || resolution.canDelete">
          <button class="action-btn cancel-btn"
                  *ngIf="resolution.canCancel && tokenService.hasPermission('Resolution.Cancel')"
                  (click)="cancelResolution(resolution)"
                  [title]="'COMMON.CANCEL' | translate">
            <img src="assets/images/x.png" alt="cancel">
          </button>

          <button class="action-btn delete-btn"
                  *ngIf="resolution.canDelete && tokenService.hasPermission('Resolution.Delete')"
                  (click)="deleteResolution(resolution)"
                  [title]="'COMMON.DELETE' | translate">
            <img src="assets/images/trash.png" alt="delete">
          </button>
        </div> -->
      </div>
    </div>

    <!-- Pagination Controls -->
    <div class="pagination-section" *ngIf="!isLoading && !hasError && filteredResolutions.length > 0">
      <!-- Records Information -->
      <!-- <div class="pagination-info">
        <div class="records-info">
          <span>{{ 'PAGINATION.RECORDS_INFO' | translate: getRecordsInfo() }}</span>
        </div>
        <div class="page-size-selector">
          <label>{{ 'PAGINATION.PAGE_SIZE' | translate }}:</label>
          <select class="page-size-select" [(ngModel)]="pageSize" (ngModelChange)="onPageSizeChange($event)">
            <option *ngFor="let size of pageSizeOptions" [value]="size">{{size}}</option>
          </select>
        </div>
      </div> -->

      <!-- Pagination Controls -->
      <div class="pagination-controls">
        <!-- First Page Button -->
        <!-- <button class="pagination-btn first-btn"
                [disabled]="!canGoPrevious()"
                (click)="onFirstPage()"
                [title]="'PAGINATION.FIRST' | translate">
          <i class="fas fa-angle-double-left"></i>
          <span class="btn-text">{{ 'PAGINATION.FIRST' | translate }}</span>
        </button> -->

        <!-- Previous Page Button -->
        <button class="pagination-btn prev-btn"
                [disabled]="!canGoPrevious()"
                (click)="onPreviousPage()"
                [title]="'PAGINATION.PREVIOUS' | translate">
          <!-- <i class="fas fa-angle-left"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-left2.png' : 'assets/images/arrow-right.png'" class="mx-2" alt="previous">

          <span class="btn-text">{{ 'PAGINATION.PREVIOUS' | translate }}</span>
        </button>

        <!-- Page Numbers -->
        <div class="page-numbers">
          <button *ngFor="let page of getPageNumbers()"
                  class="pagination-btn page-number-btn"
                  [class.active]="page === currentPage"
                  (click)="onPageChange(page)">
            {{page}}
          </button>
        </div>

        <!-- Next Page Button -->
        <button class="pagination-btn next-btn"
                [disabled]="!canGoNext()"
                (click)="onNextPage()"
                [title]="'PAGINATION.NEXT' | translate">
          <span class="btn-text">{{ 'PAGINATION.NEXT' | translate }}</span>
          <!-- <i class="fas fa-angle-right"></i> -->
          <img [src]="isEnglish() ? 'assets/images/arrow-right.png' : 'assets/images/arrow-left2.png'" class="mx-2" alt="next">

        </button>

        <!-- Last Page Button -->
        <!-- <button class="pagination-btn last-btn"
                [disabled]="!canGoNext()"
                (click)="onLastPage()"
                [title]="'PAGINATION.LAST' | translate">
          <span class="btn-text">{{ 'PAGINATION.LAST' | translate }}</span>
          <i class="fas fa-angle-double-right"></i>
        </button> -->
      </div>

      <!-- Page Information -->
      <!-- <div class="page-info">
        <span>{{ 'PAGINATION.PAGE_INFO' | translate: {current: currentPage, total: totalPages} }}</span>
      </div> -->
    </div>

    <div *ngIf="!isLoading && !hasError && filteredResolutions.length === 0"
    class="d-flex  flex-column gap-4 justify-content-center mt-5 align-items-center">
    <img src="assets/images/nodata.png" width="350">
   
    <ng-container >
        <p  class="text-center mt-3 header fs-20">{{'INVESTMENT_FUNDS.NO_DATA' | translate}}</p>
        <app-custom-button *ngIf="tokenService.hasRole('fundmanager')"  class="mt-3" [btnName]="'INVESTMENT_FUNDS.RESOLUTIONS.CREATE_FIRST' | translate"
            [iconName]="createButtonIcon.plus" (click)="addNewResolution()">
        </app-custom-button>
    </ng-container>
</div>

    <!-- Empty State -->
    <!-- <div class="empty-state" >
      <div class="empty-state-content">
        <i class="fas fa-file-alt empty-icon"></i>
        <h3>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_RESOLUTIONS' | translate }}</h3>
        <p>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_RESOLUTIONS_MESSAGE' | translate }}</p>
        <button class="btn btn-primary"
                (click)="addNewResolution()">
          {{ 'INVESTMENT_FUNDS.RESOLUTIONS.CREATE_FIRST' | translate }}
        </button>
      </div>
    </div> -->
  </div>
</div>


