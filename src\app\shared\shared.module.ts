import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';

@NgModule({
  imports: [CommonModule, TranslateModule, ReactiveFormsModule, NgSelectModule],
  exports: [TranslateModule, ReactiveFormsModule, NgSelectModule],
})
export class SharedModule {}
