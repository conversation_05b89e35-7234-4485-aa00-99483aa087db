import { Component, Input, Output, EventEmitter, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule } from '@angular/forms';
import { Observable, forkJoin } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

// Shared components
import { FileUploadComponent } from '@shared/components/file-upload/file-upload.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// API and services
import {
  DocumentServiceProxy,
  DocumentCategoryDto,
  GetDocumentCategoriesQuery,
  AddDocumentCommand
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

export interface DocumentUploadDialogData {
  fundId?: number;
  selectedCategory?: DocumentCategoryDto;
}

@Component({
  selector: 'app-document-upload',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    FormsModule,
    FileUploadComponent,
    CustomButtonComponent
  ],
  templateUrl: './document-upload.component.html',
  styleUrls: ['./document-upload.component.scss']
})
export class DocumentUploadComponent {
  @Output() uploadComplete = new EventEmitter<any>();

  // Form data
  selectedCategory: number | null = null;
  documentTitle = '';
  selectedFiles: File[] = [];
  uploadedFiles: any[] = []; // Store uploaded file data with attachment IDs
  isUploading = false;

  // Document categories
  documentCategories: DocumentCategoryDto[] = [];

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  constructor(
    private documentProxy: DocumentServiceProxy,
    private errorModalService: ErrorModalService,
    private translateService: TranslateService,
    private dialogRef: MatDialogRef<DocumentUploadComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DocumentUploadDialogData
  ) {
    this.loadCategories();

    // Pre-select category if provided
    if (this.data?.selectedCategory) {
      this.selectedCategory = this.data.selectedCategory.id;
    }
  }

  private loadCategories(): void {
    // Use NSwag-generated proxy with proper typing
    const query = new GetDocumentCategoriesQuery();

    this.documentProxy.categories(query).subscribe({
      next: (response) => {
        // Response is properly typed as DocumentCategoryDtoListBaseResponse
        if (response?.data && Array.isArray(response.data)) {
          this.documentCategories = response.data;
        } else {
          this.documentCategories = [];
        }

        // If no categories available, show error
        if (this.documentCategories.length === 0) {
          console.warn('No document categories available for upload');
        }
      },
      error: (error: any) => {
        console.error('Error loading categories:', error);
        this.errorModalService.showError(this.translateService.instant('DOCUMENTS.ERRORS.LOAD_CATEGORIES_FAILED'));
        this.documentCategories = [];
      }
    });
  }

  onFileSelected(uploadedFiles: any | any[] | null): void {
    if (!uploadedFiles) {
      this.selectedFiles = [];
      this.uploadedFiles = [];
      return;
    }

    // Handle the uploaded files from FileUploadComponent
    // The FileUploadComponent already handles MinIO upload and returns the uploaded file data
    if (Array.isArray(uploadedFiles)) {
      this.uploadedFiles = uploadedFiles;
    } else {
      this.uploadedFiles = [uploadedFiles];
    }
  }

  onUpload(): void {
    if (!this.selectedCategory || this.uploadedFiles.length === 0) {
      return;
    }

    const category = this.documentCategories.find(cat => cat.id === this.selectedCategory);
    if (!category) {
      return;
    }

    this.isUploading = true;

    // Create document records using the already uploaded files
    this.createDocumentRecords(this.uploadedFiles, category.id, this.data?.fundId).subscribe({
      next: (results: any) => {
        this.isUploading = false;
        this.uploadComplete.emit({
          category: this.selectedCategory,
          files: this.uploadedFiles,
          title: this.documentTitle,
          results: results
        });
        this.dialogRef.close(true);
      },
      error: (error: any) => {
        console.error('Upload error:', error);
        this.errorModalService.showError(this.translateService.instant('DOCUMENTS.ERRORS.CREATE_DOCUMENT_FAILED'));
        this.isUploading = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  isFormValid(): boolean {
    return !!(this.selectedCategory && this.uploadedFiles.length > 0);
  }

  getCategoryDisplayName(category: DocumentCategoryDto): string {
    if (category?.name) {
      return this.translateService.instant(category.name);
    }
    return this.translateService.instant('DOCUMENTS.UNKNOWN_CATEGORY');
  }

  /**
   * Create document records using already uploaded files
   * @param uploadedFiles any[] - Files already uploaded to MinIO with attachment IDs
   * @param categoryId number
   * @param fundId number | undefined
   * @returns Observable<any>
   */
  private createDocumentRecords(uploadedFiles: any[], categoryId: number, fundId?: number): Observable<any> {
    const documentPromises = uploadedFiles.map(uploadedFile =>
      this.createSingleDocumentRecord(uploadedFile, categoryId, fundId)
    );

    return forkJoin(documentPromises);
  }

  /**
   * Create single document record using uploaded file data
   * @param uploadedFile any - File data from MinIO upload with attachment ID
   * @param categoryId number
   * @param fundId number | undefined
   * @returns Observable<any>
   */
  private createSingleDocumentRecord(uploadedFile: any, categoryId: number, fundId?: number): Observable<any> {
    // Use the attachment ID from the MinIO upload response
    const body = new AddDocumentCommand({
      documentCategoryId: categoryId,
      attachmentId: uploadedFile.id, // Use the actual attachment ID from MinIO upload
      fundId: fundId ?? 0
    });

    return this.documentProxy.add(body).pipe(
      map((response: any) => {
        return {
          success: true,
          data: response,
          uploadedFile: uploadedFile,
          message: 'Document record created successfully'
        };
      }),
      catchError((error: any) => {
        console.error('Document creation error:', error);
        this.errorModalService.showError(
          this.translateService.instant('DOCUMENTS.ERRORS.CREATE_SINGLE_DOCUMENT_FAILED', { fileName: uploadedFile.fileName })
        );
        throw error;
      })
    );
  }
}
