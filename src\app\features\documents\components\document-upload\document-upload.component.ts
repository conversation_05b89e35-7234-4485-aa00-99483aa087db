import { Component, Output, EventEmitter, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Observable, forkJoin } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

// Shared components and interfaces
import { FileUploadComponent } from '@shared/components/file-upload/file-upload.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';

// API and services
import {
  DocumentServiceProxy,
  DocumentCategoryDto,
  GetDocumentCategoriesQuery,
  AddDocumentCommand
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { AttachmentModule } from '@shared/enum/AttachmentModule';

export interface DocumentUploadDialogData {
  fundId?: number;
  selectedCategory?: DocumentCategoryDto;
}

@Component({
  selector: 'app-document-upload',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatProgressSpinnerModule,
    FileUploadComponent,
    CustomButtonComponent,
    FormBuilderComponent
  ],
  templateUrl: './document-upload.component.html',
  styleUrls: ['./document-upload.component.scss']
})
export class DocumentUploadComponent implements OnInit {
  @Output() uploadComplete = new EventEmitter<any>();

  // Form-related properties
  formGroup!: FormGroup;
  isFormSubmitted: boolean = false;
  isValidationFire: boolean = false;
  isLoading: boolean = false;
  private isSubmitting: boolean = false;

  // File upload data
  uploadedFiles: any[] = []; // Store uploaded file data with attachment IDs

  // Document categories
  documentCategories: DocumentCategoryDto[] = [];

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  // Form controls configuration
  formControls: IControlOption[] = [
    {
      type: InputType.Dropdown,
      formControlName: 'categoryId',
      id: 'categoryId',
      name: 'categoryId',
      label: 'DOCUMENTS.CATEGORY',
      placeholder: 'DOCUMENTS.SELECT_CATEGORY',
      isRequired: true,
      class: 'col-md-12',
      options: [],
    },
    {
      type: InputType.file,
      formControlName: 'attachmentId',
      id: 'attachmentId',
      name: 'attachmentId',
      label: 'INVESTMENT_FUNDS.FORM.TERMS_FILE',
      placeholder: 'INVESTMENT_FUNDS.FORM.DRAG_DROP_FILES',
      isRequired: true,
      class: 'col-md-12',
      allowedTypes: ['pdf'],
      moduleId: AttachmentModule.Documents
    },
  ];

  constructor(
    private formBuilder: FormBuilder,
    private documentProxy: DocumentServiceProxy,
    private errorModalService: ErrorModalService,
    private translateService: TranslateService,
    private dialogRef: MatDialogRef<DocumentUploadComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DocumentUploadDialogData
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadCategories();
  }
  
  onFileUpload(data: any) {
    this.formGroup.get(data.control.formControlName)?.setValue(data.file.id);
  }
 private initForm() {
    const formGroup: any = {};

    this.formControls.forEach((control) => {
      const validators = [];
      if (control.formControlName == '') return;
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      if (control.minLength) {
        validators.push(Validators.minLength(control.minLength));
      }
      if (control.maxLength) {
        validators.push(Validators.maxLength(control.maxLength));
      }
      if (control.max) {
        validators.push(Validators.max(control.max));
      }
      if (control.min) {
        validators.push(Validators.min(control.min));
      }
      formGroup[control.formControlName] = [null, validators];
    });

    this.formGroup = this.formBuilder.group(formGroup);
  }

  private loadCategories(): void {
    // Use NSwag-generated proxy with proper typing
    const query = new GetDocumentCategoriesQuery();

    this.documentProxy.categories(query).subscribe({
      next: (response) => {
        // Response is properly typed as DocumentCategoryDtoListBaseResponse
        if (response?.data && Array.isArray(response.data)) {
          this.documentCategories = response.data;

          // Update the dropdown options
          const categoryOptions = this.documentCategories.map(category => ({
            id: category.id,
            name: category.name || this.translateService.instant('DOCUMENTS.UNKNOWN_CATEGORY')
          }));

          const categoryDropdown = this.formControls.find(control => control.formControlName === 'categoryId');
          if (categoryDropdown) {
            categoryDropdown.options = categoryOptions;
          }

          // Pre-select category if provided
          if (this.data?.selectedCategory) {
            this.formGroup.get('categoryId')?.setValue(this.data.selectedCategory.id);
          }
        } else {
          this.documentCategories = [];
        }

        // If no categories available, show error
        if (this.documentCategories.length === 0) {
          console.warn('No document categories available for upload');
        }
      },
      error: (error: any) => {
        console.error('Error loading categories:', error);
        this.errorModalService.showError(this.translateService.instant('DOCUMENTS.ERRORS.LOAD_CATEGORIES_FAILED'));
        this.documentCategories = [];
      }
    });
  }


  onSubmitClick(): void {
    // This method is called by the button click
    // It triggers form validation and submission
    if (this.formGroup.valid) {
      this.onSubmit();
    } else {
      this.isSubmitting = false;
      this.isValidationFire = true;
    }
  }

  onSubmit(_data?: any): void {
    // Guard against multiple submissions
    if (this.isSubmitting || this.isLoading) {
      return;
    }

    this.isSubmitting = true;
    this.isFormSubmitted = true;
    this.isLoading = true;

    const body = new AddDocumentCommand({
      documentCategoryId: this.formGroup.get('categoryId')?.value ?? '',
      attachmentId: this.formGroup.get('attachmentId')?.value ?? 0, // Use the actual attachment ID from MinIO upload
      fundId: this.data.fundId ?? 0
    });
    this.documentProxy.add(body).subscribe({
      next: (response: any) => {
        this.errorModalService.showSuccess(response.data);
        this.dialogRef.close(true);
      },
      error: (error: any) => {
        this.isFormSubmitted = false;
        this.isValidationFire = false;
        console.log('error', error);
      },
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
