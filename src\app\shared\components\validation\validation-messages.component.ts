import { Component, Input } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-validation-messages',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  template: `
    <div *ngIf="formSubmitted && control?.errors" class="error-message">
      <div
        *ngIf="control?.errors?.['required']"
        translate="FORM.IS_REQUIRED"
      ></div>
      <div
        *ngIf="control?.errors?.['invalidAge']"
        translate="FORM.INVALID_AGE"
      ></div>
      <div
        *ngIf="control?.errors?.['futureDate']"
        translate="FORM.NO_FUTURE_DATE"
      ></div>
      <div
        *ngIf="control?.errors?.['minlength']"
        [translateParams]="{ min: control?.errors?.['minlength'].requiredLength }"
        translate="FORM.MIN_LENGTH_ERROR"
      ></div>
      <div
        *ngIf="control?.errors?.['maxlength']"
        [translateParams]="{ max: control?.errors?.['maxlength'].requiredLength }"
        translate="FORM.MAX_LENGTH_ERROR"
      ></div>
      <div
        *ngIf="control?.errors?.['min']"
        [translateParams]="{ min: control?.errors?.['min'].min }"
        translate="FORM.MIN_VALUE_ERROR"
      ></div>
      <div
        *ngIf="control?.errors?.['max']"
        [translateParams]="{ max: control?.errors?.['max'].max }"
        translate="FORM.MAX_VALUE_ERROR"
      ></div>
      <div
        *ngIf="control?.errors?.['email']"
        translate="FORM.IS_EMAIL"
      ></div>
      <div
        *ngIf="control?.errors?.['pattern']"
        translate="FORM.PATTERN_ERROR"
      ></div>
      <div
        *ngIf="control?.errors?.['integer']"
        translate="FORM.ERROR_INT"
      ></div>
      <div
        *ngIf="control?.errors?.['duplicate']"
        translate="FORM.ERROR_DUPLICATION"
      ></div>
      <div
        *ngIf="control?.errors?.['arabicText']"
        translate="FORM.ERROR_ARABIC_TEXT"
      ></div>
      <div
        *ngIf="control?.errors?.['englishText']"
        translate="FORM.ERROR_ENGLISH_TEXT"
      ></div>
      <div
        *ngIf="control?.errors?.['positiveInteger']"
        translate="FORM.ERROR_POSITIVE_INTEGER"
      ></div>
      <div
        *ngIf="control?.errors?.['saudiPhoneNumber']"
        translate="FORM.ERROR_SAUDI_PHONE"
      ></div>
      <div
        *ngIf="control?.errors?.['saudiPhone']"
        translate="FORM.ERROR_SAUDI_PHONE"
      ></div>
      <div
        *ngIf="control?.errors?.['pastDate']"
        translate="FORM.ERROR_PAST_DATE"
      ></div>
      <div
        *ngIf="control?.errors?.['minimumAge']"
        [translateParams]="{ requiredAge: control?.errors?.['minimumAge'].requiredAge }"
        translate="FORM.ERROR_MINIMUM_AGE"
      ></div>
      <div
        *ngIf="control?.errors?.['fileSize']"
        [translateParams]="{ maxSize: control?.errors?.['fileSize'].maxSize }"
        translate="FORM.ERROR_FILE_SIZE"
      ></div>
      <div
        *ngIf="control?.errors?.['fileType']"
        [translateParams]="{ allowedTypes: control?.errors?.['fileType'].allowedTypes.join(', ') }"
        translate="FORM.ERROR_FILE_TYPE"
      ></div>
      <div
        *ngIf="control?.errors?.['numberRange']"
        [translateParams]="{ min: control?.errors?.['numberRange'].min, max: control?.errors?.['numberRange'].max }"
        translate="FORM.ERROR_NUMBER_RANGE"
      ></div>
      <div
        *ngIf="control?.errors?.['percentage']"
        translate="FORM.ERROR_PERCENTAGE"
      ></div>
      <div
        *ngIf="control?.errors?.['alphanumeric']"
        translate="FORM.ERROR_ALPHANUMERIC"
      ></div>
      <div
        *ngIf="control?.errors?.['alphanumericWithSpaces']"
        translate="FORM.ERROR_ALPHANUMERIC_SPACES"
      ></div>
      <div
        *ngIf="control?.errors?.['min']"
        [translateParams]="{ min: control?.errors?.['min'].min }"
        translate="FORM.MIN_VALUE_ERROR"
      ></div>
      <div
        *ngIf="control?.errors?.['max']"
        [translateParams]="{ max: control?.errors?.['max'].max }"
        translate="FORM.MAX_VALUE_ERROR"
      ></div>
      <div
        *ngIf="control?.errors?.['pattern']"
        translate="FORM.PATTERN_ERROR"
      ></div>
      <div
        *ngIf="control?.errors?.['dateRangeInvalid']"
        class="text-danger"
        translate="FORM.ERROR_DATE_RANGE"
      ></div>
      <div
        *ngIf="control?.errors?.['integer']"
        class="text-danger"
        translate="FORM.ERROR_INT"
      ></div>

      <!-- Password validation errors -->
      <div
        *ngIf="control?.errors?.['passwordMinLength']"
        [translateParams]="{ requiredLength: control?.errors?.['passwordMinLength'].requiredLength }"
        translate="FORM.PASSWORD_MIN_LENGTH"
      ></div>
      <div
        *ngIf="control?.errors?.['passwordUppercase']"
        translate="FORM.PASSWORD_UPPERCASE_REQUIRED"
      ></div>
      <div
        *ngIf="control?.errors?.['passwordLowercase']"
        translate="FORM.PASSWORD_LOWERCASE_REQUIRED"
      ></div>
      <div
        *ngIf="control?.errors?.['passwordNumber']"
        translate="FORM.PASSWORD_NUMBER_REQUIRED"
      ></div>
      <div
        *ngIf="control?.errors?.['passwordSpecialChar']"
        translate="FORM.PASSWORD_SPECIAL_CHAR_REQUIRED"
      ></div>
      <div
        *ngIf="control?.errors?.['passwordLetter']"
        translate="FORM.PASSWORD_LETTER_REQUIRED"
      ></div>
      <div
        *ngIf="control?.errors?.['passwordMismatch']"
        translate="FORM.PASSWORD_MISMATCH"
      ></div>

      <!-- Saudi-specific validation errors -->
      <div
        *ngIf="control?.errors?.['saudiIban']"
        translate="FORM.ERROR_SAUDI_IBAN"
      ></div>
      <div
        *ngIf="control?.errors?.['saudiMobile']"
        translate="FORM.ERROR_SAUDI_MOBILE"
      ></div>
      <div
        *ngIf="control?.errors?.['saudiPassport']"
        translate="FORM.ERROR_SAUDI_PASSPORT"
      ></div>
      <div
        *ngIf="control?.errors?.['saudiNationalId']"
        translate="FORM.ERROR_SAUDI_NATIONAL_ID"
      ></div>
    </div>
  `,
})
export class ValidationMessagesComponent {
  @Input() control: AbstractControl | null = null;
  @Input() formSubmitted = false;
}
