import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({ providedIn: 'root' })
export class AuthService {
  private readonly ACCESS_TOKEN_KEY = 'auth_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private accessToken: string | undefined;
  private refreshToken: any | undefined;

  constructor(private router: Router) {
    // Load tokens from localStorage on service initialization
    this.accessToken = localStorage.getItem(this.ACCESS_TOKEN_KEY) || undefined;
    const refreshTokenStr = localStorage.getItem(this.REFRESH_TOKEN_KEY);
    if (refreshTokenStr) {
      try {
       // this.refreshToken = JSON.parse(refreshTokenStr);
      } catch (e) {
        console.error('Error parsing refresh token:', e);
      }
    }
  }

  login(tokens: { accessToken: string; refreshToken: any }): void {
    this.accessToken = tokens.accessToken;
    this.refreshToken = tokens.refreshToken;

    localStorage.setItem(this.ACCESS_TOKEN_KEY, tokens.accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, JSON.stringify(tokens.refreshToken));
  }

  logout(): void {
    this.accessToken = undefined;
    this.refreshToken = undefined;
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    this.router.navigate(['/auth/login']);
  }

  getAccessToken(): string | undefined {
    if (!this.accessToken) {
      this.accessToken = localStorage.getItem(this.ACCESS_TOKEN_KEY) || undefined;
    }
    return this.accessToken;
  }

  getRefreshToken(): any | undefined {
    if (!this.refreshToken) {
      const refreshTokenStr = localStorage.getItem(this.REFRESH_TOKEN_KEY);
      if (refreshTokenStr) {
        try {
          this.refreshToken = JSON.parse(refreshTokenStr);
        } catch (e) {
          console.error('Error parsing refresh token:', e);
        }
      }
    }
    return this.refreshToken;
  }

  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }
}
