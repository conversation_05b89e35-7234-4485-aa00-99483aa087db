.conflicts-popup {
  padding: 24px;
  .title {
    color: #00205a;
    font-size: 20px;
    font-weight: 500;
    line-height: 32px;
    padding: 0 24px 20px;
  }
  hr {
    margin: 0;
  }
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 20px 24px 12px;
  }

  .tag {
    display: flex;
    align-items: center;
    background-color: #f0f6ff;
    border: 1px solid #cce0ff;
    border-radius: 24px;
    padding: 10px;
    font-family: "Segoe UI", sans-serif;
    font-size: 14px;
    color: #002b55;

    .name-txt {
      color: #00205a;
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
    }
  }

  .remove-icon {
    cursor: pointer;

    width: 16px;
    height: 16px;
  }

  .btn-container {
    display: flex;
    justify-content: flex-end;
    margin-left: 24px;
    margin-bottom: 12px;
  }
}
