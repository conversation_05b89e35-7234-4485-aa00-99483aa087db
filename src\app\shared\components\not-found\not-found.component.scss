.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  box-sizing: border-box;
}

.not-found-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  animation: fadeInUp 0.6s ease-out;
}

.error-icon {
  margin-bottom: 30px;
  
  svg {
    animation: pulse 2s infinite;
  }
}

.error-message {
  margin-bottom: 40px;
  
  .error-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 16px;
    line-height: 1.2;
  }
  
  .error-description {
    font-size: 1.1rem;
    color: #7f8c8d;
    line-height: 1.6;
    margin: 0;
  }
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 30px;
  
  .back-button,
  .home-button {
    min-width: 140px;
  }
}

.help-text {
  p {
    font-size: 0.9rem;
    color: #95a5a6;
    margin: 0;
    line-height: 1.5;
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .not-found-content {
    padding: 40px 20px;
    margin: 10px;
  }
  
  .error-message .error-title {
    font-size: 2rem;
  }
  
  .error-icon svg {
    width: 150px;
    height: 150px;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
    
    .back-button,
    .home-button {
      width: 100%;
      max-width: 200px;
    }
  }
}

@media (max-width: 480px) {
  .not-found-container {
    padding: 10px;
  }
  
  .not-found-content {
    padding: 30px 15px;
  }
  
  .error-message .error-title {
    font-size: 1.8rem;
  }
  
  .error-icon svg {
    width: 120px;
    height: 120px;
  }
}
