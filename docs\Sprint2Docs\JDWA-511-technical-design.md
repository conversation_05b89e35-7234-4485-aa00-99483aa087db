# 🏗️ JDWA-511: Technical Design - Create Resolution Feature

## 📋 Overview

This document outlines the technical architecture and design decisions for implementing the "Create a Resolution - Fund Manager" feature following the established JadwaUI project patterns.

## 🎯 Architecture Alignment

### Following Established Patterns
- **Strategy Module Reference**: Using the fund-strategies module as the architectural blueprint
- **Standalone Components**: Angular 18+ standalone component architecture
- **Service-Oriented Design**: Business logic encapsulated in injectable services
- **Reactive Programming**: RxJS observables for data flow
- **Material Design**: Consistent UI components and patterns

## 🗂️ Module Structure

### File Organization
```
📁 src/app/features/resolutions/
├── 📄 resolutions.component.ts           # Main list component
├── 📄 resolutions.component.html         # List template
├── 📄 resolutions.component.scss         # List styles
├── 📄 resolutions.routes.ts              # Feature routing
├── 📁 components/
│   ├── 📄 resolution-dialog.component.ts    # Create/Edit dialog
│   ├── 📄 resolution-dialog.component.html  # Dialog template
│   ├── 📄 resolution-dialog.component.scss  # Dialog styles
│   └── 📄 resolution-details.component.ts   # Details view
├── 📁 services/
│   └── 📄 resolution.service.ts          # Business logic service
└── 📁 models/
    └── 📄 resolution.models.ts           # Data models and interfaces
```

## 📊 Data Models

### Core Resolution Interface
```typescript
export interface ResolutionDto {
  id: number;
  code: string;                    // Auto-generated: fund_code/year/seq_no
  fundId: number;
  fundName: string;
  resolutionDate: string;          // ISO date string
  description?: string;            // Optional, max 500 chars
  typeId: number;
  typeName: string;
  customTypeName?: string;         // When type is "Other"
  attachmentId: number;
  attachmentName: string;
  votingMethodologyId: number;     // 1: All members, 2: Majority
  votingResultCalculationId: number; // 1: All items, 2: Majority items
  statusId: number;                // 1: Draft, 2: Pending, 3: Approved, 4: Cancelled
  statusName: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  createdByRole: string;
}

export interface CreateResolutionCommand {
  fundId: number;
  resolutionDate: string;
  description?: string;
  typeId: number;
  customTypeName?: string;
  attachmentId: number;
  votingMethodologyId: number;
  votingResultCalculationId: number;
  saveAsDraft: boolean;            // true for draft, false for pending
}

export interface ResolutionType {
  id: number;
  nameAr: string;
  nameEn: string;
  isActive: boolean;
}

export interface VotingMethodology {
  id: number;
  nameAr: string;
  nameEn: string;
  isDefault: boolean;
}
```

### API Response Models
```typescript
export interface ResolutionListResponse {
  statusCode: number;
  successed: boolean;
  message: string;
  data: ResolutionDto[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  errors: string[];
}

export interface ResolutionCreateResponse {
  statusCode: number;
  successed: boolean;
  message: string;
  data: {
    id: number;
    code: string;
    statusId: number;
  };
  errors: string[];
}
```

## 🔧 Service Layer Design

### Resolution Service
```typescript
@Injectable({
  providedIn: 'root'
})
export class ResolutionService {
  private baseUrl = `${environment.apiUrl}/api/Resolutions`;

  constructor(private http: HttpClient) {}

  // CRUD Operations
  createResolution(command: CreateResolutionCommand): Observable<ResolutionCreateResponse> {
    return this.http.post<ResolutionCreateResponse>(`${this.baseUrl}/Create`, command);
  }

  updateResolution(id: number, command: CreateResolutionCommand): Observable<any> {
    return this.http.put(`${this.baseUrl}/Update/${id}`, command);
  }

  getResolutionById(id: number): Observable<ResolutionDto> {
    return this.http.get<ResolutionDto>(`${this.baseUrl}/GetById/${id}`);
  }

  getResolutionsByFund(fundId: number, params: {
    pageNo?: number;
    pageSize?: number;
    search?: string;
    statusId?: number;
    orderBy?: string;
  }): Observable<ResolutionListResponse> {
    let httpParams = new HttpParams();
    httpParams = httpParams.set('fundId', fundId.toString());
    
    Object.keys(params).forEach(key => {
      const value = params[key as keyof typeof params];
      if (value !== null && value !== undefined && value !== '') {
        httpParams = httpParams.set(key, value.toString());
      }
    });

    return this.http.get<ResolutionListResponse>(`${this.baseUrl}/GetByFund`, { params: httpParams });
  }

  cancelResolution(id: number): Observable<any> {
    return this.http.put(`${this.baseUrl}/Cancel/${id}`, {});
  }

  // Lookup data
  getResolutionTypes(): Observable<ResolutionType[]> {
    return this.http.get<ResolutionType[]>(`${this.baseUrl}/GetTypes`);
  }

  getVotingMethodologies(): Observable<VotingMethodology[]> {
    return this.http.get<VotingMethodology[]>(`${this.baseUrl}/GetVotingMethodologies`);
  }
}
```

## 🎨 Component Architecture

### Main Resolutions Component
```typescript
@Component({
  selector: 'app-resolutions',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    PageHeaderComponent,
    BreadcrumbComponent,
    TranslateModule
  ],
  templateUrl: './resolutions.component.html',
  styleUrl: './resolutions.component.scss'
})
export class ResolutionsComponent implements OnInit {
  // Data properties
  ELEMENT_DATA: ResolutionDto[] = [];
  tableDataSource = new MatTableDataSource<ResolutionDto>();
  totalCount = 0;
  currentFundId = 0;
  
  // UI state
  isDialogOpen = false;
  
  // Table configuration
  displayedColumns: string[] = ['code', 'resolutionDate', 'typeName', 'statusName', 'actions'];
  columns: ITableColumn[] = [
    {
      columnDef: 'code',
      header: 'RESOLUTIONS.CODE',
      columnType: ColumnTypeEnum.Text,
      cell: (element: ResolutionDto) => element.code
    },
    {
      columnDef: 'resolutionDate',
      header: 'RESOLUTIONS.DATE',
      columnType: ColumnTypeEnum.Date,
      cell: (element: ResolutionDto) => element.resolutionDate
    },
    {
      columnDef: 'typeName',
      header: 'RESOLUTIONS.TYPE',
      columnType: ColumnTypeEnum.Text,
      cell: (element: ResolutionDto) => element.typeName
    },
    {
      columnDef: 'statusName',
      header: 'RESOLUTIONS.STATUS',
      columnType: ColumnTypeEnum.Status,
      cell: (element: ResolutionDto) => element.statusName
    },
    {
      columnDef: 'actions',
      header: 'RESOLUTIONS.ACTIONS',
      columnType: ColumnTypeEnum.Actions,
      cell: () => ''
    }
  ];

  constructor(
    private resolutionService: ResolutionService,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private translateService: TranslateService,
    public tokenService: TokenService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.currentFundId = +params['fundId'];
      this.loadResolutions();
    });
  }

  loadResolutions(): void {
    this.resolutionService.getResolutionsByFund(this.currentFundId, {
      pageNo: 0,
      pageSize: 0,
      orderBy: 'CreatedAt desc'
    }).subscribe((response: ResolutionListResponse) => {
      this.ELEMENT_DATA = response.data;
      this.tableDataSource.data = response.data;
      this.totalCount = response.totalCount;
    });
  }

  onCreate(): void {
    if (this.isDialogOpen) return;
    this.isDialogOpen = true;

    const dialogRef = this.dialog.open(ResolutionDialogComponent, {
      width: '800px',
      data: { 
        isEdit: false, 
        fundId: this.currentFundId,
        existingResolutions: this.tableDataSource 
      }
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      this.isDialogOpen = false;
      if (result) {
        this.resolutionService.createResolution(result).subscribe((response: any) => {
          if (response.successed) {
            this.showSuccessMessage('RESOLUTIONS.CREATED_SUCCESSFULLY');
            this.loadResolutions();
          }
        });
      }
    });
  }

  private showSuccessMessage(messageKey: string): void {
    Swal.fire({
      icon: 'success',
      title: this.translateService.instant(messageKey),
      showConfirmButton: false,
      timer: 1500
    });
  }
}
```

## 🔄 Integration Points

### API Endpoints Required
```typescript
// Base URL: {environment.apiUrl}/api/Resolutions

POST   /Create                    // Create new resolution
PUT    /Update/{id}               // Update existing resolution
GET    /GetById/{id}              // Get resolution details
GET    /GetByFund                 // Get resolutions by fund (with pagination)
PUT    /Cancel/{id}               // Cancel pending resolution
GET    /GetTypes                  // Get resolution types lookup
GET    /GetVotingMethodologies    // Get voting methodologies lookup
```

### File Upload Integration
```typescript
// Existing file upload service integration
export class FileUploadService {
  uploadResolutionFile(file: File): Observable<{attachmentId: number, fileName: string}> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('category', 'resolution');
    
    return this.http.post<any>(`${this.baseUrl}/api/FileManagement/Upload`, formData);
  }
}
```

### Notification Integration
```typescript
// Integration with existing notification service
export interface NotificationRequest {
  recipientIds: number[];
  title: string;
  message: string;
  activityType: 'resolution' | 'fund' | 'member';
  relatedEntityId: number;
  relatedEntityType: string;
}
```

## 🛡️ Security Considerations

### Permission Checks
- `Resolution.Create`: Required for creating resolutions
- `Resolution.View`: Required for viewing resolutions
- `Resolution.Edit`: Required for editing draft/pending resolutions
- `Resolution.Cancel`: Required for cancelling pending resolutions

### Data Validation
- Server-side validation for all required fields
- File type and size validation
- Date range validation (fund initiation date to today)
- Business rule validation (fund status, user permissions)

## 🌐 Routing Configuration

```typescript
// resolutions.routes.ts
export const RESOLUTION_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => 
      import('./resolutions.component').then(m => m.ResolutionsComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'details/:id',
    loadComponent: () => 
      import('./components/resolution-details.component')
        .then(m => m.ResolutionDetailsComponent),
    canActivate: [AuthGuard]
  }
];

// Integration in main app routing
{
  path: 'funds/:fundId/resolutions',
  loadChildren: () => 
    import('./features/resolutions/resolutions.routes')
      .then(m => m.RESOLUTION_ROUTES),
  canActivate: [AuthGuard]
}
```

---

**📝 Note**: This technical design follows the established JadwaUI architectural patterns and integrates seamlessly with existing components and services.
