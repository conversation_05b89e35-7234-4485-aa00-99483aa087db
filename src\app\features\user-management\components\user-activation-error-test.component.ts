import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UserManagementErrorHandlerService } from '../services/user-management-error-handler.service';
import { ApiException, ProblemDetails } from '@core/api/api.generated';

@Component({
  selector: 'app-user-activation-error-test',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  template: `
    <div class="error-test-container p-4">
      <h2>{{ 'User Activation/Deactivation Error Handler Test' | translate }}</h2>
      
      <div class="test-section mb-4">
        <h3>Language Settings</h3>
        <button 
          class="btn btn-primary me-2" 
          (click)="switchLanguage('ar')"
          [class.active]="currentLang === 'ar'">
          العربية
        </button>
        <button 
          class="btn btn-primary" 
          (click)="switchLanguage('en')"
          [class.active]="currentLang === 'en'">
          English
        </button>
        <p class="mt-2">Current Language: {{ currentLang }}</p>
      </div>

      <div class="test-section mb-4">
        <h3>User Activation Error Tests</h3>
        <div class="row">
          <div class="col-md-6">
            <h4>Backend Error Message Tests</h4>
            <button class="btn btn-danger btn-sm me-2 mb-2" (click)="testBackendErrorMessage()">Backend Error Message</button>
            <button class="btn btn-danger btn-sm me-2 mb-2" (click)="testProblemDetailsError()">ProblemDetails Error</button>
            <button class="btn btn-danger btn-sm me-2 mb-2" (click)="testApiExceptionError()">ApiException Error</button>
            <button class="btn btn-danger btn-sm me-2 mb-2" (click)="testBaseResponseError()">BaseResponse Error</button>
          </div>
          
          <div class="col-md-6">
            <h4>Status Code Specific Tests</h4>
            <button class="btn btn-warning btn-sm me-2 mb-2" (click)="testConflictError()">409 Conflict</button>
            <button class="btn btn-warning btn-sm me-2 mb-2" (click)="testNotFoundError()">404 Not Found</button>
            <button class="btn btn-warning btn-sm me-2 mb-2" (click)="testValidationError()">422 Validation</button>
            <button class="btn btn-warning btn-sm me-2 mb-2" (click)="testServerError()">500 Server Error</button>
          </div>
        </div>
      </div>

      <div class="test-section mb-4">
        <h3>User Deactivation Error Tests</h3>
        <div class="row">
          <div class="col-md-6">
            <h4>Deactivation Scenarios</h4>
            <button class="btn btn-danger btn-sm me-2 mb-2" (click)="testDeactivationConflict()">Deactivation Conflict</button>
            <button class="btn btn-danger btn-sm me-2 mb-2" (click)="testDeactivationNotFound()">User Not Found</button>
            <button class="btn btn-danger btn-sm me-2 mb-2" (click)="testDeactivationConstraint()">System Constraint</button>
          </div>
          
          <div class="col-md-6">
            <h4>Success Messages</h4>
            <button class="btn btn-success btn-sm me-2 mb-2" (click)="testActivationSuccess()">Activation Success</button>
            <button class="btn btn-success btn-sm me-2 mb-2" (click)="testDeactivationSuccess()">Deactivation Success</button>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>Test Results</h3>
        <div class="alert alert-info">
          <p><strong>Expected Behavior:</strong></p>
          <ul>
            <li>Backend error messages should be extracted and displayed</li>
            <li>Fallback to contextual messages when backend message unavailable</li>
            <li>Proper Arabic/English language support with RTL/LTR text direction</li>
            <li>Status code specific icons (warning for 409, info for 404, error for others)</li>
            <li>User names should be interpolated in error messages</li>
            <li>Server errors (5xx) should show as toast notifications</li>
            <li>Client errors (4xx) should show as modal dialogs</li>
          </ul>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .error-test-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .test-section {
      border: 1px solid #ddd;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
    .btn.active {
      background-color: #28a745;
      border-color: #28a745;
    }
    .btn-sm {
      margin: 2px;
    }
  `]
})
export class UserActivationErrorTestComponent {
  currentLang = 'ar';

  constructor(
    private translateService: TranslateService,
    private userErrorHandler: UserManagementErrorHandlerService
  ) {
    this.currentLang = this.translateService.currentLang || 'ar';
  }

  switchLanguage(lang: string): void {
    this.currentLang = lang;
    this.translateService.use(lang);
    localStorage.setItem('lang', JSON.stringify(lang));
  }

  testBackendErrorMessage(): void {
    const mockError = new HttpErrorResponse({
      status: 400,
      statusText: 'Bad Request',
      url: 'https://api.example.com/activate-user',
      error: {
        message: 'User activation failed: The user account is already active and cannot be activated again.'
      }
    });

    this.userErrorHandler.handleActivationError(mockError, {
      operation: 'activate',
      userId: 123,
      userName: 'أحمد محمد'
    });
  }

  testProblemDetailsError(): void {
    const problemDetails = new ProblemDetails({
      type: 'https://api.jadwa.com/errors/user-activation-conflict',
      title: 'User Activation Conflict',
      status: 409,
      detail: 'Cannot activate user because they hold a role that conflicts with existing active users.',
      instance: '/api/users/activate/123'
    });

    const mockError = new HttpErrorResponse({
      status: 409,
      statusText: 'Conflict',
      url: 'https://api.example.com/activate-user',
      error: problemDetails
    });

    this.userErrorHandler.handleActivationError(mockError, {
      operation: 'activate',
      userId: 123,
      userName: 'سارة أحمد'
    });
  }

  testApiExceptionError(): void {
    const problemDetails = new ProblemDetails({
      type: 'https://api.jadwa.com/errors/validation',
      title: 'Validation Error',
      status: 422,
      detail: 'User activation request contains invalid data. Please check the user ID and try again.',
      instance: '/api/users/activate'
    });

    const apiException = new ApiException('Validation Error', 422, JSON.stringify(problemDetails), {}, problemDetails);
    
    const mockError = new HttpErrorResponse({
      status: 422,
      statusText: 'Unprocessable Entity',
      url: 'https://api.example.com/activate-user',
      error: apiException
    });

    this.userErrorHandler.handleActivationError(mockError, {
      operation: 'activate',
      userId: 456,
      userName: 'محمد علي'
    });
  }

  testBaseResponseError(): void {
    const baseResponse = {
      statusCode: 400,
      successed: false,
      message: 'User activation failed due to incomplete profile information. Please complete the user profile before activation.',
      data: null,
      errors: []
    };

    const mockError = new HttpErrorResponse({
      status: 400,
      statusText: 'Bad Request',
      url: 'https://api.example.com/activate-user',
      error: baseResponse
    });

    this.userErrorHandler.handleActivationError(mockError, {
      operation: 'activate',
      userId: 789,
      userName: 'فاطمة خالد'
    });
  }

  testConflictError(): void {
    const mockError = new HttpErrorResponse({
      status: 409,
      statusText: 'Conflict',
      url: 'https://api.example.com/activate-user',
      error: {
        message: 'Cannot activate user due to role conflicts with existing active users.'
      }
    });

    this.userErrorHandler.handleActivationError(mockError, {
      operation: 'activate',
      userId: 101,
      userName: 'عبدالله سعد'
    });
  }

  testNotFoundError(): void {
    const mockError = new HttpErrorResponse({
      status: 404,
      statusText: 'Not Found',
      url: 'https://api.example.com/activate-user',
      error: {
        message: 'User not found in the system.'
      }
    });

    this.userErrorHandler.handleActivationError(mockError, {
      operation: 'activate',
      userId: 999,
      userName: 'مستخدم غير موجود'
    });
  }

  testValidationError(): void {
    const mockError = new HttpErrorResponse({
      status: 422,
      statusText: 'Unprocessable Entity',
      url: 'https://api.example.com/activate-user',
      error: {
        message: 'Validation failed: User ID is required and must be a positive integer.'
      }
    });

    this.userErrorHandler.handleActivationError(mockError, {
      operation: 'activate',
      userId: -1,
      userName: 'مستخدم غير صحيح'
    });
  }

  testServerError(): void {
    const mockError = new HttpErrorResponse({
      status: 500,
      statusText: 'Internal Server Error',
      url: 'https://api.example.com/activate-user',
      error: {
        message: 'Internal server error occurred while processing user activation request.'
      }
    });

    this.userErrorHandler.handleActivationError(mockError, {
      operation: 'activate',
      userId: 202,
      userName: 'نورا حسن'
    });
  }

  testDeactivationConflict(): void {
    const mockError = new HttpErrorResponse({
      status: 409,
      statusText: 'Conflict',
      url: 'https://api.example.com/deactivate-user',
      error: {
        message: 'Cannot deactivate user because they are the only active administrator.'
      }
    });

    this.userErrorHandler.handleActivationError(mockError, {
      operation: 'deactivate',
      userId: 303,
      userName: 'خالد أحمد'
    });
  }

  testDeactivationNotFound(): void {
    const mockError = new HttpErrorResponse({
      status: 404,
      statusText: 'Not Found',
      url: 'https://api.example.com/deactivate-user',
      error: {
        message: 'User account not found or already deactivated.'
      }
    });

    this.userErrorHandler.handleActivationError(mockError, {
      operation: 'deactivate',
      userId: 404,
      userName: 'مستخدم محذوف'
    });
  }

  testDeactivationConstraint(): void {
    const mockError = new HttpErrorResponse({
      status: 409,
      statusText: 'Conflict',
      url: 'https://api.example.com/deactivate-user',
      error: {
        message: 'Cannot deactivate user due to active fund management responsibilities.'
      }
    });

    this.userErrorHandler.handleActivationError(mockError, {
      operation: 'deactivate',
      userId: 505,
      userName: 'ريم عبدالرحمن'
    });
  }

  testActivationSuccess(): void {
    this.userErrorHandler.showSuccess('activate', 'أحمد محمد');
  }

  testDeactivationSuccess(): void {
    this.userErrorHandler.showSuccess('deactivate', 'سارة أحمد');
  }
}
