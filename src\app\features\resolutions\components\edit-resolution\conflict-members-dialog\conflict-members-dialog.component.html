<div class="conflict-members-dialog">
  <div class="dialog-header">
    <h2 class="dialog-title">
      {{ 'INVESTMENT_FUNDS.RESOLUTIONS.CONFLICT_MEMBERS' | translate }}
    </h2>
    <!-- <button type="button" class="close-btn" (click)="onClose()" aria-label="Close">
      <i class="fas fa-times"></i>
    </button> -->
  </div>

  <div class="dialog-content">
    <!-- Item Title -->
    <!-- <div class="item-info">
      <h6 class="item-title">{{ itemTitle }}</h6>
      <p class="members-count">
        {{ 'INVESTMENT_FUNDS.RESOLUTIONS.CONFLICT_MEMBERS_COUNT' | translate }}: 
        <strong>{{ conflictMembers.length }}</strong>
      </p>
    </div> -->

    <!-- Members List -->
    <div class="members-list" *ngIf="conflictMembers.length > 0">
      <div class="member-item" *ngFor="let member of conflictMembers; let i = index">
        <!-- <div class="member-number">{{ i + 1 }}</div> -->
        <div class="member-details">
          <div class="member-name">{{ member.boardMemberName || member.userName }}</div>
          <!-- <div class="member-type" *ngIf="member.memberType">{{ member.memberType }}</div>
          <div class="conflict-notes" *ngIf="member.conflictNotes">
            <small class="notes-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.CONFLICT_NOTES' | translate }}:</small>
            <span class="notes-text">{{ member.conflictNotes }}</span>
          </div> -->
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="conflictMembers.length === 0">
      <i class="fas fa-users"></i>
      <p>{{ 'INVESTMENT_FUNDS.RESOLUTIONS.NO_CONFLICT_MEMBERS' | translate }}</p>
    </div>

    <!-- Actions -->
    <div class="dialog-actions">
      <button type="button" class="btn outline-btn" (click)="onClose()">
        <i class="fas fa-check"></i>
        {{ 'COMMON.CLOSE' | translate }}
      </button>
    </div>
  </div>
</div>
