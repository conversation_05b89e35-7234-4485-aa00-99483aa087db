# Tel Input Type Implementation

## Overview
The Tel input type has been implemented in the form-builder component to handle Saudi Arabia phone numbers with proper validation and formatting.

## Features

### 🇸🇦 Saudi Arabia Phone Number Support
- **Country Code**: Fixed +966 prefix with Saudi flag
- **Format**: 9-digit mobile numbers starting with 5
- **Validation**: Real-time validation for Saudi mobile number format
- **Input Restriction**: Only allows digits, automatically formats input

### Visual Design
- **Country Prefix**: Displays 🇸🇦 +966 in a styled prefix container
- **Input Field**: Clean input field for the 9-digit number
- **Validation States**: Visual feedback for valid/invalid states

## Usage

### 1. Form Control Configuration
```typescript
{
  formControlName: 'phoneNumber',
  type: InputType.Tel,
  id: 'phoneNumber',
  name: 'phoneNum<PERSON>',
  label: 'USER_MANAGEMENT.CREATE.PHONE_NUMBER',
  placeholder: 'FORM.PHONE_PLACEHOLDER', // Will show "5xxxxxxxx"
  isRequired: true,
  class: 'col-md-6'
}
```

### 2. Form Validation Setup
```typescript
import { saudiPhoneValidator } from '@shared/validators/saudi-phone.validator';

// In your form initialization
phoneNumber: ['', [Validators.required, saudiPhoneValidator()]]
```

### 3. Complete Example
```typescript
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { InputType } from '@shared/enum/input-type.enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { saudiPhoneValidator } from '@shared/validators/saudi-phone.validator';

@Component({
  selector: 'app-example',
  template: `
    <div class="form-container">
      <app-form-builder
        [formGroup]="exampleForm"
        [formControls]="formControls"
        [isFormSubmitted]="isFormSubmitted">
      </app-form-builder>
    </div>
  `
})
export class ExampleComponent {
  exampleForm: FormGroup;
  isFormSubmitted = false;

  formControls: IControlOption[] = [
    {
      formControlName: 'phoneNumber',
      type: InputType.Tel,
      id: 'phoneNumber',
      name: 'phoneNumber',
      label: 'Phone Number',
      placeholder: 'FORM.PHONE_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-6'
    }
  ];

  constructor(private formBuilder: FormBuilder) {
    this.exampleForm = this.formBuilder.group({
      phoneNumber: ['', [Validators.required, saudiPhoneValidator()]]
    });
  }
}
```

## Validation Rules

### Saudi Phone Number Format
- **Length**: Exactly 9 digits
- **First Digit**: Must be 5 (Saudi mobile numbers)
- **Valid Examples**: 
  - 501234567 (STC)
  - 551234567 (STC)
  - 561234567 (Mobily)
  - 581234567 (Zain)

### Input Behavior
- **Auto-correction**: If user types a different first digit, it's replaced with 5
- **Digit-only**: Only numeric input is allowed
- **Length Limit**: Maximum 9 digits
- **Real-time Validation**: Validates as user types

## Styling

### CSS Classes
```scss
.tel-input-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  
  .country-code-prefix {
    background-color: #f8f9fa;
    padding: 0 12px;
    border-right: 1px solid var(--border-color);
    
    .country-flag {
      margin-right: 6px;
    }
  }
  
  .tel-input {
    flex: 1;
  }
}
```

### Validation States
- **Valid**: Green border and success styling
- **Invalid**: Red border with error message
- **Focus**: Blue border with shadow

## Translation Keys

### English (en.json)
```json
{
  "FORM": {
    "PHONE_PLACEHOLDER": "5xxxxxxxx",
    "ERROR_SAUDI_PHONE": "Please enter a valid Saudi phone number (05xxxxxxxx)"
  }
}
```

### Arabic (ar.json)
```json
{
  "FORM": {
    "PHONE_PLACEHOLDER": "5xxxxxxxx",
    "ERROR_SAUDI_PHONE": "يرجى إدخال رقم هاتف سعودي صالح (05xxxxxxxx)"
  }
}
```

## Utility Functions

### Available Helper Functions
```typescript
import { 
  formatSaudiPhoneNumber, 
  validateAndFormatSaudiPhone,
  getSaudiNetworkProvider 
} from '@shared/validators/saudi-phone.validator';

// Format for display
const formatted = formatSaudiPhoneNumber('501234567');
// Result: "+966 50 123 4567"

// Validate and format
const result = validateAndFormatSaudiPhone('501234567');
// Result: { isValid: true, formatted: "+966 50 123 4567" }

// Get network provider
const provider = getSaudiNetworkProvider('501234567');
// Result: "STC"
```

## Network Providers
- **STC**: 50, 51, 52, 53, 54, 55
- **Mobily**: 56, 57
- **Zain**: 58, 59

## Implementation Notes

### Form Builder Integration
- The Tel input type is fully integrated with the form-builder component
- Supports all standard form-builder features (validation, events, styling)
- Works with both LTR and RTL layouts

### Event Handling
- `onTelKeyPressed`: Handles key press validation
- `onTelValueChange`: Handles value formatting and validation
- Standard form-builder events are also supported

### Accessibility
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly

This implementation ensures consistent Saudi phone number handling across all forms in the application while maintaining the existing form-builder architecture and styling patterns.
