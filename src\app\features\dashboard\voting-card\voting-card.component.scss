@import "../../../../assets/scss/variables";

.voting-container {
  background-color: $card-background ;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 24px;
  max-width: 400px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.voting-header {
  text-align: right;
  margin-bottom: 24px;


  .user-info {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 16px;

    .profile-image {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      margin-left: 12px;
    }

    .user-name {
      font-size: 16px;
      span{
        font-weight: 300;
      }
    }
  }

  hr{
    color:$border-color;
    border: 1px solid;
  }


  .voting-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
    text-align: center;

  }
}

.voting-content {
  text-align: center;

  .remaining-elements {
    font-size: 16px;
    color: #333;
    margin-bottom: 16px;
  }

  .vote-button {
    background-color: #0f3c6c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 12px 32px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover:not(:disabled) {
      background-color: #0a2d52;
    }

    &:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
  }
}
