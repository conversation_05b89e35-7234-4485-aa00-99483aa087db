# 🏗️ JadwaUI Project Architecture Analysis

## 📋 Table of Contents
- [Project Overview](#project-overview)
- [Architecture Patterns](#architecture-patterns)
- [Project Structure](#project-structure)
- [Module Organization](#module-organization)
- [Technology Stack](#technology-stack)

## 🎯 Project Overview

JadwaUI is a modern Angular 18+ application built with TypeScript, following enterprise-grade architectural patterns. The application implements a comprehensive investment fund management system with features including:

- **Fund Management**: Investment fund creation, editing, and monitoring
- **Strategy Management**: Fund strategy configuration and management
- **User Authentication**: JWT-based authentication with role-based access control
- **Voting System**: Governance and voting functionality
- **Dashboard Analytics**: Real-time data visualization and reporting

## 🏛️ Architecture Patterns

### Core Architectural Principles

| Principle | Implementation | Benefits |
|-----------|----------------|----------|
| **🧩 Modular Design** | Feature-based standalone components with lazy loading | Improved performance, maintainability |
| **🔄 Separation of Concerns** | Clear boundaries between core, features, and shared modules | Better code organization, testability |
| **⚡ Reactive Programming** | RxJS observables throughout the application | Efficient async operations, real-time updates |
| **🎨 Component-Based** | Reusable UI components with consistent design | Code reusability, consistent UX |
| **🔧 Service-Oriented** | Business logic encapsulated in injectable services | Loose coupling, dependency injection |
| **🛡️ Security-First** | JWT authentication with HTTP interceptors | Secure API communication |

### Design Patterns Used

1. **Repository Pattern**: Service layer abstracts data access
2. **Observer Pattern**: RxJS observables for reactive programming
3. **Dependency Injection**: Angular's built-in DI container
4. **Interceptor Pattern**: HTTP request/response interception
5. **Guard Pattern**: Route protection and access control
6. **Factory Pattern**: Dynamic component and service creation

## 📁 Project Structure

```
📁 src/app/
├── 🏗️ core/                    # Core functionality & infrastructure
│   ├── 🔌 api/                # Generated API clients (NSwag)
│   │   ├── api.generated.ts   # Auto-generated API service proxies
│   │   └── api.module.ts      # API module configuration
│   ├── 🛡️ guards/             # Route guards & access control
│   │   └── auth.guard.ts      # Authentication guard
│   ├── 🔄 interceptors/       # HTTP interceptors
│   │   ├── token.interceptor.ts    # JWT token injection
│   │   ├── error.interceptor.ts    # Global error handling
│   │   └── auth-interceptors/      # Authentication interceptors
│   ├── 🎨 layout/             # Layout components & shells
│   │   └── components/        # Layout component implementations
│   ├── ⚙️ services/           # Core business services
│   │   ├── strategy.service.ts     # Strategy management service
│   │   └── signalr.service.ts      # Real-time communication
│   ├── 📝 enums/              # Application-wide enumerations
│   ├── 🔧 gl-services/        # Global utility services
│   │   ├── language-services/ # Internationalization
│   │   ├── spinner-services/  # Loading indicators
│   │   └── notification-services/ # User notifications
│   └── 🎭 gl-interfaces/      # Global interfaces and types
├── 🎯 features/               # Feature modules (lazy-loaded)
│   ├── 🔐 auth/               # Authentication & user management
│   │   ├── components/        # Auth-related components
│   │   └── services/          # Authentication services
│   ├── 📊 dashboard/          # Main dashboard & analytics
│   ├── 💰 investment-funds/   # Fund management & operations
│   │   ├── components/        # Fund-related components
│   │   ├── services/          # Fund business logic
│   │   └── investment-funds.routes.ts # Feature routing
│   ├── 📈 fund-strategies/    # Strategy configuration & management
│   │   ├── fund-strategies.component.ts # Main strategy component
│   │   └── fund-strategies.module.ts    # Strategy module
│   ├── 🗳️ voting/             # Voting system & governance
│   └── 📋 strategy/           # Strategy dialog components
└── 🔗 shared/                 # Shared components & utilities
    ├── 🎨 components/         # Reusable UI components
    │   ├── table/             # Data table component
    │   ├── page-header/       # Page header component
    │   ├── breadcrumb/        # Navigation breadcrumb
    │   ├── validation/        # Form validation components
    │   └── form-builder/      # Dynamic form builder
    ├── 🔧 services/           # Shared utility services
    ├── 📊 interfaces/         # Shared type definitions
    ├── 🎭 enums/              # Shared enumerations
    └── 🧩 pipes/              # Custom Angular pipes
```

## 🧩 Module Organization

### Feature Module Structure

Each feature module follows a consistent structure:

```
📁 feature-name/
├── 📄 feature-name.component.ts     # Main feature component
├── 📄 feature-name.component.html   # Component template
├── 📄 feature-name.component.scss   # Component styles
├── 📄 feature-name.module.ts        # Feature module definition
├── 📄 feature-name.routes.ts        # Feature-specific routing
├── 📁 components/                   # Feature-specific components
├── 📁 services/                     # Feature business logic
└── 📁 models/                       # Feature data models
```

### Lazy Loading Implementation

Features are lazy-loaded using Angular's dynamic imports:

```typescript
// app.routes.ts
{
  path: 'investment-funds',
  loadChildren: () => import('./features/investment-funds/investment-funds.routes')
    .then(m => m.INVESTMENT_FUNDS_ROUTES),
  canActivate: [AuthGuard]
}
```

## 🛠️ Technology Stack

### Core Technologies
- **Angular 18+**: Modern Angular framework with standalone components
- **TypeScript 5+**: Type-safe JavaScript development
- **RxJS 7+**: Reactive programming with observables
- **Angular Material**: UI component library
- **SCSS**: Enhanced CSS with variables and mixins

### Development Tools
- **NSwag**: Automatic API client generation from Swagger/OpenAPI
- **Angular CLI**: Project scaffolding and build tools
- **ESLint**: Code linting and quality enforcement
- **Prettier**: Code formatting and style consistency

### Third-Party Libraries
- **SweetAlert2**: Beautiful alert dialogs
- **ngx-translate**: Internationalization (i18n)
- **ngx-toastr**: Toast notifications
- **Firebase**: Push notifications and analytics

### Build & Deployment
- **Webpack**: Module bundling and optimization
- **Angular Universal**: Server-side rendering (SSR) ready
- **Environment Configuration**: Multi-environment support (dev, test, prod)

---

*This document provides a high-level overview of the project architecture. For detailed implementation guides, see the following documentation files:*

- [Strategy Module Deep Dive](./strategy-module-deep-dive.md)
- [Generic Module Creation Guide](./generic-module-creation-guide.md)
- [Implementation Examples](./implementation-examples.md)
