import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { AuthService } from 'src/app/features/auth/services/auth-service/auth.service';

export const AuthInterceptor: HttpInterceptorFn = (req, next) => {

  const authService = inject(AuthService);
  const token = localStorage.getItem('auth_token');


  const lang = localStorage.getItem('lang') || 'ar';
  let headers = req.headers;

  if (token) {
    headers = headers.set('Authorization', `Bearer ${token}`);
  }
  headers = headers.set('Accept-Language', lang);

  const clonedRequest = req.clone({ headers });

  return next(clonedRequest);
};
