@import "../../../../../../assets/scss/variables";
.form-control:focus{
  box-shadow: none !important;
}

.header-logo {
  img {
    height: 32px;
    width: auto;
  }
}

.header {
  color: $navy-blue;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}
.header-padding {
  // padding-top: 2rem;
  padding: 1.75rem;
}
// Mobile styles
@media (max-width: 991.98px) {
  .search-section {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .search-container .search-input {
    height: 44px;
    font-size: 14px;
  }

  .search-container .search-btn {
    width: 28px;
    height: 28px;
    right: 10px;

    svg {
      width: 16px;
      height: 15px;
    }
  }
}

// Notification badge styles are defined later in the file

// Search Section Styles
.search-section {
  width: 263px;
  position: relative;
}

.search-container {
  position: relative;
  display: flex;
  width: 263px;
  padding: 0px 12px;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
  border: 2px solid #D1D1D1;
  border-radius: 12px;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 10px;

  .search-input {
    flex: 1;
    height: 48px;
    padding: 0;
    border: none;
    background: transparent;
    font-size: 16px;
    color: #333;
    &::placeholder {
      color: #999;
      font-weight: 400;
    }

    &:focus {
      outline: none;
    }
  }

  .search-btn {
    background: none;
    border: none;
    color: #4f4f4f;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
    flex-shrink: 0;

    svg {
      width: 19px;
      height: 18px;
    }

    &:hover {
      background: rgba(0, 32, 90, 0.1);

      svg path {
        fill: $navy-blue;
      }
    }
  }
}

.create-fund-btn {
  height: 40px;
  padding: 0 16px;
  background-color: $navy-blue;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  white-space: nowrap;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }

  i {
    font-size: 12px;
  }

  &:hover {
    background-color: darken($navy-blue, 5%);
  }
}

.notification-badge {
  position: absolute;
  right: 9.25px;
  top: 3px;
  background-color: $notification-badg;
  border-radius: 50%;
  display: flex;
  padding: 2px 3px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #fff;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  object-fit: cover;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.lang-btn {
  display: flex;
  align-items: self-start;
  gap: 8px;
  font-size: 20px;
  border-color: #00205a;
  padding: 3px 12px;
  border: 1px solid;
  height: 32px;

  span {
    line-height: 1rem;
  }
}

.menu-toggle {
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: white;

  &:hover {
    background-color: #f8f9fa;
  }

  img {
    width: 24px;
    height: 24px;
  }
}

.user-profile-section {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(0, 32, 90, 0.05);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  img {
    transition: all 0.3s ease;
  }

  &:hover img {
    box-shadow: 0 2px 8px rgba(0, 32, 90, 0.2);
  }
  span{
    text-wrap-mode: nowrap;
  }
}

// Language Dropdown Styles
.lang-btn {
  background: transparent;
  border: 1px solid #E0E0E0;
  color: $navy-blue;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f8f9fa;
    border-color: $navy-blue;
  }

  &:focus {
    box-shadow: none;
    border-color: $navy-blue;
  }

  .language-flag {
    width: 16px;
    height: 16px;
    object-fit: cover;
    border-radius: 2px;
  }

  .language-text {
    font-size: 14px;
    font-weight: 500;
  }
}

.dropdown-menu {
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 8px 0;
  min-width: 140px;

  .dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
    color: $navy-blue;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
      color: $navy-blue;
    }

    &.active {
      background-color: $navy-blue;
      color: white;

      .language-flag svg path {
        opacity: 0.9;
      }
    }

    .language-flag {
      width: 16px;
      height: 16px;
      object-fit: cover;
      border-radius: 2px;
    }
  }
}
