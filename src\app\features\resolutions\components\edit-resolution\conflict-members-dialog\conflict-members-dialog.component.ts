import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';

// API Generated Types
import { ResolutionItemConflictDto } from '@core/api/api.generated';

export interface ConflictMembersDialogData {
  itemTitle: string;
  conflictMembers: ResolutionItemConflictDto[];
}

@Component({
  selector: 'app-conflict-members-dialog',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule
  ],
  templateUrl: './conflict-members-dialog.component.html',
  styleUrl: './conflict-members-dialog.component.scss'
})
export class ConflictMembersDialogComponent {
  itemTitle: string;
  conflictMembers: ResolutionItemConflictDto[];

  constructor(
    public dialogRef: MatDialogRef<ConflictMembersDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConflictMembersDialogData
  ) {
    this.itemTitle = data.itemTitle;
    this.conflictMembers = data.conflictMembers || [];
  }

  onClose(): void {
    this.dialogRef.close();
  }
}
