# SCSS Import Paths Reference

## Form Container Import Paths

Use the correct relative path based on your component's location:

### Component Location Examples

#### 1. Features > Module > Component
**Location**: `src/app/features/user-management/create-user/`
**Import**: 
```scss
@import '../../../shared/styles/form-container';
```

#### 2. Features > Module > Components > Component  
**Location**: `src/app/features/investment-funds/components/create-fund/`
**Import**:
```scss
@import '../../../../shared/styles/form-container';
```

#### 3. Shared > Components > Component
**Location**: `src/app/shared/components/example/`
**Import**:
```scss
@import '../../styles/form-container';
```

#### 4. Core > Components > Component
**Location**: `src/app/core/components/example/`
**Import**:
```scss
@import '../../../shared/styles/form-container';
```

### Path Calculation Guide

To calculate the correct path:
1. Count the directory levels from your component to `src/app/`
2. Add `shared/styles/form-container` to the path
3. Use `../` for each level up

**Example**:
- Component: `src/app/features/module/components/component/component.scss`
- Levels up to `src/app/`: 4 levels (`../../../../`)
- Full path: `../../../../shared/styles/form-container`

### Common Patterns

```scss
// Most common patterns:

// For features/module/component/
@import '../../../shared/styles/form-container';

// For features/module/components/component/
@import '../../../../shared/styles/form-container';

// For shared/components/component/
@import '../../styles/form-container';
```

### Troubleshooting

If you get import errors:
1. Check the file exists: `src/app/shared/styles/_form-container.scss`
2. Verify your component's directory structure
3. Count the `../` needed to reach `src/app/`
4. Ensure the path ends with `shared/styles/form-container` (no file extension)

### Alternative: Angular SCSS Configuration

You can also configure Angular to use absolute paths by adding to `angular.json`:

```json
{
  "projects": {
    "your-app": {
      "architect": {
        "build": {
          "options": {
            "stylePreprocessorOptions": {
              "includePaths": ["src/app"]
            }
          }
        }
      }
    }
  }
}
```

Then use:
```scss
@import 'shared/styles/form-container';
```

### Current Working Examples

✅ **create-fund component**:
```scss
@import '../../../../shared/styles/form-container';
```

✅ **create-user component**:
```scss
@import '../../../shared/styles/form-container';
```
