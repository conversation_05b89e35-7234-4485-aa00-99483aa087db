import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AlertType } from '@core/enums/alert-type';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ErrorModalService {
  constructor() {}

  private _alert$ = new BehaviorSubject<{ message: string; type: AlertType } | null>(null);
  alert$ = this._alert$.asObservable();

  showSuccess(message: string): void {
    this._alert$.next({ message, type: AlertType.Success });
    this.autoClear();
  }

  showError(message: string): void {
    this._alert$.next({ message, type: AlertType.Error });
    this.autoClear();
  }

  private autoClear() {
    setTimeout(() => this._alert$.next(null), 3000);
  }
}
