import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MatDialogModule } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';

import { ResolutionsComponent } from './resolutions.component';
import { ResolutionService } from './services/resolution.service';
import { TokenService } from '../auth/services/token.service';

describe('ResolutionsComponent', () => {
  let component: ResolutionsComponent;
  let fixture: ComponentFixture<ResolutionsComponent>;
  let mockResolutionService: jasmine.SpyObj<ResolutionService>;
  let mockTokenService: jasmine.SpyObj<TokenService>;

  beforeEach(async () => {
    const resolutionServiceSpy = jasmine.createSpyObj('ResolutionService', [
      'getResolutionsByFund', 
      'createResolution', 
      'updateResolution',
      'getResolutionById'
    ]);
    
    const tokenServiceSpy = jasmine.createSpyObj('TokenService', ['hasPermission']);

    await TestBed.configureTestingModule({
      imports: [
        ResolutionsComponent,
        HttpClientTestingModule,
        MatDialogModule,
        TranslateModule.forRoot()
      ],
      providers: [
        { provide: ResolutionService, useValue: resolutionServiceSpy },
        { provide: TokenService, useValue: tokenServiceSpy },
        {
          provide: ActivatedRoute,
          useValue: { params: of({ fundId: '1' }) }
        }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ResolutionsComponent);
    component = fixture.componentInstance;
    mockResolutionService = TestBed.inject(ResolutionService) as jasmine.SpyObj<ResolutionService>;
    mockTokenService = TestBed.inject(TokenService) as jasmine.SpyObj<TokenService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load resolutions on init', () => {
    const mockResponse = {
      statusCode: 200,
      successed: true,
      message: 'Success',
      data: [
        { 
          id: 1, 
          code: 'RES-001', 
          fundId: 1,
          fundName: 'Test Fund',
          resolutionDate: '2024-01-15',
          typeName: 'Test Type',
          statusName: 'Draft'
        }
      ],
      totalCount: 1,
      currentPage: 1,
      pageSize: 10,
      errors: []
    };
    
    mockResolutionService.getResolutionsByFund.and.returnValue(of(mockResponse));

    component.ngOnInit();

    expect(mockResolutionService.getResolutionsByFund).toHaveBeenCalledWith(1, jasmine.any(Object));
    expect(component.ELEMENT_DATA).toEqual(mockResponse.data);
    expect(component.totalCount).toBe(1);
  });

  it('should handle permission check', () => {
    mockTokenService.hasPermission.and.returnValue(true);
    
    const hasPermission = component.tokenService.hasPermission('Resolution.Create');
    
    expect(hasPermission).toBe(true);
    expect(mockTokenService.hasPermission).toHaveBeenCalledWith('Resolution.Create');
  });

  it('should update breadcrumb with fund name', () => {
    component.currentFundId = 1;
    component.currentFundName = 'Test Fund';
    
    component['updateBreadcrumb']();
    
    expect(component.breadcrumbItems).toEqual([
      { label: 'BREADCRUMB.HOME', routerLink: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', routerLink: '/admin/investment-funds' },
      { label: 'Test Fund', routerLink: '/admin/investment-funds/1' },
      { label: 'RESOLUTIONS.TITLE', routerLink: '' }
    ]);
  });

  it('should load mock data on service error', () => {
    mockResolutionService.getResolutionsByFund.and.returnValue(
      of().pipe(() => { throw new Error('Service error'); })
    );

    spyOn(component as any, 'loadMockData');
    spyOn(component as any, 'showErrorMessage');

    component.loadResolutions();

    expect(component['showErrorMessage']).toHaveBeenCalledWith('RESOLUTIONS.ERROR_LOADING');
    expect(component['loadMockData']).toHaveBeenCalled();
  });
});
