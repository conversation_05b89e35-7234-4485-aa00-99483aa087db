<div [ngClass]="['custom-alert', alertType + '-alert']" [ngStyle]="{ position: isStaticPosition ? 'static' : 'fixed',
   width: isStaticPosition ? '100%' : '70%'
}" *ngIf="showAlert">
  <div class="alert-icon">
    <img [src]="'assets/images/' + alertType + '.png'" [alt]="alertType" />
  </div>
  <div class="alert-text">
    {{ msg }}
  </div>

  <img *ngIf="hasClose" src="assets/icons/cancel-icon.png" (click)="close()" alt="close" style="cursor: pointer;" />
</div>
