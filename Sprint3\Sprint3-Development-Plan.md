# Sprint 3 Development Plan - Jadwa Fund Management System

## Executive Summary

This document outlines the comprehensive development plan for implementing Sprint 3 user management features in the Jadwa Fund Management System. The plan focuses on building a complete user administration system with profile management, user lifecycle operations, and advanced administrative functions.

## Current State Analysis

### Existing Implementation
- **User Management Service**: Basic service exists with limited functionality for fund-specific user retrieval
- **Member Management**: Existing member management for fund boards (not system users)
- **Form Builder**: Robust form-builder component with Tel input and Saudi phone validation
- **Shared Components**: Established pattern for form containers and shared styling
- **API Integration**: Generated API proxies exist for user management endpoints

### Missing Components
- **System User Management UI**: No dedicated user management interface for administrators
- **User Profile Management**: No self-service profile management for users
- **User Lifecycle Operations**: Missing activation/deactivation, password reset, message resending
- **Advanced User Features**: No filtering, search, or bulk operations
- **Role Management UI**: No interface for complex role assignment logic

## Sprint 3 Requirements Overview

### Core User Stories
1. **JDWA-1280**: Manage Personal Profile - Self-service profile management
2. **JDWA-1223**: Add New System User - Administrative user creation with role logic
3. **JDWA-1213**: View System Users List - Comprehensive user listing with actions
4. **Additional Stories**: User filtering, password reset, message resending, activation/deactivation

### Key Features Required
- **User List Management**: Paginated, sortable, filterable user list
- **User CRUD Operations**: Create, read, update, delete users with validation
- **Role Management**: Complex role assignment with single-holder role logic
- **Status Management**: User activation/deactivation with business rules
- **Profile Management**: Self-service profile updates with file uploads
- **Administrative Functions**: Password reset, message resending, bulk operations

## Development Phases

### Phase 1: Gap Analysis & Architecture Setup (5 days)
**Objective**: Establish foundation for user management implementation

#### Tasks:
1. **Gap Analysis** (1 day)
   - Document current vs required features
   - Identify integration points with existing systems
   - Define data flow and API requirements

2. **Module Structure Setup** (1 day)
   - Create user-management feature module
   - Set up routing and lazy loading
   - Establish component hierarchy

3. **Enhanced Interfaces & DTOs** (2 days)
   - Create comprehensive user interfaces
   - Define registration flag models
   - Establish role management interfaces

4. **Service Enhancement** (1 day)
   - Extend UserManagementService
   - Add CRUD operations support
   - Implement status management methods

### Phase 2: Core User Management Components (12 days)
**Objective**: Implement primary user management UI components

#### Tasks:
1. **User List Component** (3 days) ✅ COMPLETED (July 15, 2025)
   - ✅ Implement paginated user list
   - ✅ Add sorting and advanced filtering with hybrid approach
   - ✅ Implement conditional action buttons based on registration flags
   - **Acceptance Criteria**: ✅ ALL MET
     - ✅ Display all users with pagination
     - ✅ Sort by Last Update Date and other columns
     - ✅ Show/hide action buttons based on user status and registration flags
     - ✅ Handle empty states and errors
     - ✅ Implement hybrid filtering (inline mobile + popup for other filters)
   - **Implementation Details**:
     - Created `UserListComponent` with full table integration
     - Implemented conditional action button logic per Sprint 3 specifications
     - Added `SearchInputComponent` for mobile number filtering
     - Created `UserFilterDialogComponent` for advanced filtering
     - Updated user interface models with registration flags

2. **Create User Component** (3 days)
   - Implement user creation form
   - Add role selection logic
   - Implement single-holder role validation
   - **Acceptance Criteria**:
     - Validate all mandatory fields
     - Handle role selection constraints
     - Show replacement confirmation for single-holder roles
     - Generate default passwords

3. **Edit User Component** (2 days)
   - Implement user editing form
   - Handle non-editable fields
   - Implement role updates
   - **Acceptance Criteria**:
     - Pre-populate form with user data
     - Prevent editing of Mobile No.
     - Validate role changes

4. **User Profile Component** (3 days)
   - Implement self-service profile management
   - Add file upload for CV and photos
   - Implement password change link
   - **Acceptance Criteria**:
     - Allow users to update personal information
     - Handle file uploads with validation
     - Link to password management

5. **User Detail View** (1 day)
   - Implement read-only user details
   - Display comprehensive user information
   - **Acceptance Criteria**:
     - Show all user attributes
     - Display role assignments
     - Show registration status

### Phase 3: Advanced User Management Features (8 days)
**Objective**: Implement advanced administrative and user features

#### Tasks:
1. **User Activation/Deactivation** (2 days)
   - Implement status change functionality
   - Add single-holder role protection
   - **Acceptance Criteria**:
     - Prevent deactivation of single-holder roles
     - Show confirmation dialogs
     - Update user status correctly

2. **Password Reset Functionality** (2 days)
   - Implement admin password reset
   - Add eligibility validation
   - **Acceptance Criteria**:
     - Only show for eligible users
     - Generate secure passwords
     - Send notifications

3. **Registration Message Resending** (2 days)
   - Implement WhatsApp message resending
   - Add eligibility checks
   - **Acceptance Criteria**:
     - Validate user eligibility
     - Handle API failures gracefully
     - Update message flags

4. **Advanced Filtering & Search** (1 day)
   - Implement role-based filtering
   - Add text search functionality
   - **Acceptance Criteria**:
     - Filter by role, status, registration flags
     - Search by name, email
     - Combine multiple filters

5. **File Upload Management** (1 day)
   - Implement CV and photo uploads
   - Add file validation and storage
   - **Acceptance Criteria**:
     - Validate file types and sizes
     - Handle upload errors
     - Store files securely

### Phase 4: Testing & Integration (6 days)
**Objective**: Ensure quality and performance of user management system

#### Tasks:
1. **Unit Testing** (2 days)
   - Test all components and services
   - Test form validation logic
   - Test role management logic

2. **Integration Testing** (2 days)
   - Test complete workflows
   - Test API integrations
   - Test error scenarios

3. **E2E Testing** (1 day)
   - Test admin workflows
   - Test user self-service workflows
   - Test edge cases

4. **Performance Optimization** (0.5 days)
   - Optimize user list performance
   - Implement efficient data loading

5. **Documentation & Deployment** (0.5 days)
   - Create user documentation
   - Prepare deployment scripts

## Technical Considerations

### Form Builder Integration
- Leverage existing form-builder patterns
- Use Tel input type for Saudi phone validation
- Follow shared styling patterns (form-container.scss)

### Validation Strategy
- Client-side validation using Angular reactive forms
- Server-side validation for security
- Saudi phone number validation using existing validator

### Role Management Logic
- Multi-select enabled only for specific role combinations
- Single-holder role validation and replacement workflow
- Dynamic UI based on role selection

### File Upload Strategy
- CV files: PDF/DOCX, max 10MB
- Profile photos: JPG/PNG, max 2MB
- Secure file storage integration

### Performance Considerations
- Virtual scrolling for large user lists
- Lazy loading of user details
- Efficient API pagination and filtering

## Risk Assessment & Mitigation

### High Risks
1. **Complex Role Logic**: Mitigation - Comprehensive testing and clear documentation
2. **File Upload Security**: Mitigation - Strict validation and secure storage
3. **Performance with Large Datasets**: Mitigation - Pagination and virtual scrolling

### Medium Risks
1. **API Integration Issues**: Mitigation - Mock services for development
2. **UI Complexity**: Mitigation - Incremental development and testing

## Resource Allocation

### Development Team
- **Frontend Developer**: 2 developers for UI components
- **Backend Integration**: 1 developer for API integration
- **QA Engineer**: 1 tester for comprehensive testing

### Timeline
- **Total Duration**: 31 days
- **Phase 1**: Days 1-5
- **Phase 2**: Days 6-17
- **Phase 3**: Days 18-25
- **Phase 4**: Days 26-31

## Success Criteria

### Functional Requirements
- All Sprint 3 user stories implemented and tested
- Complete user lifecycle management
- Self-service profile management
- Administrative user management functions

### Non-Functional Requirements
- Performance: User list loads within 2 seconds
- Security: All file uploads validated and secure
- Usability: Intuitive UI following existing design patterns
- Accessibility: WCAG 2.1 AA compliance

## Dependencies

### External Dependencies
- WhatsApp Business API for message sending
- File storage service (Azure Blob or similar)
- Backend API endpoints for user management

### Internal Dependencies
- Existing form-builder component
- Shared styling patterns
- Authentication and authorization system

## Conclusion

This development plan provides a structured approach to implementing Sprint 3 user management features. The phased approach ensures incremental delivery while maintaining quality and performance standards. The plan leverages existing components and patterns while introducing new functionality required for comprehensive user management.
