import { Directive, HostListener } from '@angular/core';
import { NgControl } from '@angular/forms';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { LanguageService } from '@core/gl-services/language-services/language.service';

@Directive({
  selector: '[appArabdtNumberConvert]',
  standalone: true
})
export class ArabdtNumberConvertDirective {
 
  constructor(private ngControl: NgControl, private languageService: LanguageService) {}

  // Host listener for input events
  @HostListener('input', ['$event'])
  onInput(event: Event): void {
    const inputElement = event.target as HTMLInputElement;

    // Check the current language
    const currentLang = JSON.parse(localStorage.getItem('lang') || LanguageEnum.ar);

    // Convert only if the language is Arabic
    if (currentLang === 'ar') {
      inputElement.value = this.convertToArabic(inputElement.value);

      // Update the form control value if using reactive forms
      if (this.ngControl?.control) {
        this.ngControl.control.setValue(inputElement.value);
      }
    }
  }

  private convertToArabic(input: string): string {
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return input.replace(/\d/g, (digit) => arabicNumerals[+digit]);
  }
}
