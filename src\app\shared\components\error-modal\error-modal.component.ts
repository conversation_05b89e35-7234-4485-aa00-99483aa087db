import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
} from '@angular/material/dialog';

interface DialogData {
  message: string;
  isError?: boolean;
}

@Component({
  selector: 'app-error-modal',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="modal-container p-4 text-center">
      <button class="close-button" (click)="close()">
        <i class="fa fa-times"></i>
      </button>

      <div class="icon-container mb-3">
        <img
          [src]="
            data.isError
              ? 'assets/images/error-icon.png'
              : 'assets/images/success.png'
          "
          [alt]="data.isError ? 'Error' : 'Success'"
        />
      </div>
      <div class="d-flex justify-content-center">
        <p class="message" [dir]="'rtl'">{{ data.message }}</p>
      </div>
    </div>
  `,
  styles: [
    `
      .modal-container {
        width: 600px;
        padding: 16px 36px;
        border-radius: 8px;
        background: #ffffff;
        position: relative;
      }

      .close-button {
        position: absolute;
        top: 16px;
        right: 16px;
        border: none;
        background: none;
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.7;
        transition: opacity 0.2s;

        &:hover {
          opacity: 1;
        }

        i {
          font-size: 16px;
          color: #002054;
        }
      }

      .icon-container {
        display: flex;
        justify-content: center;
        margin-top: 16px;
        img {
          width: 48px;
          height: 48px;
        }
      }

      .message {
        color: #002054;
        text-align: center;
        word-break: break-all;
        font-family: var(
          --font-family-arabic,
          'DIN Next LT Arabic',
          'Helvetica Neue',
          'Helvetica',
          'Arial',
          sans-serif
        );
        font-size: 24px;
        font-weight: 700;
        letter-spacing: 0;
        margin: 0;
      }
    `,
  ],
})
export class ErrorModalComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    private dialogRef: MatDialogRef<ErrorModalComponent>
  ) {
    // Set default to error if not specified
    if (data.isError === undefined) {
      data.isError = true;
    }
  }

  close(): void {
    this.dialogRef.close();
  }
}
