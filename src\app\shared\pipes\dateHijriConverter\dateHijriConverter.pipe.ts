import { Pipe, PipeTransform } from '@angular/core';
import moment from 'moment';
import momentHijri from 'moment-hijri';
import { DateTime } from 'luxon';
import { LanguageService } from '@core/gl-services/language-services/language.service';

@Pipe({
  name: 'dateHijriConverter',
  standalone: true
})
export class DateHijriConverterPipe implements PipeTransform {
  hijriMonths = [
    'محرم', 'صفر', 'ربيع الأول', 'ربيع الآخر',
    'جمادى الأولى', 'جمادى الآخرة', 'رجب', 'شعبان',
    'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
  ];
  
  hijriMonthsEn = [
    'Muharram', 'Safar', 'Rabi al-Awwal', '<PERSON><PERSON> al-<PERSON>i',
    '<PERSON><PERSON> al-Awwal', '<PERSON><PERSON> al<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>ban',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON> al<PERSON>‘dah', '<PERSON>hu al-Hijjah'
  ];
  
  currentLang: string = 'ar'; 
  months: string[] = this.hijriMonths; 
  

  constructor(private languageService: LanguageService) {
    this.currentLang = this.languageService.currentLang;
    this.months = this.currentLang === 'ar' ? this.hijriMonths : this.hijriMonthsEn;
  
    this.languageService.currentLang$.subscribe(lang => {
      this.currentLang = lang;
      this.months = lang === 'ar' ? this.hijriMonths : this.hijriMonthsEn;
    });
  }
  
  transform(dateTime: string | DateTime, convertTo: 'hijri' | 'gregorian' = 'hijri'): string {
    if (!dateTime) return '';

    try {
      // Convert DateTime object to string if needed
      let dateString: string;
      if (typeof dateTime === 'string') {
        dateString = dateTime;
      } else {
        // Handle DateTime object from Luxon
        dateString = dateTime.toJSDate().toISOString();
      }

      // Validate the date string
      const momentDate = moment(dateString);
      if (!momentDate.isValid()) {
        console.warn('Invalid date provided to dateHijriConverter:', dateTime);
        return '';
      }

      // Get date components directly to avoid locale issues with formatting
      const year = momentDate.year();
      const month = momentDate.month() + 1; // moment months are 0-based
      const day = momentDate.date();
      let value = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      if (convertTo === 'hijri') {
        const hijri = this.convertGregorianToHijri(this.parseDate(value));

        // Validate hijri conversion results
        if (isNaN(hijri.day) || isNaN(hijri.month) || isNaN(hijri.year)) {
          console.warn('Invalid Hijri conversion result:', hijri, 'for date:', dateTime);
          return '';
        }

        const monthName = this.months[hijri.month - 1]; // zero-based
        if (!monthName) {
          console.warn('Invalid Hijri month index:', hijri.month, 'for date:', dateTime);
          return '';
        }

        return `${hijri.day} ${monthName} ${hijri.year}`;
      } else {
        const gregorian = this.convertHijriToGregorian(this.parseDate(value));

        // Validate gregorian conversion results
        if (isNaN(gregorian.day) || isNaN(gregorian.month) || isNaN(gregorian.year)) {
          console.warn('Invalid Gregorian conversion result:', gregorian, 'for date:', dateTime);
          return '';
        }

        return `${this.pad(gregorian.day)}/${this.pad(gregorian.month)}/${gregorian.year}`;
      }
    } catch (error) {
      console.error('Error in dateHijriConverter pipe:', error, 'for date:', dateTime);
      return '';
    }
  }

  private parseDate(dateStr: string): { day: number; month: number; year: number } {
    try {
      // Handle both YYYY-MM-DD format and potential other formats
      const parts = dateStr.split('-');
      if (parts.length >= 3) {
        const year = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10);
        const day = parseInt(parts[2], 10);

        // Validate the parsed values
        if (isNaN(year) || isNaN(month) || isNaN(day)) {
          console.warn('Failed to parse date parts:', { year, month, day }, 'from:', dateStr);
          return { day: NaN, month: NaN, year: NaN };
        }

        return { day, month, year };
      } else {
        console.warn('Invalid date format for parsing:', dateStr);
        return { day: NaN, month: NaN, year: NaN };
      }
    } catch (error) {
      console.error('Error parsing date:', error, 'for dateStr:', dateStr);
      return { day: NaN, month: NaN, year: NaN };
    }
  }

  private pad(n: number): string {
    return n < 10 ? '0' + n : n.toString();
  }

  private convertGregorianToHijri(gregorian: { day: number; month: number; year: number }) {
    try {
      // Validate input
      if (isNaN(gregorian.day) || isNaN(gregorian.month) || isNaN(gregorian.year)) {
        console.warn('Invalid gregorian date input:', gregorian);
        return { day: NaN, month: NaN, year: NaN };
      }

      momentHijri.locale('en'); // numerals in English, but we’ll format manually
      const formatted = `${gregorian.year}-${this.pad(gregorian.month)}-${this.pad(gregorian.day)}`;

      // Validate the formatted date
      const momentDate = momentHijri(formatted, 'YYYY-MM-DD');
      if (!momentDate.isValid()) {
        console.warn('Invalid moment date for Hijri conversion:', formatted);
        return { day: NaN, month: NaN, year: NaN };
      }

      const hijriDate = momentDate.format('iYYYY-iMM-iDD');
      const [year, month, day] = hijriDate.split('-').map(Number);

      // Validate the conversion result
      if (isNaN(day) || isNaN(month) || isNaN(year)) {
        console.warn('Invalid Hijri conversion result:', hijriDate, 'from:', formatted);
        return { day: NaN, month: NaN, year: NaN };
      }

      return { day, month, year };
    } catch (error) {
      console.error('Error in convertGregorianToHijri:', error, 'for input:', gregorian);
      return { day: NaN, month: NaN, year: NaN };
    }
  }

  private convertHijriToGregorian(hijri: { day: number; month: number; year: number }) {
    momentHijri.locale('en');
    const formatted = `${hijri.year}-${this.pad(hijri.month)}-${this.pad(hijri.day)}`;
    const gregorianDate = momentHijri(formatted, 'iYYYY-iMM-iDD').format('YYYY-MM-DD');
    const [year, month, day] = gregorianDate.split('-').map(Number);
    return { day, month, year };
  }
}
