import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export class CustomValidators {

  /**
   * Validates that the input contains only Arabic text
   */
  static arabicText(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const arabicPattern = /^[\u0600-\u06FF\s]+$/;
    return arabicPattern.test(control.value) ? null : { arabicText: true };
  }

  /**
   * Validates that the input contains only English text
   */
  static englishText(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const englishPattern = /^[a-zA-Z\s]+$/;
    return englishPattern.test(control.value) ? null : { englishText: true };
  }

  /**
   * Validates email format with stricter rules than <PERSON><PERSON>'s default email validator
   * Requires a valid domain with at least 2 characters after the dot
   */
  static strictEmail(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;

    const email = control.value.trim();

    // More strict email pattern that requires:
    // - Valid characters before @
    // - @ symbol
    // - Valid domain name
    // - At least one dot in domain
    // - At least 2 characters after the last dot (TLD)
    const strictEmailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    if (!strictEmailPattern.test(email)) {
      return { email: true };
    }

    // Additional checks
    // Check for consecutive dots
    if (email.includes('..')) {
      return { email: true };
    }

    // Check if it starts or ends with a dot (before @)
    const [localPart, domainPart] = email.split('@');
    if (localPart.startsWith('.') || localPart.endsWith('.')) {
      return { email: true };
    }

    // Check domain part
    if (domainPart.startsWith('.') || domainPart.endsWith('.') || domainPart.startsWith('-') || domainPart.endsWith('-')) {
      return { email: true };
    }

    return null;
  }

  /**
   * Validates that the input is a positive integer
   */
  static positiveInteger(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const value = Number(control.value);
    return Number.isInteger(value) && value > 0 ? null : { positiveInteger: true };
  }

  /**
   * Validates that the input is an integer (positive, negative, or zero)
   */
  static integer(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const value = Number(control.value);
    return Number.isInteger(value) ? null : { integer: true };
  }

  /**
   * Validates phone number format (Saudi format)
   */
  static saudiPhoneNumber(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const phonePattern = /^(05|5)(5|0|3|6|4|9|1|8|7)[0-9]{7}$/;
    return phonePattern.test(control.value) ? null : { saudiPhoneNumber: true };
  }

  /**
   * Validates that a date is not in the future
   */
  static notFutureDate(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const inputDate = new Date(control.value);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today
    return inputDate <= today ? null : { futureDate: true };
  }

  /**
   * Validates that a date is not in the past
   */
  static notPastDate(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const inputDate = new Date(control.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Start of today
    return inputDate >= today ? null : { pastDate: true };
  }

  /**
   * Creates a validator that checks if a value is unique in an array
   */
  static uniqueInArray(existingValues: string[], caseSensitive: boolean = false): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;

      const value = caseSensitive ? control.value : control.value.toLowerCase();
      const values = caseSensitive ? existingValues : existingValues.map(v => v.toLowerCase());

      return values.includes(value) ? { duplicate: true } : null;
    };
  }

  /**
   * Creates a validator for date range validation
   */
  static dateRange(startDateKey: string, endDateKey: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const startDate = control.get(startDateKey)?.value;
      const endDate = control.get(endDateKey)?.value;

      if (!startDate || !endDate) return null;

      const start = new Date(startDate);
      const end = new Date(endDate);

      return start <= end ? null : { dateRange: true };
    };
  }

  /**
   * Validates file size
   */
  static fileSize(maxSizeInMB: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;

      const file = control.value as File;
      if (!(file instanceof File)) return null;

      const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
      return file.size <= maxSizeInBytes ? null : { fileSize: { maxSize: maxSizeInMB, actualSize: Math.round(file.size / 1024 / 1024 * 100) / 100 } };
    };
  }

  /**
   * Validates file type
   */
  static fileType(allowedTypes: string[]): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;

      const file = control.value as File;
      if (!(file instanceof File)) return null;

      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const normalizedTypes = allowedTypes.map(type => type.startsWith('.') ? type.substring(1).toLowerCase() : type.toLowerCase());

      return fileExtension && normalizedTypes.includes(fileExtension) ? null : { fileType: { allowedTypes, actualType: fileExtension } };
    };
  }

  /**
   * Validates minimum age
   */
  static minimumAge(minAge: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;

      const birthDate = new Date(control.value);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        return age - 1 >= minAge ? null : { minimumAge: { requiredAge: minAge, actualAge: age - 1 } };
      }

      return age >= minAge ? null : { minimumAge: { requiredAge: minAge, actualAge: age } };
    };
  }

  /**
   * Validates that a number is within a specific range
   */
  static numberRange(min: number, max: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;

      const value = Number(control.value);
      if (isNaN(value)) return { numberRange: { min, max, actualValue: control.value } };

      return value >= min && value <= max ? null : { numberRange: { min, max, actualValue: value } };
    };
  }

  /**
   * Validates percentage (0-100)
   */
  static percentage(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;

    const value = Number(control.value);
    if (isNaN(value)) return { percentage: true };

    return value >= 0 && value <= 100 ? null : { percentage: true };
  }

  /**
   * Validates that a string contains only alphanumeric characters
   */
  static alphanumeric(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const alphanumericPattern = /^[a-zA-Z0-9]+$/;
    return alphanumericPattern.test(control.value) ? null : { alphanumeric: true };
  }

  /**
   * Validates that a string contains only alphanumeric characters and spaces
   */
  static alphanumericWithSpaces(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    const pattern = /^[a-zA-Z0-9\s]+$/;
    return pattern.test(control.value) ? null : { alphanumericWithSpaces: true };
  }
}
