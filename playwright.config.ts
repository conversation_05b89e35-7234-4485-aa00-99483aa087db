import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright Configuration for Jadwa Fund Management System
 *
 * This configuration supports:
 * - Multiple environments (local, test, production)
 * - Cross-browser testing (Chrome, Firefox, Safari, Edge)
 * - Mobile device testing
 * - Arabic/English localization testing
 * - Comprehensive reporting
 */

export default defineConfig({
  // Test directory structure
  testDir: './tests/e2e',

  // Global test configuration
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,

  // Reporter configuration
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['line'],
    ['allure-playwright', { outputFolder: 'test-results/allure-results' }]
  ],

  // Global test settings
  use: {
    // Base URL configuration
    baseURL: process.env.BASE_URL || 'http://localhost:4200',

    // Browser settings
    headless: process.env.CI ? true : false,
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,

    // Screenshots and videos
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',

    // Timeouts
    actionTimeout: 30000,
    navigationTimeout: 30000,

    // Localization support
    locale: 'ar-EG',
    timezoneId: 'Asia/Riyadh',

    // Additional context options
    extraHTTPHeaders: {
      'Accept-Language': 'ar-EG,ar;q=0.9,en;q=0.8'
    }
  },

  // Environment-specific configurations
  projects: [
    // Setup project for authentication
    {
      name: 'setup',
      testMatch: /.*\.setup\.ts/,
      teardown: 'cleanup'
    },

    // Cleanup project
    {
      name: 'cleanup',
      testMatch: /.*\.cleanup\.ts/
    },

    // Desktop browsers - Arabic locale
    {
      name: 'chromium-ar',
      use: {
        ...devices['Desktop Chrome'],
        locale: 'ar-EG',
        extraHTTPHeaders: {
          'Accept-Language': 'ar-EG,ar;q=0.9'
        }
      },
      dependencies: ['setup']
    },

    {
      name: 'firefox-ar',
      use: {
        ...devices['Desktop Firefox'],
        locale: 'ar-EG',
        extraHTTPHeaders: {
          'Accept-Language': 'ar-EG,ar;q=0.9'
        }
      },
      dependencies: ['setup']
    },

    {
      name: 'webkit-ar',
      use: {
        ...devices['Desktop Safari'],
        locale: 'ar-EG',
        extraHTTPHeaders: {
          'Accept-Language': 'ar-EG,ar;q=0.9'
        }
      },
      dependencies: ['setup']
    },

    // Desktop browsers - English locale
    {
      name: 'chromium-en',
      use: {
        ...devices['Desktop Chrome'],
        locale: 'en-US',
        extraHTTPHeaders: {
          'Accept-Language': 'en-US,en;q=0.9'
        }
      },
      dependencies: ['setup']
    },

    {
      name: 'firefox-en',
      use: {
        ...devices['Desktop Firefox'],
        locale: 'en-US',
        extraHTTPHeaders: {
          'Accept-Language': 'en-US,en;q=0.9'
        }
      },
      dependencies: ['setup']
    },

    // Mobile devices
    {
      name: 'Mobile Chrome',
      use: {
        ...devices['Pixel 5'],
        locale: 'ar-EG'
      },
      dependencies: ['setup']
    },

    {
      name: 'Mobile Safari',
      use: {
        ...devices['iPhone 12'],
        locale: 'ar-EG'
      },
      dependencies: ['setup']
    },

    // Microsoft Edge
    {
      name: 'Microsoft Edge',
      use: {
        ...devices['Desktop Edge'],
        channel: 'msedge',
        locale: 'ar-EG'
      },
      dependencies: ['setup']
    }
  ],

  // Web server configuration for local development
  webServer: process.env.CI ? undefined : {
    command: 'npm run serve:test',
    url: 'http://localhost:4200',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },

  // Output directories
  outputDir: 'test-results/artifacts',

  // Global timeout settings
  timeout: 60000,
  expect: {
    timeout: 10000,
    toHaveScreenshot: {
      threshold: 0.2,
      mode: 'pixel'
    },
    toMatchSnapshot: {
      threshold: 0.2
    }
  }
});
