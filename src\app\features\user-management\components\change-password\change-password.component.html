<!-- Change Password Section -->
<div class="change-password-section">
  <div class="section-header mb-4">
    <h4 class="section-title">{{ 'USER_PROFILE.CHANGE_PASSWORD' | translate }}</h4>
  </div>

    <!-- Form Fields -->

    <div class="form-fields-section">
      <app-form-builder
        [formControls]="formControls"
        [formGroup]="changePasswordForm"
        [isFormSubmitted]="isFormSubmitted">

        <div slot="between" class="hr-first-container">
                    <ul class="password-rules p-0 list-unstyled">
                        <li class="title mb-2">{{ 'LOGIN_PAGE.PASSWORD_RULES.TITLE' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_1' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_2' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_3' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_4' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_5' | translate }}</li>
                        <li class="title-rule">{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_6' | translate }}</li>
                      </ul>
                </div>
      </app-form-builder>
    </div>

    <!-- Action Buttons -->

    <div class="my-4 d-flex justify-content-end align-items-center">
       <app-custom-button [btnName]="'COMMON.CANCEL' | translate" (click)="cancel()"
        [buttonType]="buttonEnum.Secondary" [iconName]="IconEnum.cancel">
      </app-custom-button>
      <app-custom-button  type="submit" class="change-password-btn"  [iconName]="" [btnName]="'LOGIN_PAGE.CHANGE_PASSWORD' | translate" (click)="onSubmit()"></app-custom-button>

    </div>


</div>
