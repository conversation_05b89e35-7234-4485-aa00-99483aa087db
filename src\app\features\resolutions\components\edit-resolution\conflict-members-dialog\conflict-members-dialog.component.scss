.conflict-members-dialog {
  width: 100%;
  max-width: 500px;
  background: white;
  border-radius: 12px;
  overflow: hidden;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;

    .dialog-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #00205a;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 18px;
      color: #666;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background: #e0e0e0;
        color: #333;
      }

      i {
        font-size: 16px;

        // Fallback for when FontAwesome doesn't load
        &.fas.fa-times:before {
          content: "×";
          font-family: Arial, sans-serif;
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
  }

  .dialog-content {
    padding: 24px;

    .item-info {
      margin-bottom: 20px;
      padding-bottom: 16px;
      // border-bottom: 1px solid #e0e0e0;

      .item-title {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #00205a;
      }

      .members-count {
        margin: 0;
        font-size: 14px;
        color: #666;

        strong {
          color: #007bff;
        }
      }
    }

    .members-list {
      display: flex;
      align-items: center;
      gap: 10px;
      flex-wrap: wrap;

      .member-item {
        display: flex;
        align-items: center;
        padding: 10px;
        border-radius: 9999px;
        border: 1px solid #B4D6FA;
        background-color: #EBF3FC;

        // &:last-child {
        //   border-bottom: none;
        // }

        .member-number {
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          background: #007bff;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 600;
        }

        .member-details {
          flex: 1;

          .member-name {
            font-size: 14px;
            font-weight: 400;
            color: #00205a;
          }

          .member-type {
            font-size: 12px;
            color: #666;
            margin-bottom: 6px;
            font-style: italic;
          }

          .conflict-notes {
            font-size: 12px;
            color: #666;

            .notes-label {
              font-weight: 600;
              color: #333;
            }

            .notes-text {
              margin-left: 4px;
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #666;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        color: #ccc;

        // Fallback for FontAwesome users icon
        &.fas.fa-users:before {
          content: "👥";
          font-family: Arial, sans-serif;
          font-size: 40px;
        }
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }

    .dialog-actions {
      display: flex;
      justify-content: end;
      padding-top: 20px;
      // border-top: 1px solid #e0e0e0;

      .btn {
        padding: 10px 24px;
        font-weight: 500;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 100px;
        justify-content: center;
        background: #007bff;
        border-color: #007bff;
        color: white;

        i {
          font-size: 14px;

          // Fallback for FontAwesome check icon
          &.fas.fa-check:before {
            content: "✓";
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: bold;
          }
        }
        transition: all 0.2s ease;

        &:hover {
          background: #0056b3;
          border-color: #004085;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .conflict-members-dialog {
    max-width: 95vw;
    margin: 10px;

    .dialog-header {
      padding: 16px 20px;

      .dialog-title {
        font-size: 16px;
      }
    }

    .dialog-content {
      padding: 20px;

      .members-list {
        max-height: 250px;

        .member-item {
          .member-details {
            .member-name {
              font-size: 13px;
            }

            .member-type,
            .conflict-notes {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}
