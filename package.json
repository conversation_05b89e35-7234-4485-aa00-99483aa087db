{"name": "j<PERSON>wa", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "build:test": "ng build --configuration test", "serve:prod": "ng serve --configuration production", "serve:test": "ng serve --configuration test", "serve:local": "ng serve", "watch": "ng build --watch --configuration development", "test": "ng test", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report", "test:e2e:local": "TEST_ENV=local playwright test", "test:e2e:test": "TEST_ENV=test playwright test", "test:e2e:production": "TEST_ENV=production playwright test --grep @readonly", "test:selenium": "npx mocha --config tests/selenium/config/mocha.config.js", "test:selenium:chrome": "BROWSER=chrome npx mocha --config tests/selenium/config/mocha.config.js", "test:selenium:firefox": "BROWSER=firefox npx mocha --config tests/selenium/config/mocha.config.js", "test:selenium:edge": "BROWSER=edge npx mocha --config tests/selenium/config/mocha.config.js", "test:selenium:safari": "BROWSER=safari npx mocha --config tests/selenium/config/mocha.config.js", "test:selenium:local": "ENVIRONMENT=local npx mocha --config tests/selenium/config/mocha.config.js", "test:selenium:test": "ENVIRONMENT=test npx mocha --config tests/selenium/config/mocha.config.js", "test:selenium:production": "ENVIRONMENT=production npx mocha --config tests/selenium/config/mocha.config.js", "test:selenium:auth": "npx mocha --config tests/selenium/config/mocha.config.js --grep 'Authentication'", "test:selenium:resolution": "npx mocha --config tests/selenium/config/mocha.config.js --grep 'Resolution'", "test:selenium:localization": "npx mocha --config tests/selenium/config/mocha.config.js --grep 'Localization'", "test:selenium:report": "open test-results/selenium/selenium-test-results.html", "test:all": "npm run test:e2e && npm run test:selenium", "test:compare": "npm run test:e2e:local && npm run test:selenium:local", "nswag": "nswag run"}, "private": true, "dependencies": {"@angular/animations": "^18.0.0", "@angular/cdk": "^18.2.14", "@angular/common": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/core": "^18.0.0", "@angular/fire": "^18.0.0", "@angular/forms": "^18.0.0", "@angular/localize": "^18.2.13", "@angular/material": "^18.2.5", "@angular/platform-browser": "^18.0.0", "@angular/platform-browser-dynamic": "^18.0.0", "@angular/router": "^18.0.0", "@microsoft/signalr": "^8.0.7", "@ng-bootstrap/ng-bootstrap": "^17.0.1", "@ng-select/ng-select": "^14.9.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@popperjs/core": "^2.11.8", "@types/luxon": "^3.6.2", "bootstrap": "^5.3.6", "bootstrap-icons": "^1.13.1", "docx": "^9.0.2", "file-saver": "^2.0.5", "firebase": "^10.8.1", "hammerjs": "^2.0.8", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "jwt-decode": "^4.0.0", "luxon": "^3.6.1", "moment": "^2.30.1", "moment-hijri": "^3.0.0", "ngx-pipes": "^3.2.2", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.0", "sweetalert2": "^11.22.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.7", "@angular/cli": "^18.2.19", "@angular/compiler-cli": "^18.0.0", "@playwright/test": "^1.53.2", "@types/chai": "^5.2.2", "@types/file-saver": "^2.0.7", "@types/jasmine": "~5.1.0", "@types/mocha": "^10.0.10", "@types/moment-hijri": "^2.1.4", "@types/selenium-webdriver": "^4.1.28", "allure-playwright": "^3.3.0", "chai": "^5.2.0", "chromedriver": "^138.0.1", "edgedriver": "^6.1.1", "geckodriver": "^5.0.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "mocha": "^11.7.1", "mochawesome": "^7.1.3", "nswag": "^14.4.0", "selenium-webdriver": "^4.34.0", "typescript": "~5.4.2", "webdriver-manager": "^12.1.9"}, "overrides": {}}