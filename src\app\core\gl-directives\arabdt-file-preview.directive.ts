import { Directive, ElementRef, HostListener, Input, Renderer2 } from '@angular/core';

@Directive({
  selector: '[appArabdtFilePreview]',
  standalone: true
})
export class ArabdtFilePreviewDirective {
  @Input('appArabdtFilePreview') previewContainer!: any; // Use ElementRef

  constructor(private renderer: Renderer2) {}

  @HostListener('change', ['$event.target'])
onFileChange(input: HTMLInputElement) {
  const file = input.files?.[0];
  if (!file || !this.previewContainer) return; // Ensure file exists before proceeding

  const reader = new FileReader();

  reader.onload = () => {
    this.previewContainer.innerHTML = ''; // Clear previous preview
    const previewElement = this.createPreviewElement(file, reader.result as string);
    this.renderer.appendChild(this.previewContainer, previewElement);
  };

  reader.onerror = () => {
    console.error('Failed to read file');
  };

  reader.readAsDataURL(file);
}


  private createPreviewElement(file: File, fileDataUrl: string): HTMLElement {
    if (file.type.startsWith('image/')) {
      const img = this.renderer.createElement('img');
      this.renderer.setAttribute(img, 'src', fileDataUrl);
      this.renderer.setStyle(img, 'maxWidth', '100%');
      this.renderer.setStyle(img, 'maxHeight', '200px');
      return img;
    } else if (file.type.startsWith('video/')) {
      const video = this.renderer.createElement('video');
      this.renderer.setAttribute(video, 'src', fileDataUrl);
      this.renderer.setAttribute(video, 'controls', '');
      this.renderer.setStyle(video, 'maxWidth', '100%');
      this.renderer.setStyle(video, 'maxHeight', '200px');
      return video;
    } else {
      const text = this.renderer.createText(`Preview not available for ${file.name}`);
      const div = this.renderer.createElement('div');
      this.renderer.appendChild(div, text);
      return div;
    }
  }
}

// serve time before add plugins 2.695 seconds

// build time before add plugins 4.585 seconds

// dist file size 2 MB on disk

// after

// serve time after add plugins 2.681 seconds

// build time after add plugins 4.780 seconds

// dist file size 2 MB on disk