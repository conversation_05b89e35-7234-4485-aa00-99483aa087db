/**
 * Spinner Debug Utilities
 * Use these functions in browser console to debug spinner issues
 */

declare global {
  interface Window {
    spinnerDebug: {
      getState: () => any;
      forceHide: () => void;
      enableLogging: () => void;
      testSpinner: () => void;
    };
  }
}

// Add debug utilities to window object for console access
if (typeof window !== 'undefined') {
  window.spinnerDebug = {
    /**
     * Get current spinner state
     */
    getState: () => {
      try {
        const appRoot = document.querySelector('app-root');
        if (!appRoot) return 'App root not found';
        
        const component = (window as any).ng?.getComponent(appRoot);
        if (!component?.loaderService) return 'Spinner service not found';
        
        const service = component.loaderService;
        return {
          isLoading: service.isLoading,
          activeRequests: service.activeRequestsCount,
          debugInfo: service.getDebugInfo()
        };
      } catch (error) {
        return `Error: ${error}`;
      }
    },

    /**
     * Force hide the spinner (emergency reset)
     */
    forceHide: () => {
      try {
        const appRoot = document.querySelector('app-root');
        if (!appRoot) return 'App root not found';
        
        const component = (window as any).ng?.getComponent(appRoot);
        if (!component?.loaderService) return 'Spinner service not found';
        
        component.loaderService.forceHide();
        return 'Spinner force hidden';
      } catch (error) {
        return `Error: ${error}`;
      }
    },

    /**
     * Enable logging for debugging
     */
    enableLogging: () => {
      console.log('To enable logging, set enableLogging = true in SpinnerService constructor');
      return 'Check console for instructions';
    },

    /**
     * Test spinner show/hide functionality
     */
    testSpinner: () => {
      try {
        const appRoot = document.querySelector('app-root');
        if (!appRoot) return 'App root not found';
        
        const component = (window as any).ng?.getComponent(appRoot);
        if (!component?.loaderService) return 'Spinner service not found';
        
        const service = component.loaderService;
        
        console.log('Testing spinner...');
        console.log('Initial state:', service.getDebugInfo());
        
        service.show();
        console.log('After show():', service.getDebugInfo());
        
        setTimeout(() => {
          service.hide();
          console.log('After hide():', service.getDebugInfo());
        }, 2000);
        
        return 'Test started - check console for results';
      } catch (error) {
        return `Error: ${error}`;
      }
    }
  };
}

export {};
