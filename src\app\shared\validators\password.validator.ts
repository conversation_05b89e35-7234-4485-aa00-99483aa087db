import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * Password validator that checks for:
 * - Minimum 8 characters
 * - At least one uppercase letter
 * - At least one lowercase letter
 * - At least one number
 * - At least one special character
 */
export function passwordValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null; // Don't validate empty values, use required validator for that
    }

    const errors: ValidationErrors = {};

    // Check minimum length (8 characters)
    if (value.length < 8) {
      errors['passwordMinLength'] = { requiredLength: 8, actualLength: value.length };
    }

    // Check for at least one uppercase letter
    if (!/[A-Z]/.test(value)) {
      errors['passwordUppercase'] = { message: 'Password must contain at least one uppercase letter' };
    }

    // Check for at least one lowercase letter
    if (!/[a-z]/.test(value)) {
      errors['passwordLowercase'] = { message: 'Password must contain at least one lowercase letter' };
    }

    // Check for at least one number
    if (!/[0-9]/.test(value)) {
      errors['passwordNumber'] = { message: 'Password must contain at least one number' };
    }

    // Check for at least one special character
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)) {
      errors['passwordSpecialChar'] = { message: 'Password must contain at least one special character' };
    }

    return Object.keys(errors).length > 0 ? errors : null;
  };
}

/**
 * Password strength validator with different levels
 */
export function passwordStrengthValidator(level: 'weak' | 'medium' | 'strong' = 'medium'): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return null;
    }

    const errors: ValidationErrors = {};

    switch (level) {
      case 'weak':
        // Minimum 6 characters, at least one letter and one number
        if (value.length < 6) {
          errors['passwordMinLength'] = { requiredLength: 6, actualLength: value.length };
        }
        if (!/[a-zA-Z]/.test(value)) {
          errors['passwordLetter'] = { message: 'Password must contain at least one letter' };
        }
        if (!/[0-9]/.test(value)) {
          errors['passwordNumber'] = { message: 'Password must contain at least one number' };
        }
        break;

      case 'medium':
        // Minimum 8 characters, uppercase, lowercase, number
        if (value.length < 8) {
          errors['passwordMinLength'] = { requiredLength: 8, actualLength: value.length };
        }
        if (!/[A-Z]/.test(value)) {
          errors['passwordUppercase'] = { message: 'Password must contain at least one uppercase letter' };
        }
        if (!/[a-z]/.test(value)) {
          errors['passwordLowercase'] = { message: 'Password must contain at least one lowercase letter' };
        }
        if (!/[0-9]/.test(value)) {
          errors['passwordNumber'] = { message: 'Password must contain at least one number' };
        }
        break;

      case 'strong':
        // Minimum 8 characters, uppercase, lowercase, number, special character
        if (value.length < 8) {
          errors['passwordMinLength'] = { requiredLength: 8, actualLength: value.length };
        }
        if (!/[A-Z]/.test(value)) {
          errors['passwordUppercase'] = { message: 'Password must contain at least one uppercase letter' };
        }
        if (!/[a-z]/.test(value)) {
          errors['passwordLowercase'] = { message: 'Password must contain at least one lowercase letter' };
        }
        if (!/[0-9]/.test(value)) {
          errors['passwordNumber'] = { message: 'Password must contain at least one number' };
        }
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)) {
          errors['passwordSpecialChar'] = { message: 'Password must contain at least one special character' };
        }
        break;
    }

    return Object.keys(errors).length > 0 ? errors : null;
  };
}
