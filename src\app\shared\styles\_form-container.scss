// Shared Form Container Styles
// This file contains the standard form styling pattern used across all form components
// Usage: @import 'src/app/shared/styles/form-container';

.form-container {
  padding: 2rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 0.5px solid #dce0e3;
  
  .form-group {
    margin-bottom: 1.5rem;

    &.col-12 {
      width: 100%;
    }

    &.col-md-6 {
      width: 48%;
      margin-inline-end: 2%;
      &:nth-child(2n) {
        margin-inline-end: 0;
      }
    }

    &.col-md-4 {
      width: 31.33%;
      margin-inline-end: 2%;
      &:nth-child(3n) {
        margin-inline-end: 0;
      }
    }

    &.col-md-3 {
      width: 23%;
      margin-inline-end: 2.66%;
      &:nth-child(4n) {
        margin-inline-end: 0;
      }
    }
  }

  .actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    
    &.justify-content-end {
      justify-content: flex-end;
    }
    
    &.justify-content-center {
      justify-content: center;
    }
    
    &.justify-content-start {
      justify-content: flex-start;
    }
  }

  // Form validation styles
  ::ng-deep {
    .form-control.is-invalid {
      border-color: #e74a3b;
      
      &:focus {
        border-color: #e74a3b;
        box-shadow: 0 0 0 0.2rem rgba(231, 74, 59, 0.25);
      }
    }
    
    .invalid-feedback {
      color: #e74a3b;
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }

    .form-control.is-valid {
      border-color: #28a745;
      
      &:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
      }
    }
    
    .valid-feedback {
      color: #28a745;
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }
  }

  // Loading state
  &.loading {
    position: relative;
    pointer-events: none;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      border-radius: 8px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .form-container {
    padding: 1rem;
    
    .form-group {
      &.col-md-6,
      &.col-md-4,
      &.col-md-3 {
        width: 100%;
        margin-inline-end: 0;
      }
    }
    
    .actions {
      flex-direction: column;
      
      app-custom-button {
        width: 100%;
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .form-container {
    .form-group {
      &.col-md-6 {
        margin-inline-start: 2%;
        margin-inline-end: 0;
        &:nth-child(2n) {
          margin-inline-start: 0;
        }
      }

      &.col-md-4 {
        margin-inline-start: 2%;
        margin-inline-end: 0;
        &:nth-child(3n) {
          margin-inline-start: 0;
        }
      }

      &.col-md-3 {
        margin-inline-start: 2.66%;
        margin-inline-end: 0;
        &:nth-child(4n) {
          margin-inline-start: 0;
        }
      }
    }
    
    .actions {
      &.justify-content-end {
        justify-content: flex-start;
      }
      
      &.justify-content-start {
        justify-content: flex-end;
      }
    }
  }
}

// Dark theme support (if needed in the future)
@media (prefers-color-scheme: dark) {
  .form-container {
    background: #1a202c;
    border-color: #2d3748;
    color: #e2e8f0;
  }
}

// Print styles
@media print {
  .form-container {
    background: white;
    border: 1px solid #000;
    box-shadow: none;
    
    .actions {
      display: none;
    }
  }
}
