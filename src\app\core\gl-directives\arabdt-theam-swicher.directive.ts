import { Directive, Input, Renderer2, OnInit, OnChanges, SimpleChanges } from '@angular/core';

@Directive({
  selector: '[appArabdtThemeSwicher]',
  standalone: true
})
export class ArabdtThemeSwicherDirective {
  @Input() appArabdtThemeSwicher: 'dark' | 'light' = 'light';

  constructor(private renderer: Renderer2) {}

  ngOnInit() {
    this.updateTheme();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['appArabdtThemeSwicher']) {
      this.updateTheme();
    }
  }

  private updateTheme() {
    const rootElement = document.documentElement;

    if (this.appArabdtThemeSwicher === 'dark') {
      this.renderer.addClass(rootElement, 'dark-theme');
      this.renderer.removeClass(rootElement, 'light-theme');
    } else {
      this.renderer.addClass(rootElement, 'light-theme');
      this.renderer.removeClass(rootElement, 'dark-theme');
    }
  }
}

// example usage at app component


// serve time before add plugins 2.695 seconds

// build time before add plugins 4.585 seconds

// dist file size 2 MB on disk

// after

// serve time after add plugins 2.681 seconds

// build time after add plugins 4.780 seconds

// dist file size 2 MB on disk