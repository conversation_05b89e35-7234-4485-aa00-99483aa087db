# 🧩 Generic Module Creation Guide

## 🏗️ Architectural Principles (Updated)

**IMPORTANT:** This guide has been updated to reflect the established JadwaUI architectural patterns:

### ✅ DO Use:
- **Direct API Proxy Integration**: Use NSwag-generated API proxies directly in components
- **Dynamic Form Component**: Use `app-form-builder` for all forms
- **Standalone Pages**: Create operations should be standalone pages, not dialogs
- **Generated Models**: Use models directly from `@core/api/api.generated.ts`
- **Fund Details Navigation**: Integrate navigation cards in fund details for related features

### ❌ DON'T Use:
- Custom service layers (use API proxies directly)
- Custom model files (use generated models)
- Dialog components for create operations (use standalone pages)
- Manual form implementations (use dynamic form builder)

## 📋 Table of Contents
- [Prerequisites](#prerequisites)
- [Step-by-Step Module Creation](#step-by-step-module-creation)
- [Direct API Integration](#direct-api-integration)
- [Dynamic Form Implementation](#dynamic-form-implementation)
- [Fund Details Navigation Integration](#fund-details-navigation-integration)
- [File Structure Template](#file-structure-template)
- [Code Templates](#code-templates)
- [Naming Conventions](#naming-conventions)
- [Integration Checklist](#integration-checklist)

## 🎯 Prerequisites

Before creating a new module, ensure you have:

- ✅ **Angular CLI** installed and configured
- ✅ **Project structure** understanding from architecture documentation
- ✅ **API endpoints** defined and documented
- ✅ **Data models** specified for the feature
- ✅ **UI/UX designs** approved for the feature
- ✅ **Permissions** defined for role-based access

## 🚀 Step-by-Step Module Creation

### Step 1: Create Feature Directory Structure

```bash
# Navigate to features directory
cd src/app/features

# Create new feature directory
mkdir your-feature-name
cd your-feature-name

# Create subdirectories (NO services or models directories)
mkdir components
```

### Step 2: Generate Core Files

```bash
# Generate main component (standalone) - for list view
ng generate component your-feature-name --standalone --skip-tests

# Generate create component (standalone page, NOT dialog)
ng generate component components/create-your-feature-name --standalone --skip-tests

# NO service generation - use API proxies directly
# NO dialog components for create operations - use standalone pages
```

### Step 3: Create Routing Configuration

Create `your-feature-name.routes.ts`:

```typescript
import { Routes } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';

export const YOUR_FEATURE_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => 
      import('./your-feature-name.component').then(m => m.YourFeatureNameComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'details/:id',
    loadComponent: () => 
      import('./components/your-feature-details/your-feature-details.component')
        .then(m => m.YourFeatureDetailsComponent),
    canActivate: [AuthGuard]
  }
];
```

### Step 4: Update Main App Routing

Add your feature route to `app.routes.ts`:

```typescript
{
  path: 'your-feature-name',
  loadChildren: () => 
    import('./features/your-feature-name/your-feature-name.routes')
      .then(m => m.YOUR_FEATURE_ROUTES),
  canActivate: [AuthGuard]
}
```

## 🔌 Direct API Integration

### Step 5: Configure API Proxy

1. **Run NSwag to generate API clients:**
```bash
nswag run
```

2. **Add API proxy to app.config.ts:**
```typescript
import { YourFeatureServiceProxy } from '@core/api/api.generated';

export const appConfig: ApplicationConfig = {
  providers: [
    // ... other providers
    YourFeatureServiceProxy,
  ],
};
```

3. **Use API proxy directly in components:**
```typescript
import { YourFeatureServiceProxy, YourFeatureDto } from '@core/api/api.generated';

@Component({...})
export class YourFeatureComponent implements OnInit {
  constructor(private yourFeatureProxy: YourFeatureServiceProxy) {}

  loadData(): void {
    this.yourFeatureProxy.yourFeatureList(0, 10, '', 'CreatedAt desc')
      .subscribe(response => {
        this.data = response.data || [];
      });
  }
}
```

## 📝 Dynamic Form Implementation

### Step 6: Use app-form-builder Component

```typescript
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';

@Component({
  imports: [FormBuilderComponent, ...],
  ...
})
export class CreateYourFeatureComponent {
  formControls: IControlOption[] = [
    {
      type: InputType.Text,
      formControlName: 'name',
      id: 'name',
      name: 'name',
      label: 'YOUR_FEATURE.NAME',
      placeholder: 'YOUR_FEATURE.NAME_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-6',
    },
    {
      type: InputType.Date,
      formControlName: 'date',
      id: 'date',
      name: 'date',
      label: 'YOUR_FEATURE.DATE',
      isRequired: true,
      class: 'col-md-6',
    },
    // Add more form controls as needed
  ];
}
```

## 🧭 Fund Details Navigation Integration

### Step 7: Add Navigation Card to Fund Details

Update `fund-details.component.ts`:

```typescript
// Add to fundCards array
{
  notificationCount: 0,
  fundCountKey: 'yourFeatureCount',
  title: this.translateService.instant('FUND_DETAILS.YOUR_FEATURE'),
  icon: 'assets/images/your-feature-icon.png',
  routerLink: `/admin/investment-funds/${this.fundId}/your-feature`,
  disabled: false
},
```

Add translation keys:
```json
// ar.json
"FUND_DETAILS": {
  "YOUR_FEATURE": "ميزتك الجديدة"
}

// en.json
"FUND_DETAILS": {
  "YOUR_FEATURE": "Your Feature"
}
```

## 📁 File Structure Template (Updated)

```
📁 your-feature-name/
├── 📄 your-feature-name.component.ts       # Main list component (read-only)
├── 📄 your-feature-name.component.html     # Component template
├── 📄 your-feature-name.component.scss     # Component styles
├── 📄 your-feature-name.routes.ts          # Feature routing
└── 📁 components/                          # Feature components
    ├── 📁 create-your-feature-name/        # Standalone create page
    │   ├── 📄 create-your-feature-name.component.ts
    │   ├── 📄 create-your-feature-name.component.html
    │   └── 📄 create-your-feature-name.component.scss
    └── 📁 update-your-feature-name/        # Standalone update page (if needed)
        ├── 📄 update-your-feature-name.component.ts
        ├── 📄 update-your-feature-name.component.html
        └── 📄 update-your-feature-name.component.scss

❌ NO services/ directory - use API proxies directly
❌ NO models/ directory - use generated models from @core/api/api.generated.ts
❌ NO dialog components for create operations - use standalone pages
```

## 🎨 Code Templates

### Main Component Template

```typescript
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';

// Shared components
import { TableComponent } from '@shared/components/table/table.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

// Core interfaces and enums
import { ITableColumn, TableActionEvent } from '@core/gl-interfaces/I-table/i-table';
import { ColumnTypeEnum, DataHandlingType } from '@core/enums/column-type';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';

// Feature-specific imports
import { YourFeatureService } from './services/your-feature.service';
import { YourFeatureDialogComponent } from './components/your-feature-dialog.component';
import { YourFeatureDto } from './models/your-feature.models';
import { TokenService } from '@features/auth/services/token.service';

@Component({
  selector: 'app-your-feature-name',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    PageHeaderComponent,
    BreadcrumbComponent,
    TranslateModule
  ],
  templateUrl: './your-feature-name.component.html',
  styleUrl: './your-feature-name.component.scss'
})
export class YourFeatureNameComponent implements OnInit {
  // Data properties
  ELEMENT_DATA: YourFeatureDto[] = [];
  tableDataSource = new MatTableDataSource<YourFeatureDto>();
  totalCount = 0;
  
  // UI state
  isDialogOpen = false;
  breadcrumbSizeEnum = SizeEnum;
  
  // Table configuration
  displayedColumns: string[] = ['name', 'description', 'updatedAt', 'actions'];
  columns: ITableColumn[] = [
    {
      columnDef: 'name',
      header: 'YOUR_FEATURE.NAME',
      columnType: ColumnTypeEnum.Text,
      cell: (element: YourFeatureDto) => element.name
    },
    {
      columnDef: 'description',
      header: 'YOUR_FEATURE.DESCRIPTION',
      columnType: ColumnTypeEnum.Text,
      cell: (element: YourFeatureDto) => element.description
    },
    {
      columnDef: 'updatedAt',
      header: 'YOUR_FEATURE.UPDATED_AT',
      columnType: ColumnTypeEnum.Date,
      cell: (element: YourFeatureDto) => element.updatedAt
    },
    {
      columnDef: 'actions',
      header: 'YOUR_FEATURE.ACTIONS',
      columnType: ColumnTypeEnum.Actions,
      cell: () => ''
    }
  ];
  
  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', routerLink: '/admin/dashboard' },
    { label: 'YOUR_FEATURE.TITLE', routerLink: '' }
  ];

  constructor(
    private yourFeatureService: YourFeatureService,
    private dialog: MatDialog,
    private translateService: TranslateService,
    public tokenService: TokenService
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.yourFeatureService.getList(0, 0, '', 'UpdatedAt desc')
      .subscribe((res: any) => {
        this.ELEMENT_DATA = res.data;
        this.tableDataSource.data = res.data;
        this.totalCount = res.totalCount;
      });
  }

  onClickAction(actionData: TableActionEvent): void {
    this.edit(actionData.row);
  }

  edit(row: YourFeatureDto): void {
    this.yourFeatureService.getById(row.id).subscribe((res: any) => {
      const item = res.data;
      const dialogRef = this.dialog.open(YourFeatureDialogComponent, {
        width: '500px',
        data: {
          isEdit: true,
          item: item,
          existingItems: this.tableDataSource
        }
      });

      dialogRef.afterClosed().subscribe((result: any) => {
        if (result) {
          this.yourFeatureService.update(result).subscribe((res: any) => {
            if (res.successed) {
              this.showSuccessMessage('YOUR_FEATURE.UPDATED_SUCCESSFULLY');
              this.loadData();
            }
          });
        }
      });
    });
  }

  onCreate(): void {
    if (this.isDialogOpen) return;
    this.isDialogOpen = true;

    const dialogRef = this.dialog.open(YourFeatureDialogComponent, {
      width: '500px',
      data: { 
        isEdit: false, 
        existingItems: this.tableDataSource 
      }
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      this.isDialogOpen = false;
      if (result) {
        this.yourFeatureService.create(result).subscribe((res: any) => {
          if (res.successed) {
            this.showSuccessMessage('YOUR_FEATURE.CREATED_SUCCESSFULLY');
            this.loadData();
          }
        });
      }
    });
  }

  private showSuccessMessage(messageKey: string): void {
    Swal.fire({
      icon: 'success',
      title: this.translateService.instant(messageKey),
      showConfirmButton: false,
      timer: 1500
    });
  }
}
```

### Service Template

```typescript
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@environments/environment';
import { YourFeatureDto } from '../models/your-feature.models';

@Injectable({
  providedIn: 'root'
})
export class YourFeatureService {
  private baseUrl = `${environment.apiUrl}/api/YourFeature`;

  constructor(private http: HttpClient) {}

  create(item: YourFeatureDto): Observable<any> {
    return this.http.post(`${this.baseUrl}/Create`, item);
  }

  update(item: YourFeatureDto): Observable<any> {
    return this.http.put(`${this.baseUrl}/Update`, item);
  }

  getById(id: number): Observable<YourFeatureDto> {
    return this.http.get<YourFeatureDto>(`${this.baseUrl}/GetById?id=${id}`);
  }

  getList(pageNo: number, pageSize: number, search: string, orderBy: string): Observable<any> {
    return this.http.get(`${this.baseUrl}/List`, {
      params: { 
        pageNo: pageNo.toString(), 
        pageSize: pageSize.toString(), 
        search, 
        orderBy 
      }
    });
  }

  delete(id: number): Observable<any> {
    return this.http.delete(`${this.baseUrl}/Delete?id=${id}`);
  }
}
```

### Data Model Template

```typescript
// your-feature.models.ts
export interface YourFeatureDto {
  id: number;
  name: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateYourFeatureCommand {
  name: string;
  description: string;
  isActive: boolean;
}

export interface UpdateYourFeatureCommand {
  id: number;
  name: string;
  description: string;
  isActive: boolean;
}
```

## 📝 Naming Conventions

### File Naming
- **Components**: `feature-name.component.ts`
- **Services**: `feature-name.service.ts`
- **Models**: `feature-name.models.ts`
- **Routes**: `feature-name.routes.ts`

### Class Naming
- **Components**: `FeatureNameComponent`
- **Services**: `FeatureNameService`
- **Interfaces**: `FeatureNameDto`, `CreateFeatureNameCommand`

### Selector Naming
- **Components**: `app-feature-name`
- **Dialogs**: `app-feature-name-dialog`

### Translation Keys
- **Feature prefix**: `FEATURE_NAME.`
- **Actions**: `FEATURE_NAME.CREATE`, `FEATURE_NAME.EDIT`
- **Messages**: `FEATURE_NAME.CREATED_SUCCESSFULLY`

## ✅ Integration Checklist

### Before Development
- [ ] API endpoints documented and tested
- [ ] Data models defined and approved
- [ ] UI/UX designs finalized
- [ ] Permissions and roles defined
- [ ] Translation keys prepared

### During Development
- [ ] Follow established file structure
- [ ] Use provided code templates
- [ ] Implement proper error handling
- [ ] Add form validation
- [ ] Include loading states
- [ ] Add permission checks

### After Development
- [ ] Test all CRUD operations
- [ ] Verify responsive design
- [ ] Test internationalization
- [ ] Validate accessibility
- [ ] Review code quality
- [ ] Update documentation

### Deployment Preparation
- [ ] Environment configurations updated
- [ ] API endpoints configured
- [ ] Translation files updated
- [ ] Route guards applied
- [ ] Performance optimized

## 🔧 Advanced Patterns

### Custom Validation Implementation

```typescript
// In dialog component
validateCustomRules(): boolean {
  const duplicateCheck = this.existingItems.some(item =>
    item.name.toLowerCase() === this.formData.name.toLowerCase() &&
    (!this.isEdit || item.id !== this.formData.id)
  );

  if (duplicateCheck) {
    this.nameControl.setErrors({ duplicate: true });
    return false;
  }
  return true;
}
```

### Permission-Based UI Elements

```html
<!-- In component template -->
<app-page-header
  [title]="'YOUR_FEATURE.TITLE'"
  [showCreateButton]="tokenService.hasPermission('YourFeature.Create')"
  (create)="onCreate()">
</app-page-header>
```

### Error Handling Pattern

```typescript
// In service
handleError(operation: string) {
  return (error: any): Observable<any> => {
    console.error(`${operation} failed:`, error);
    return throwError(() => error);
  };
}
```

---

*Follow this guide to maintain consistency across all modules in the application.*
