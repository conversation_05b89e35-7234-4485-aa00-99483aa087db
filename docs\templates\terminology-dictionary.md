# 📖 Terminology Dictionary
## Standardized Terms for Jadwa Web Application Development

> **Comprehensive dictionary of terms, patterns, and conventions used across all Jadwa web application templates and documentation**

---

## 🏗️ Architecture Terms

### 🎨 Frontend Architecture

| Term | Definition | Usage Example |
|------|------------|---------------|
| **Standalone Component** | Angular 18+ component that imports its own dependencies | `@Component({ standalone: true, imports: [...] })` |
| **Feature Module** | Business domain-specific module containing related components | `src/app/features/investment-funds/` |
| **Shared Component** | Reusable UI component used across multiple features | `src/app/shared/components/table/` |
| **Layout Component** | Structural component defining page layout | `src/app/core/layout/admin-layout/` |
| **Service Proxy** | Auto-generated TypeScript client from Swagger API | `InvestmentFundsServiceProxy` |
| **Custom Service** | Business logic wrapper around service proxy | `InvestmentFundsService` |

### ⚙️ Backend Integration

| Term | Definition | Usage Example |
|------|------------|---------------|
| **Swagger Endpoint** | API documentation endpoint | `http://************:44301/swagger/v2/swagger.json` |
| **NSwag Client** | Auto-generated TypeScript API client | Generated from Swagger specification |
| **Service Proxy** | Direct API communication layer | `[Controller]ServiceProxy` |
| **Base Response** | Standardized API response format | `{ statusCode, successed, message, data, errors }` |
| **Paginated Response** | Response format for list endpoints | Includes pagination metadata |
| **Command Pattern** | Request model for API operations | `Create[Entity]Command`, `Update[Entity]Command` |

---

## 🎯 Business Domain Terms

### 💼 Investment Management

| Term | Definition | Context |
|------|------------|---------|
| **Investment Fund** | Managed investment vehicle | Core business entity |
| **Fund Strategy** | Investment approach and methodology | Strategic planning component |
| **Fund Manager** | Professional managing investment funds | User role with specific permissions |
| **Board Secretary** | Administrative role for board meetings | User role with meeting management access |
| **Legal Council** | Legal advisory role | User role with legal document access |
| **Voting System** | Decision-making mechanism for fund governance | Feature for board decisions |

### 👥 User Management

| Term | Definition | Permissions |
|------|------------|-------------|
| **Admin** | System administrator | Full system access |
| **Fund Manager** | Investment professional | Fund-specific operations |
| **Board Secretary** | Meeting coordinator | Meeting and document management |
| **Legal Council** | Legal advisor | Legal document access |
| **Viewer** | Read-only access | View permissions only |

---

## 🔧 Technical Patterns

### 📦 Component Patterns

| Pattern | Description | File Structure |
|---------|-------------|----------------|
| **Feature Component** | Business-specific functionality | `[feature]/components/[component]/` |
| **List Component** | Data display with pagination | `[entity]-list.component.ts` |
| **Details Component** | Single item detailed view | `[entity]-details.component.ts` |
| **Form Component** | Data input and validation | `[entity]-form.component.ts` |
| **Modal Component** | Overlay dialog functionality | `[entity]-modal.component.ts` |

### 🔄 Service Patterns

| Pattern | Description | Implementation |
|---------|-------------|----------------|
| **CRUD Service** | Create, Read, Update, Delete operations | Standard service methods |
| **State Service** | Reactive state management | BehaviorSubject-based |
| **Transform Service** | Data transformation logic | API to frontend model conversion |
| **Validation Service** | Business rule validation | Input validation and business rules |
| **Cache Service** | Data caching mechanism | Observable-based caching |

---

## 🎨 UI/UX Terms

### 🖼️ Layout Components

| Component | Purpose | Location |
|-----------|---------|----------|
| **Page Header** | Page title and navigation | `app-page-header` |
| **Breadcrumb** | Navigation path indicator | `app-breadcrumb` |
| **Custom Button** | Standardized button component | `app-custom-button` |
| **Table Component** | Data table with pagination | `app-table` |
| **Modal Dialog** | Overlay dialog component | `app-modal` |

### 🎯 UI States

| State | Description | CSS Class |
|-------|-------------|-----------|
| **Loading State** | Data fetching in progress | `.loading` |
| **Empty State** | No data available | `.empty-state` |
| **Error State** | Error occurred | `.error-state` |
| **Success State** | Operation completed successfully | `.success-state` |
| **Disabled State** | Component not interactive | `.disabled` |

---

## 🌍 Internationalization Terms

### 🗣️ Language Support

| Term | Description | Code |
|------|-------------|------|
| **Primary Language** | Arabic (Right-to-Left) | `ar` |
| **Secondary Language** | English (Left-to-Right) | `en` |
| **RTL Layout** | Right-to-Left text direction | `dir="rtl"` |
| **LTR Layout** | Left-to-Right text direction | `dir="ltr"` |
| **Translation Key** | Internationalization identifier | `FEATURE.SECTION.KEY` |

### 📅 Localization

| Term | Description | Format |
|------|-------------|--------|
| **Hijri Date** | Islamic calendar date | Arabic locale |
| **Gregorian Date** | Western calendar date | English locale |
| **Number Format** | Locale-specific number display | Arabic/English numerals |
| **Currency Format** | Monetary value display | SAR currency |

---

## 🔐 Security Terms

### 🎫 Authentication

| Term | Description | Implementation |
|------|-------------|----------------|
| **JWT Token** | JSON Web Token for authentication | Bearer token in headers |
| **Token Refresh** | Automatic token renewal | Interceptor-based |
| **Session Management** | User session handling | Token-based sessions |
| **Route Guard** | Navigation protection | Angular route guards |

### 🛡️ Authorization

| Term | Description | Usage |
|------|-------------|-------|
| **Permission** | Specific action authorization | `[ENTITY]_[ACTION]` format |
| **Role** | Collection of permissions | User role assignment |
| **Access Control** | Permission-based feature access | Component-level checks |
| **Data Filtering** | Role-based data access | Service-level filtering |

---

## 📊 Data Terms

### 📋 Data Models

| Term | Description | Pattern |
|------|-------------|---------|
| **Entity** | Business data object | Core data structure |
| **DTO** | Data Transfer Object | API communication model |
| **Interface** | TypeScript type definition | Frontend data contract |
| **Command** | API request model | `Create[Entity]Command` |
| **Query** | API query parameters | Search and filter parameters |

### 🔄 Data Operations

| Operation | Description | HTTP Method |
|-----------|-------------|-------------|
| **Create** | Add new entity | POST |
| **Read** | Retrieve entity data | GET |
| **Update** | Modify existing entity | PUT |
| **Delete** | Remove entity | DELETE |
| **List** | Retrieve multiple entities | GET with pagination |

---

## 🧪 Testing Terms

### 🔬 Test Types

| Type | Description | Framework |
|------|-------------|-----------|
| **Unit Test** | Individual component/service testing | Jasmine + Karma |
| **Integration Test** | Component-service interaction testing | Angular Testing Utilities |
| **E2E Test** | End-to-end user workflow testing | Cypress/Protractor |
| **Accessibility Test** | WCAG compliance testing | axe-core |

### 📊 Test Metrics

| Metric | Description | Target |
|--------|-------------|--------|
| **Code Coverage** | Percentage of code tested | Minimum 80% |
| **Test Coverage** | Feature functionality tested | 100% critical paths |
| **Performance Test** | Response time validation | < 2 seconds API calls |
| **Accessibility Score** | WCAG compliance level | AA compliance |

---

## 📱 Responsive Design Terms

### 📐 Breakpoints

| Breakpoint | Screen Size | Usage |
|------------|-------------|-------|
| **xs** | < 576px | Mobile phones |
| **sm** | ≥ 576px | Small tablets |
| **md** | ≥ 768px | Tablets |
| **lg** | ≥ 992px | Desktops |
| **xl** | ≥ 1200px | Large desktops |
| **xxl** | ≥ 1400px | Extra large screens |

### 🎨 Design Patterns

| Pattern | Description | Implementation |
|---------|-------------|----------------|
| **Mobile-First** | Design starting from mobile | CSS media queries |
| **Progressive Enhancement** | Feature addition by screen size | Responsive components |
| **Touch-Friendly** | Mobile interaction optimization | Touch target sizing |
| **Adaptive Layout** | Layout changes by screen size | CSS Grid/Flexbox |

---

## 🔄 Development Workflow Terms

### 📋 Requirement Types

| Type | Description | Template |
|------|-------------|----------|
| **Functional Requirement** | Business functionality specification | requirement-specification-template |
| **Non-Functional Requirement** | Performance, security, usability | requirement-specification-template |
| **Technical Requirement** | Implementation specifications | All templates |
| **Integration Requirement** | System integration needs | api-integration-template |

### 🎯 Development Phases

| Phase | Description | Deliverables |
|-------|-------------|--------------|
| **Analysis** | Requirement gathering and analysis | Requirement documents |
| **Design** | Architecture and UI/UX design | Design specifications |
| **Implementation** | Code development | Working features |
| **Testing** | Quality assurance and validation | Test results |
| **Deployment** | Production release | Deployed application |

---

## 📚 Documentation Standards

### 📖 Document Types

| Type | Purpose | Location |
|------|---------|----------|
| **API Documentation** | Endpoint specifications | Swagger/OpenAPI |
| **Component Documentation** | Component usage guide | Inline comments |
| **Architecture Documentation** | System design overview | `docs/` directory |
| **User Documentation** | End-user guides | User manual |

### ✍️ Writing Standards

| Standard | Description | Example |
|----------|-------------|---------|
| **Clear Headings** | Descriptive section titles | `## User Authentication` |
| **Consistent Terminology** | Use dictionary terms | Always use "Service Proxy" |
| **Code Examples** | Practical implementation samples | TypeScript code blocks |
| **Visual Aids** | Diagrams and screenshots | Mermaid diagrams |

---

*Dictionary Version: 1.0*
*Last Updated: 2025-06-24*
*Created by: Jadwa Development Team*
