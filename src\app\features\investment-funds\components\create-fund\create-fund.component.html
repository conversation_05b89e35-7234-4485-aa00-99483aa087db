<div class="create-fund-page">

  <app-breadcrumb (onClickEvent)="onBreadcrumbClicked($event)" [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium" divider=">"></app-breadcrumb>
  <div class="mt-3">

    <app-page-header [title]="'INVESTMENT_FUNDS.CREATE_NEW_FUND' | translate">
    </app-page-header>
  </div>

  <div class="form-container mt-3">
    <app-form-builder 
    (dropdownChanged)="dropdownChanged($event)"
    [formControls]="formControls" [formGroup]="formGroup" [isFormSubmitted]="isValidationFire"(removeSelection)="onRemoveSelectedItem($event)"
      (dateSelected)="dateSelected($event)" (formSubmit)="onSubmit($event)" (fileUploaded)="onFileUpload($event)">
    </app-form-builder>

    <div class="actions justify-content-end">
      <app-custom-button [btnName]="'COMMON.CANCEL' | translate" (click)="router.navigate(['/admin/investment-funds'])"
        [buttonType]="buttonEnum.Secondary" [iconName]="IconEnum.cancel">
      </app-custom-button>

      <app-custom-button [btnName]="'COMMON.CREATE' | translate" [buttonType]="buttonEnum.Primary"
        [iconName]="IconEnum.verify" (click)="onSubmit(formGroup.value)">
      </app-custom-button>
    </div>
  </div>
</div>