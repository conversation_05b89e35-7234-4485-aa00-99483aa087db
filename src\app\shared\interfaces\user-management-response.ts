// user-management-response.model.ts
export interface UserDto {
  id: number;
  fullName: string;
  email: string;
  country: string;
  address: string;
}

export interface PaginatedResponse<T> {
  statusCode: number;
  successed: boolean;
  message: string;
  data: T[];
  errors: string[];
  currentPage: number;
  totalCount: number;
  totalPages: number;
  pageSize: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}
