// user-management.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
// models/fund.dto.ts
export interface AddFundCommand {
  id: number;
  name: string;
  strategyId: number;
  strategyName: string;
  status: string;
  statusId: number;
  initiationDate: string; // ISO string
  exitDate: string; // ISO string
  oldCode: number;
  propertiesNumber: number;
  attachmentId: number;
  votingTypeId: number;
  legalCouncilId: number;
  fundManagers: number[];
  fundBoardSecretaries: number[];
}
export interface EditExitDateCommand extends AddFundCommand {
  exitDate: string;
}
export interface FundNotification {
  title: string;
  message: string;
  createdAt: string;
  id: number;
}

export interface FundHistory {
  userName: string | null;
  roleName: string;
  statusId: number;
  statusName: string;
  createdAt: string;
  id: number;
}

export interface FundDetailsDto {
  propertiesNumber: number;
  decisionCount: number;
  evaluationCount: number;
  documentCount: number;
  meetingCount: number;
  memberCount: number;
  fundNotifications: FundNotification[];
  fundHistory: FundHistory[];
  exitDate: string;
  name: string;
  strategyId: number;
  strategyName: string;
  status: string;
  statusId: number;
  initiationDate: string;
  id: number;
  attachmentName:string;
}

export interface ApiResponse<T> {
  statusCode: number;
  successed: boolean;
  message: string;
  data: T;
  errors: any;
}


export interface exitDate {
  id: number,
  exitDate: string
}

@Injectable({
  providedIn: 'root',
})
export class FundsService {
  private baseUrl = environment.apiUrl + '/api/Funds';

  constructor(private http: HttpClient) {}

  addFund(payload: AddFundCommand): Observable<any> {
    return this.http.post(`${this.baseUrl}/AddFund`, payload);
  }
  EditFund(payload: AddFundCommand): Observable<any> {
    return this.http.put(`${this.baseUrl}/EditFund`, payload);
  }

  getFundDetailsById(id: number): Observable<any> {
    const url = `${this.baseUrl}/GetFunDetails?id=${id}`;
    return this.http.get(url);
  }

  editFundExitDate(exitDate: exitDate): Observable<any> {
    const url = `${this.baseUrl}/EditFundExitDate`;
    return this.http.put(url, exitDate);
  }

  getFundDetails(id: any): Observable<ApiResponse<FundDetailsDto>> {
    
    return this.http.get<ApiResponse<FundDetailsDto>>(`${this.baseUrl}`, {
      params: { id },
    });
  }
}
