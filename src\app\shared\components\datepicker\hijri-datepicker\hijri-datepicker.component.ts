import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Output,
  OnInit,
  Injectable,
  Input,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  NgbDatepickerI18n,
  NgbDateStruct,
  NgbCalendar,
  NgbDatepickerModule,
  NgbModule,
  NgbCalendarIslamicUmalqura,
} from '@ng-bootstrap/ng-bootstrap';

const WEEKDAYS = ['ن', 'ث', 'ر', 'خ', 'ج', 'س', 'ح'];
const MONTHS = [
  'محرم',
  'صفر',
  'ربيع الأول',
  'ربيع الآخر',
  'جمادى الأولى',
  'جمادى الآخرة',
  'رجب',
  'شعبان',
  'رمضان',
  'شوال',
  'ذو القعدة',
  'ذو الحجة',
];

@Injectable()
export class IslamicI18n extends NgbDatepickerI18n {
  getMonthShortName(month: number) {
    return MONTHS[month - 1];
  }

  getMonthFullName(month: number) {
    return MONTHS[month - 1];
  }

  getWeekdayLabel(weekday: number) {
    return WEEKDAYS[weekday - 1];
  }

  getDayAriaLabel(date: NgbDateStruct): string {
    return `${date.day}-${date.month}-${date.year}`;
  }
}

@Component({
  standalone: true,
  selector: 'arabdt-hijri-datepicker',
  templateUrl: './hijri-datepicker.component.html',
  styleUrls: ['./hijri-datepicker.component.scss'],
  imports: [
    NgbDatepickerModule,
    NgbModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  providers: [
    { provide: NgbCalendar, useClass: NgbCalendarIslamicUmalqura },
    { provide: NgbDatepickerI18n, useClass: IslamicI18n },
  ],
})
export class HijriDatepickerComponent {
  @Output() dateChange = new EventEmitter<NgbDateStruct>();
  @Output() onBlur = new EventEmitter();
  @Input() placeholder: string | undefined;
  @Input() model: NgbDateStruct | undefined;
  @Input() minDate!: NgbDateStruct;
  @Input() maxDate!: NgbDateStruct;
  @Input() disabled: boolean | undefined;
  @Input() isInvalid: boolean | undefined;

  constructor() {}
  date: { year: number; month: number; } | undefined;

  onDateSelect() {
    this.dateChange.emit(this.model);
    // Hide the datepicker after selection
    if (this.model) {
      setTimeout(() => {
        // Use setTimeout to ensure the value is emitted before hiding
        const event = new Event('click', { bubbles: true });
        document.dispatchEvent(event);
      }, 100);
    }
  }
  
  onDateSelectBlur() {
    this.onBlur.emit();
  }
}
