.search-input-container {
  width: 263px;
  
  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    
    .search-input {
      width: 100%;
      height: 40px;
      padding: 0px 12px;
      border: 1px solid #E0E0E0;
      border-radius: 4px;
      font-size: 14px;
      
      &:focus {
        outline: none;
        border-color: #2F80ED;
        box-shadow: 0 0 0 2px rgba(47, 128, 237, 0.2);
      }
      
      &:disabled {
        background-color: #F2F2F2;
        cursor: not-allowed;
      }
    }
    
    .search-icons {
      position: absolute;
      right: 12px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 10px;
      
      .search-icon {
        color: #828282;
        font-size: 14px;
      }
      
      .clear-button {
        background: none;
        border: none;
        padding: 0;
        cursor: pointer;
        color: #BDBDBD;
        font-size: 12px;
        
        &:hover {
          color: #4F4F4F;
        }
        
        &:disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }
    }
  }
}

// RTL Support
:host-context([dir="rtl"]) {
  .search-input-container {
    .search-input-wrapper {
      .search-icons {
        right: auto;
        left: 12px;
      }
    }
  }
}
