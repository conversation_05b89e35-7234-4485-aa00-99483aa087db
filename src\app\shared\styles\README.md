# Shared Form Styling Guide

This directory contains shared SCSS files that provide consistent styling patterns across the application.

## Form Container Pattern

### Overview
The `_form-container.scss` file provides a standardized styling pattern for all form components in the application. This ensures visual consistency and reduces code duplication.

### Usage

#### 1. Import the shared styles in your component SCSS file:
```scss
// Use relative path from your component to the shared styles
@import '../../../shared/styles/form-container'; // For components in features/*/components/*
@import '../../../../shared/styles/form-container'; // For components in features/*/*/components/*
```

#### 2. Use the standard HTML structure:
```html
<div class="your-component-page">
  <!-- Breadcrumb -->
  <app-breadcrumb [breadcrumbs]="breadcrumbItems"></app-breadcrumb>
  
  <div class="mt-3">
    <!-- Page Header -->
    <app-page-header [title]="'YOUR_PAGE_TITLE' | translate"></app-page-header>
  </div>

  <div class="form-container mt-3">
    <form [formGroup]="yourForm" (ngSubmit)="onSubmit()" novalidate>
      <app-form-builder
        [formGroup]="yourForm"
        [formControls]="formControls"
        [isFormSubmitted]="isFormSubmitted"
        (controlFocused)="onControlFocus($event.event, $event.control)"
        (valueChanged)="onValueChange($event.event, $event.control)"
        (keyPressed)="onKeyPressed($event.event, $event.control)"
        (checkboxChanged)="onCheckboxChange($event.event, $event.control)"
        (dateSelected)="onDateSelected($event.event, $event.control)"
        (dropdownChanged)="onDropdownChange($event.event, $event.control)"
        (fileUploaded)="onFileUploaded($event.file, $event.control)">
      </app-form-builder>

      <div class="actions justify-content-end">
        <app-custom-button
          [btnName]="'COMMON.CANCEL' | translate"
          [buttonType]="ButtonTypeEnum.Secondary"
          [disabled]="isLoading"
          (click)="onCancel()">
        </app-custom-button>

        <app-custom-button
          [btnName]="'COMMON.SAVE' | translate"
          [buttonType]="ButtonTypeEnum.Primary"
          [disabled]="isLoading"
          (click)="onSubmit()">
        </app-custom-button>
      </div>
    </form>
  </div>
</div>
```

#### 3. Component SCSS structure:
```scss
// Your Component Styles - Using shared form container pattern
@import '../../../shared/styles/form-container'; // Adjust path as needed

// Component-specific styles can be added here if needed
.your-component-page {
  // Add any component-specific styles here
}
```

### Features Included

#### Layout & Spacing
- Consistent padding and margins
- Responsive grid system for form controls
- Proper spacing between form elements

#### Form Control Widths
- `.col-12` - Full width (100%)
- `.col-md-6` - Half width (48% with 2% margin)
- `.col-md-4` - Third width (31.33% with 2% margin)
- `.col-md-3` - Quarter width (23% with 2.66% margin)

#### Action Buttons
- Consistent button spacing and alignment
- Support for different justification options:
  - `.justify-content-end` (default)
  - `.justify-content-center`
  - `.justify-content-start`

#### Form Validation
- Consistent styling for valid/invalid states
- Error and success message styling
- Focus states with proper color coding

#### Responsive Design
- Mobile-first approach
- Stacked layout on small screens
- Full-width buttons on mobile

#### RTL Support
- Proper margin handling for right-to-left languages
- Reversed button alignment for RTL
- Consistent spacing in both directions

#### Additional Features
- Loading state overlay
- Dark theme support (future-ready)
- Print-friendly styles
- Accessibility considerations

### Examples

#### Existing Components Using This Pattern
- `create-user` component
- `create-fund` component

#### Form Control Configuration
When setting up form controls, use the appropriate class for desired width:

```typescript
{
  formControlName: 'firstName',
  type: InputType.Text,
  class: 'col-md-6', // Half width
  // ... other properties
},
{
  formControlName: 'description',
  type: InputType.Textarea,
  class: 'col-12', // Full width
  // ... other properties
}
```

### Best Practices

1. **Always import the shared styles** instead of duplicating CSS
2. **Use the standard HTML structure** for consistency
3. **Follow the responsive class naming** for form controls
4. **Add component-specific styles** only when necessary
5. **Test on both LTR and RTL layouts** if your app supports multiple languages
6. **Ensure proper form validation styling** is applied

### Customization

If you need to customize the shared styles for a specific component:

```scss
@import '../../../shared/styles/form-container'; // Adjust path as needed

.your-component-page {
  .form-container {
    // Override specific properties
    background: #ffffff;
    border: 2px solid #007bff;
    
    .actions {
      // Custom button styling
      gap: 2rem;
    }
  }
}
```

### Migration Guide

To migrate existing form components to use this pattern:

1. Replace custom form container styles with the import
2. Update HTML structure to match the standard pattern
3. Ensure form controls use the appropriate responsive classes
4. Test the component in different screen sizes and languages
5. Remove any duplicate CSS that's now covered by the shared styles

This standardization improves maintainability, ensures consistency, and makes it easier to implement design changes across all forms in the application.
