import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-voting',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './voting.component.html',
  styleUrls: ['./voting.component.scss']
})
export class VotingComponent {
  userName: string = '<PERSON>';
  userImage: string = 'assets/images/profile-placeholder.jpg';
  remainingVotes: number = 3;

  // Box counts
  totalBoxes: number = 7;
  activeBoxes: number = 3;
  inactiveBoxes: number = 2;

  constructor() {}
} 