<div class="filter-dialog-container p-4">
    <h3 class="header font-weight-bold">
        <span>
            <img src="assets/images/filter-1.png" alt="filter">
        </span>
        {{'USER_MANAGEMENT.FILTERS.ADVANCED_FILTERS' | translate}}
    </h3>

    <!-- Form -->
    <app-form-builder
        [formControls]="formControls"
        [formGroup]="formGroup"
        [isFormSubmitted]="isFormSubmitted"
        (dropdownChanged)="dropdownChanged($event)">
    </app-form-builder>

    <div class="mt-3 row">
        <div class="col-12 mb-3">
            <app-custom-button
                class="w-100 py-2 fs-14"
                [btnName]="'USER_MANAGEMENT.FILTERS.APPLY_FILTERS' | translate"
                [buttonType]="buttonEnum.Primary"
                [iconName]="IconEnum.verify"
                (click)="applyFilters()">
            </app-custom-button>
        </div>
        <div class="col-6">
            <app-custom-button
                class="w-100 fs-14"
                [btnName]="'USER_MANAGEMENT.FILTERS.RESET' | translate"
                [buttonType]="buttonEnum.OutLine"
                [iconName]="IconEnum.reset"
                (click)="resetFilters()">
            </app-custom-button>
        </div>
        <div class="col-6">
            <app-custom-button
                class="w-100 fs-14"
                [btnName]="'COMMON.CANCEL' | translate"
                (click)="closeDialog()"
                [buttonType]="buttonEnum.Secondary"
                [iconName]="IconEnum.cancel">
            </app-custom-button>
        </div>
    </div>
</div>
