.member-card {
  background: #f6faff;
  padding: 12px 10px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
  height: auto;
  border-radius: 16px;
  border: 1px solid #eaeef1;


  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #d1d5db;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 19px;

    .title {
      color: #282828;
      font-size: 14px;
      font-weight: 400;
      line-height: 16px;
      margin-bottom: 0;
    }
    .card-actions {
      display: flex;
      gap: 8px;

      padding: 0;
      margin: 0;

      .action-btn {
        width: 36px;
        height: 36px;
        border: none;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        background-color: transparent;
        border: 1px solid transparent;
      }
    }

    .member-status {
      .status-badge {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;


        &.active {
          background-color: #f1faf1;
          color: #27ae60;
        }

        &.inactive {
          background-color: #e0e0e0;
          color: #828282;
        }
      }
    }
  }

  .member-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 14px;

    .member-avatar {
      .avatar-img {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        object-fit: cover;
        border: 1px solid #e5e7eb;
        background-color: #f9fafb;
      }
    }

    .member-details {
      .member-name {
        margin: 0 0 4px 0;

        color: #00205a;
        font-size: 16px;
        font-weight: 500;
        line-height: 22px;
      }

      .member-role {
        margin: 0;
        color: #828282;
        font-size: 10px;
        font-weight: 400;
        line-height: 16px;
      }
    }
  }

  .card-body {
    .member-attributes {
      .attribute-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .attribute-label {
          font-size: 14px;
          color: #6b7280;
          font-weight: 400;
        }

        .attribute-value {
          font-size: 14px;
          color: #111827;
          font-weight: 500;

          &.chairman-badge {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #dc2626;
            font-weight: 600;

            .chairman-icon {
              width: 16px;
              height: 16px;
            }
          }
        }
      }
      .member-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .meta-item {
          margin-bottom: 0px;
          font-size: 13px;

          .meta-label {
            color: #828282;
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            margin-bottom: 4px;
          }

          .gregorian {
            color: #00205A;
            font-size: 14px;
            font-weight: 400;
            line-height: 16px;
          }

          .hijri {
            color: #828282;
            font-size: 10px;
            font-weight: 400;
            line-height: 16px;
            margin-bottom: 5px;


          }

          .meta-value {
            color: #333;
            font-weight: 600;
          }
        }
        .status {
          display: flex;
          align-items: center;
          gap: 8px;
          height: 24px;
          border-radius: 20px;
          padding: 10px;

          font-size: 14px;
          font-weight: 400;
          line-height: 16px;

          .circle {
            width: 12px;
            height: 12px;
            border-radius: 50%;
          }

          &.active {
            background-color: #f1faf1;
            color: #27ae60;
          }

          &.inactive {
            background-color: #e0e0e0;
            color: #828282;
          }
        }
      }
    }
  }

  .card-actions {
    display: flex;
    gap: 8px;
    // margin-top: 16px;
    // padding-top: 16px;

    .btn {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      font-size: 12px;
      border-radius: 6px;
      transition: all 0.2s ease;

      .action-icon {
        width: 14px;
        height: 14px;
      }

      &.btn-outline-primary {
        border-color: #1976d2;
        color: #1976d2;

        &:hover {
          background-color: #1976d2;
          color: white;
        }
      }

      &.btn-outline-danger {
        border-color: #d32f2f;
        color: #d32f2f;

        &:hover {
          background-color: #d32f2f;
          color: white;
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] .member-card {
  .card-header .member-info {
    display: flex;
    flex-direction: row-reverse;

    .member-details {
      text-align: right;
    }
  }

  .card-actions {
    flex-direction: row-reverse;
  }

  .attribute-item {
    .chairman-badge {
      flex-direction: row-reverse;
    }

    .attribute-label {
      text-align: right;
    }

    .attribute-value {
      text-align: left;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .member-card {
    padding: 16px;

    .card-header {
      flex-direction: column;
      gap: 12px;
      align-items: center;

      .member-status {
        align-self: flex-end;
      }
    }

    .card-actions {
      flex-direction: column;

      .btn {
        justify-content: center;
      }
    }
  }
}
