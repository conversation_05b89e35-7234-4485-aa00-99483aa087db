# 🧪 Multi-Role Selection E2E Testing Instructions

## 📋 Complete Step-by-Step Guide

This document provides comprehensive instructions for running automated Playwright tests to validate the multi-role selection logic implemented in the user management component.

## 🚀 Quick Start (5 Steps)

### Step 1: Start the Application
```bash
cd c:\Workspace\jadwa.web
npm run serve:local
```
**Wait for**: Application to be available at `http://localhost:4200`

### Step 2: Install Playwright Browsers (if not already done)
```bash
npx playwright install
```

### Step 3: Run Authentication Setup
```bash
npx playwright test tests/e2e/auth.setup.ts --project=setup
```

### Step 4: Run Multi-Role Selection Tests
```bash
npx playwright test tests/e2e/user-management/multi-role-selection.spec.ts --project=chromium-ar
```

### Step 5: View Test Results
```bash
npx playwright show-report
```

## 🎯 What the Tests Validate

### ✅ **Valid Multi-Select Scenarios (Should PASS)**
1. **Fund Manager + Board Member** - Multi-select enabled
2. **Associate Fund Manager + Board Member** - Multi-select enabled
3. **Different selection orders** - Order doesn't matter

### ❌ **Invalid Multi-Select Scenarios (Should RESTRICT to single selection)**
1. **Fund Manager + Legal Council** - Only one role remains
2. **Associate Fund Manager + Finance Controller** - Only one role remains  
3. **Board Member + Board Secretary** - Only one role remains
4. **Any single role selection** - Multi-select disabled
5. **More than 2 roles** - Maximum 2 roles enforced

### 🔧 **Additional Validations**
- Form validation works with multi-role selection
- No console errors during role selection
- Performance is acceptable (< 3 seconds per operation)
- UI responds smoothly to role changes

## 📊 Expected Test Results

### ✅ **Success Indicators**
```
✓ should load create user page successfully
✓ should restrict to single role selection by default
✓ should enable multi-select for Fund Manager + Board Member combination
✓ should enable multi-select for Associate Fund Manager + Board Member combination
✓ should test different selection orders - Board Member first, then Fund Manager
✓ should restrict invalid combination: Fund Manager + Legal Council
✓ should restrict invalid combination: Associate Fund Manager + Finance Controller
✓ should restrict invalid combination: Board Member + Board Secretary
✓ should prevent selection of more than 2 roles
✓ should handle form validation with multi-role selection
✓ should not show console errors during role selection
✓ should maintain smooth user experience during role selection

12 passed (Xm Xs)
```

### ❌ **Failure Indicators**
- Any test marked with ❌ or "failed"
- Console errors during execution
- Timeouts or element not found errors
- Performance issues (operations > 3 seconds)

## 🐛 Troubleshooting

### Issue 1: Application Not Running
**Error**: `Connection refused to localhost:4200`
**Solution**:
```bash
cd c:\Workspace\jadwa.web
npm run serve:local
# Wait for "Local: http://localhost:4200" message
```

### Issue 2: Authentication Failed
**Error**: `Login failed or timeout`
**Solutions**:
1. Verify credentials work manually:
   - Go to `http://localhost:4200`
   - Login with `superadmin` / `123Pa$$word!`
2. Re-run auth setup:
   ```bash
   npx playwright test tests/e2e/auth.setup.ts --project=setup
   ```

### Issue 3: Element Not Found
**Error**: `Locator not found`
**Solutions**:
1. Check if the create user page loads manually:
   - Navigate to `http://localhost:4200/admin/user-management/create`
2. Verify roles dropdown is visible
3. If UI changed, update selectors in `tests/e2e/page-objects/create-user.page.ts`

### Issue 4: Tests Running Too Slow
**Solutions**:
1. Run with single browser:
   ```bash
   npx playwright test tests/e2e/user-management/multi-role-selection.spec.ts --project=chromium-ar
   ```
2. Run in headless mode (default)
3. Check system resources

## 🔍 Debug Mode

For detailed debugging, run tests with visible browser:
```bash
npx playwright test tests/e2e/user-management/multi-role-selection.spec.ts --headed --debug
```

This will:
- Show browser window
- Pause at each step
- Allow manual inspection
- Provide debugging tools

## 📈 Advanced Usage

### Run Specific Test Cases
```bash
# Run only valid combination tests
npx playwright test tests/e2e/user-management/multi-role-selection.spec.ts -g "should enable multi-select"

# Run only invalid combination tests  
npx playwright test tests/e2e/user-management/multi-role-selection.spec.ts -g "should restrict invalid"
```

### Run with Different Browsers
```bash
# Arabic locale
npx playwright test tests/e2e/user-management/multi-role-selection.spec.ts --project=chromium-ar

# English locale
npx playwright test tests/e2e/user-management/multi-role-selection.spec.ts --project=chromium-en

# Firefox
npx playwright test tests/e2e/user-management/multi-role-selection.spec.ts --project=firefox-ar
```

### Generate Different Report Formats
```bash
# HTML report (default)
npx playwright show-report

# JSON report
cat test-results/results.json

# JUnit XML
cat test-results/junit.xml
```

## 🎬 Test Artifacts

After running tests, check these locations for debugging:
- **Screenshots**: `test-results/artifacts/` (for failed tests)
- **Videos**: `test-results/artifacts/` (for failed tests)
- **Traces**: `test-results/artifacts/` (detailed execution traces)
- **HTML Report**: `test-results/html-report/`

## ✅ Validation Checklist

Before considering the implementation complete, ensure:

- [ ] Application starts successfully on `http://localhost:4200`
- [ ] Login works with `superadmin` / `123Pa$$word!`
- [ ] Create user page loads at `/admin/user-management/create`
- [ ] Roles dropdown is visible and functional
- [ ] All 12 test cases pass
- [ ] No console errors in browser
- [ ] Performance is acceptable (< 3 seconds per operation)
- [ ] HTML report shows all tests passed

## 🎯 Success Criteria

The multi-role selection logic is correctly implemented when:

1. **✅ Valid Combinations Work**: Fund Manager + Board Member and Associate Fund Manager + Board Member allow multi-select
2. **❌ Invalid Combinations Restricted**: All other combinations restrict to single selection  
3. **🔢 Maximum Limit Enforced**: No more than 2 roles can be selected
4. **🚀 Performance Acceptable**: Role selection operations complete quickly
5. **🐛 No Errors**: No JavaScript console errors during role selection
6. **📝 Form Integration**: Form validation works correctly with multi-role selection

## 📞 Need Help?

If tests fail or you encounter issues:
1. Follow the troubleshooting guide above
2. Run tests in debug mode with `--headed --debug`
3. Check test artifacts (screenshots, videos, traces)
4. Verify the application is running and accessible
5. Ensure credentials are correct and working
