@import "../../../../../assets/scss/variables";


.fund-details-container {
    background-color: $card-background ;
    border: 1px solid $border-color;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header {
  margin: 0 0 42px;
font-size: 24px;
font-weight: 700;
line-height:  20px;
}
.fund-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;

  .header-actions {
    display: flex;
    gap: 12px;

    button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      color: #666;
      transition: color 0.2s ease;

      &:hover {
        color: #333;
      }
      .fa-pencil{
        color: #EAA300;
      }
      .expand{
        color: #00205A;
      }
      i {
        font-size: 16px;
      }
    }
  }

  .section-title {
    font-size: 16px;
    font-weight: 700;
    margin: 0;
    line-height:  22px;
  }
}

hr{
  color:$border-hr-color;
  border: 1px solid;
  margin: 0;
}

.fund-details-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: 500px;
  }
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  padding: 16px 0;

  .detail-item {
    display: flex;
    flex-direction: column;
    label {
      display: block;
      font-size: 14px;
      color: #828282;
    font-weight: 700;
      margin-bottom: 8px;
    }

    button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      color: #666;
      transition: color 0.2s ease;

      &:hover {
        color: #333;
      }
      .fa-pencil{
        color: #EAA300;
      }
      .expand{
        color: #00205A;
      }
      i {
        font-size: 16px;
      }
    }

    .value {
      font-size: 16px;
      color: #00205A;
      font-weight: 500;
    }

    .date-value {
      display: flex;
      flex-direction: row;
      gap: 6px;

      .gregorian {
        font-size: 16px;
        color: #00205A;
        font-weight: 500;
      }

      .hijri {
        font-size: 12px;
        color: #828282;
      }
    }

    .status {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      height: 24px;
      border-radius: 20px;
    padding: 10px;

    .circle{
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }

      &.active {
        color: #28a745;

        span {
          color: #28a745;
        }
      }

      span {
        font-size: 12px;
      }
    }
    .status-new span {
      background-color: gray;
    }

    .status-under-construction {
      background-color: #E5EEFB;
      color: #2F80ED;

      span {
        background-color: #2F80ED;
    }
  }
    .status-waiting {
      color: #FF5F3D;

      background-color: #FDF1EB;
      span {
        background-color: #FF5F3D;
    }
    }
    .status-active {
      color: #27AE60;

      background-color: #F1FAF1;
      span {
        background-color: #27AE60;
    }
  }
    .status-exited{
      color: #828282;

      background-color: #E0E0E0;
      span {
        background-color: #828282;
    }
  }
    .custom-btn{
      color: #00205A;
      border: 1px solid #00205A;
      background-color: #fff;
      border-radius: 8px;

     cursor: default;
     padding: 0 31.426px;
     height: 28px;
     font-weight: 400;

    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .details-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .details-grid {
    grid-template-columns: 1fr;
  }
}
