import {
  APP_INITIALIZER,
  ApplicationConfig,
  importProvidersFrom,
  provideZoneChangeDetection,
} from '@angular/core';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import {
  HttpClient,
  provideHttpClient,
  withInterceptors,
} from '@angular/common/http';
import { tokenInterceptor } from './core/interceptors/token.interceptor';
import { errorInterceptor } from './core/interceptors/error.interceptor';

import { provideAnimations } from '@angular/platform-browser/animations';
import { provideToastr } from 'ngx-toastr';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { MatDialogModule } from '@angular/material/dialog';
import {
  API_BASE_URL,
  AuthenticationServiceProxy,
  AuthorzationServiceProxy,
  UsersServiceProxy,
  UserManagementServiceProxy,
  FileManagmentServiceProxy,
  FundsServiceProxy,
  TypesServiceProxy,
  StrategiesServiceProxy,
  NotificationServiceProxy,
  ResolutionsServiceProxy,
  BoardMembersServiceProxy,
  MinIOFileServiceProxy,
  DocumentServiceProxy,
  ApiServiceProxy,
} from './core/api/api.generated';
import { environment } from '../environments/environment';
import { TokenService } from './features/auth/services/token.service';

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}
export function initToken(tokenService: TokenService) {
  return () => tokenService.init();
}
export const appConfig: ApplicationConfig = {
  providers: [
    provideHttpClient(withInterceptors([tokenInterceptor, errorInterceptor])),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideAnimations(),
    provideToastr(),
    TranslateService,
    importProvidersFrom(
      MatDialogModule,
      TranslateModule.forRoot({
        defaultLanguage: 'ar',
        useDefaultLang: true,
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient],
        },
      })
    ),
    // API Configuration
    { provide: API_BASE_URL, useValue: environment.apiUrl },
    {
      provide: APP_INITIALIZER,
      useFactory: initToken,
      deps: [TokenService],
      multi: true,
    },

    // API Service Proxies
    AuthenticationServiceProxy,
    AuthorzationServiceProxy,
    UsersServiceProxy,
    UserManagementServiceProxy,
    FileManagmentServiceProxy,
    FundsServiceProxy,
    TypesServiceProxy,
    StrategiesServiceProxy,
    NotificationServiceProxy,
    ResolutionsServiceProxy,
    BoardMembersServiceProxy,
    MinIOFileServiceProxy,
    DocumentServiceProxy,
    ApiServiceProxy,
  ],
};
