# Multi-Role Selection E2E Testing Guide

## 🎯 Overview

This guide provides step-by-step instructions for running comprehensive Playwright end-to-end tests to validate the multi-role selection logic implemented in the user management component.

## 📋 Prerequisites

### 1. Application Setup
Ensure the Jadwa web application is running locally:
```bash
cd c:\Workspace\jadwa.web
npm run serve:local
```
The application should be accessible at `http://localhost:4200`

### 2. Test Credentials
The tests use these credentials (ensure they work in your environment):
- **Username**: `superadmin`
- **Password**: `123Pa$$word!`

### 3. Playwright Installation
Ensure Playwright and browsers are installed:
```bash
npm install
npx playwright install
```

## 🚀 Quick Start

### Option 1: Using NPM Scripts
```bash
# Run all multi-role selection tests
npm run test:e2e -- tests/e2e/user-management/multi-role-selection.spec.ts

# Run with specific browser
npm run test:e2e -- tests/e2e/user-management/multi-role-selection.spec.ts --project=chromium-ar

# Run in headed mode (visible browser)
npm run test:e2e:headed -- tests/e2e/user-management/multi-role-selection.spec.ts
```

### Option 2: Using Custom Scripts

**Windows (PowerShell):**
```powershell
# Navigate to project directory
cd c:\Workspace\jadwa.web

# Run the test script
.\tests\e2e\run-multi-role-tests.ps1

# Run in debug mode
.\tests\e2e\run-multi-role-tests.ps1 --debug
```

**Linux/Mac (Bash):**
```bash
# Make script executable
chmod +x tests/e2e/run-multi-role-tests.sh

# Run the test script
./tests/e2e/run-multi-role-tests.sh

# Run in debug mode
./tests/e2e/run-multi-role-tests.sh --debug
```

## 🧪 Test Scenarios Covered

### ✅ Valid Multi-Select Scenarios
1. **Fund Manager + Board Member**
   - Select Fund Manager first, then Board Member
   - Verify both roles are selected
   - Confirm multi-select is enabled

2. **Associate Fund Manager + Board Member**
   - Select both roles simultaneously
   - Verify both roles are selected
   - Confirm multi-select is enabled

3. **Different Selection Orders**
   - Select Board Member first, then Fund Manager
   - Verify order doesn't affect functionality

### ❌ Invalid Multi-Select Scenarios
1. **Fund Manager + Legal Council**
   - Attempt to select both roles
   - Verify only one role remains selected
   - Confirm multi-select is disabled

2. **Associate Fund Manager + Finance Controller**
   - Attempt to select both roles
   - Verify restriction to single selection

3. **Board Member + Board Secretary**
   - Attempt to select both roles
   - Verify restriction to single selection

### 🔧 Additional Validations
1. **Single Role Selection**
   - Select any single role
   - Verify multi-select is disabled

2. **Maximum Role Limit**
   - Attempt to select more than 2 roles
   - Verify maximum of 2 roles enforced

3. **Form Integration**
   - Select valid multi-role combination
   - Fill required form fields
   - Verify form validation works correctly

4. **Performance Testing**
   - Measure role selection response time
   - Verify operations complete within 3 seconds

5. **Error Monitoring**
   - Monitor browser console for JavaScript errors
   - Verify no errors during role selection

## 📊 Expected Results

### ✅ Pass Criteria
- **Valid Combinations**: Fund Manager + Board Member and Associate Fund Manager + Board Member allow multi-select
- **Invalid Combinations**: All other combinations restrict to single selection
- **Performance**: Role selection operations complete within 3 seconds
- **No Errors**: No console errors during role selection
- **Form Integration**: Form validation works correctly with multi-role selection

### ❌ Fail Criteria
- Multi-select enabled for invalid role combinations
- More than 2 roles can be selected simultaneously
- Console errors appear during role selection
- Poor performance (operations take > 3 seconds)
- Form validation issues with multi-role selection

## 🐛 Debugging and Troubleshooting

### Common Issues

1. **Application Not Running**
   ```bash
   # Error: Connection refused to localhost:4200
   # Solution: Start the application
   npm run serve:local
   ```

2. **Authentication Failures**
   ```bash
   # Error: Login failed or timeout
   # Solution: Verify credentials and re-run auth setup
   npx playwright test tests/e2e/auth.setup.ts --project=setup
   ```

3. **Element Not Found**
   ```bash
   # Error: Locator not found
   # Solution: Check if UI has changed, update selectors
   # Edit: tests/e2e/page-objects/create-user.page.ts
   ```

### Debug Mode Execution
```bash
# Run with browser visible and debugging tools
npx playwright test tests/e2e/user-management/multi-role-selection.spec.ts --headed --debug

# Run with UI mode for interactive debugging
npx playwright test tests/e2e/user-management/multi-role-selection.spec.ts --ui
```

### Test Artifacts
- **Screenshots**: Captured automatically on test failures
- **Videos**: Recorded for failed tests
- **Traces**: Available for detailed debugging
- **Console Logs**: Captured and available in reports

## 📈 Test Reports

### HTML Report
```bash
# Generate and view comprehensive HTML report
npx playwright show-report
```

### Other Report Formats
- **JSON**: `test-results/results.json`
- **JUnit**: `test-results/junit.xml`
- **Allure**: `test-results/allure-results/`

## 🔄 Continuous Integration

### GitHub Actions Example
```yaml
name: Multi-Role Selection E2E Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: npx playwright install
      - run: npm run serve:local &
      - run: npx playwright test tests/e2e/user-management/multi-role-selection.spec.ts
```

## 📝 Test Maintenance

### Updating Tests for UI Changes
1. **Update Selectors**: Modify `tests/e2e/page-objects/create-user.page.ts`
2. **Add New Scenarios**: Extend `tests/e2e/user-management/multi-role-selection.spec.ts`
3. **Update Credentials**: Modify `tests/e2e/auth.setup.ts` if needed

### Best Practices
- Keep page object models updated with UI changes
- Add new test cases for new role combinations
- Monitor test execution time and optimize if needed
- Regularly update Playwright and browser versions

## 🎯 Success Metrics

After running the tests, you should see:
- ✅ All test cases passing
- 🚀 Fast execution times (< 3 seconds per role selection)
- 🔍 No console errors
- 📊 Comprehensive test coverage report
- 🎬 Screenshots/videos only for failed tests (indicating success)

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review test artifacts (screenshots, videos, traces)
3. Run tests in debug mode for detailed investigation
4. Verify the application is running and accessible
5. Ensure test credentials are valid
