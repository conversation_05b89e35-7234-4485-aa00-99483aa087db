import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import {
  ColumnTypeEnum,
  DataHandlingType,
} from '../../../core/enums/column-type';
import {
  ActionDisplayMode,
  ITableColumn,
  SwitchToggleEvent,
  TableActionEvent,
  TextLinkClickEvent,
} from '../../../core/gl-interfaces/I-table/i-table';
import { CommonModule } from '@angular/common';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CdkTableModule } from '@angular/cdk/table';
import { SelectionModel } from '@angular/cdk/collections';
import {
  MatPaginator,
  MatPaginatorModule,
  PageEvent,
} from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-table',
  standalone: true,
  imports: [
    MatTableModule,
    CommonModule,
    MatSlideToggleModule,
    MatCheckboxModule,
    MatPaginatorModule,
    MatSortModule,
    MatMenuModule,
    MatButtonModule,
    MatIconModule,
    CdkTableModule,
    TranslateModule
  ],
  templateUrl: './table.component.html',
  styleUrl: './table.component.scss',
})
export class TableComponent {
  columnTypeEnum = ColumnTypeEnum;
  ActionDisplayMode = ActionDisplayMode;
  @Input() columns: ITableColumn[] | undefined;
  @Input() displayedColumns: string[] | undefined;
  @Input() sortingType: DataHandlingType | undefined;
  @Input() paginationType: DataHandlingType | undefined;
  @Input() totalItems = 0;
  @Input() pageSize = 10;

  @Input() selection = new SelectionModel<any>(true, []);
  @Input() dataSource: MatTableDataSource<any> | undefined;

  @Output() onClickAction = new EventEmitter<TableActionEvent>();
  @Output() switchToggleEvent = new EventEmitter<SwitchToggleEvent>();
  @Output() textLinkClick = new EventEmitter<TextLinkClickEvent>();
  @Output() toggleAllRows = new EventEmitter<void>();
  @Output() toggleRow = new EventEmitter<any>();

  @Output() sortChanged = new EventEmitter<{
    active: string;
    direction: string;
  }>();
  @Output() pageChange = new EventEmitter<PageEvent>();

  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @Input() customColumnTemplate!: TemplateRef<any>;
  DataHandlingType = DataHandlingType;

  constructor(private translateService:TranslateService){}

  ngOnInit(): void {}

  ngAfterViewInit(): void {
    this.initializeSorting();
    this.checkPaginationType();
    this.checkSortingType();
  }
  checkSortingType(): void {
    if (this.dataSource)
      if (this.sortingType === DataHandlingType.Frontend) {
        this.dataSource.sort = this.sort; // Apply MatSort for frontend sorting
      } else {
        this.dataSource.sort = null; // Detach MatSort for backend sorting
      }
  }

  initializeSorting(): void {
    if (this.sortingType === DataHandlingType.Backend) {
      this.sort.sortChange.subscribe(({ active, direction }) => {
        this.sortChanged.emit({ active, direction }); // Emit backend sorting parameters
      });
    }
  }
  checkPaginationType(): void {
    if (this.dataSource)
      if (this.paginationType === DataHandlingType.Frontend) {
        this.dataSource.paginator = this.paginator; // Attach MatPaginator for frontend pagination
      } else {
        this.dataSource.paginator = null; // Detach paginator for backend pagination
      }
  }

  onPageChange(event: PageEvent) {
    this.pageSize = event.pageSize;
    if (this.paginationType === DataHandlingType.Backend) {
      this.pageChange.emit(event);
    }
  }

  isAllSelected(): boolean {
    if (!this.dataSource) return false;
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  checkboxLabel(row?: any): string {
    if (!row) {
      return this.isAllSelected() ? 'deselect all' : 'select all';
    }
    return this.selection.isSelected(row)
      ? `deselect row ${row.position + 1}`
      : `select row ${row.position + 1}`;
  }

  onToggleAllRows(): void {
    this.toggleAllRows.emit();
  }

  onToggleRow(row: any): void {
    this.toggleRow.emit(row);
  }

  handleAction(action: string, row: any): void {
    this.onClickAction.emit({ action, row });
  }

  onToggle(row: any, newValue: boolean): void {
    this.switchToggleEvent.emit({ row, newValue });
  }

  onTextLinkClick(row: any, columnDef: string): void {
    this.textLinkClick.emit({ row, columnDef });
  }
}
