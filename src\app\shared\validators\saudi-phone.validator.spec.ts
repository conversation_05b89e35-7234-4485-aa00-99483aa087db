import { FormControl } from '@angular/forms';
import { 
  saudiPhoneValidator, 
  formatSaudiPhoneNumber, 
  validateAndFormatSaudiPhone,
  getSaudiNetworkProvider 
} from './saudi-phone.validator';

describe('Saudi Phone Validator', () => {
  
  describe('saudiPhoneValidator', () => {
    const validator = saudiPhoneValidator();

    it('should return null for valid Saudi phone numbers', () => {
      const validNumbers = ['501234567', '551234567', '561234567', '581234567', '591234567'];
      
      validNumbers.forEach(number => {
        const control = new FormControl(number);
        const result = validator(control);
        expect(result).toBeNull();
      });
    });

    it('should return null for empty values', () => {
      const control = new FormControl('');
      const result = validator(control);
      expect(result).toBeNull();
    });

    it('should return error for numbers not starting with 5', () => {
      const invalidNumbers = ['401234567', '301234567', '601234567'];
      
      invalidNumbers.forEach(number => {
        const control = new FormControl(number);
        const result = validator(control);
        expect(result).toEqual({
          saudiPhone: {
            message: 'VALIDATION.SAUDI_PHONE_PREFIX',
            actualPrefix: number.charAt(0),
            expectedPrefix: '5'
          }
        });
      });
    });

    it('should return error for incorrect length', () => {
      const shortNumber = '50123';
      const longNumber = '5012345678';
      
      let control = new FormControl(shortNumber);
      let result = validator(control);
      expect(result).toEqual({
        saudiPhone: {
          message: 'VALIDATION.SAUDI_PHONE_LENGTH',
          actualLength: 5,
          expectedLength: 9
        }
      });

      control = new FormControl(longNumber);
      result = validator(control);
      expect(result).toEqual({
        saudiPhone: {
          message: 'VALIDATION.SAUDI_PHONE_LENGTH',
          actualLength: 10,
          expectedLength: 9
        }
      });
    });

    it('should handle numbers with non-digit characters', () => {
      const control = new FormControl('501-234-567');
      const result = validator(control);
      expect(result).toBeNull(); // Should be valid after removing non-digits
    });
  });

  describe('formatSaudiPhoneNumber', () => {
    it('should format valid 9-digit numbers correctly', () => {
      const result = formatSaudiPhoneNumber('501234567');
      expect(result).toBe('+966 50 123 4567');
    });

    it('should return original string for invalid length', () => {
      const result = formatSaudiPhoneNumber('50123');
      expect(result).toBe('50123');
    });

    it('should return empty string for empty input', () => {
      const result = formatSaudiPhoneNumber('');
      expect(result).toBe('');
    });

    it('should handle numbers with non-digit characters', () => {
      const result = formatSaudiPhoneNumber('501-234-567');
      expect(result).toBe('+966 50 123 4567');
    });
  });

  describe('validateAndFormatSaudiPhone', () => {
    it('should return valid result for correct numbers', () => {
      const result = validateAndFormatSaudiPhone('501234567');
      expect(result).toEqual({
        isValid: true,
        formatted: '+966 50 123 4567'
      });
    });

    it('should return invalid result with errors for incorrect numbers', () => {
      const result = validateAndFormatSaudiPhone('40123');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Phone number must be exactly 9 digits, got 5');
      expect(result.errors).toContain('Phone number must start with 5');
    });

    it('should return invalid result for empty input', () => {
      const result = validateAndFormatSaudiPhone('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Phone number is required');
    });
  });

  describe('getSaudiNetworkProvider', () => {
    it('should identify STC numbers correctly', () => {
      const stcNumbers = ['501234567', '511234567', '521234567', '531234567', '541234567', '551234567'];
      stcNumbers.forEach(number => {
        expect(getSaudiNetworkProvider(number)).toBe('STC');
      });
    });

    it('should identify Mobily numbers correctly', () => {
      const mobilyNumbers = ['561234567', '571234567'];
      mobilyNumbers.forEach(number => {
        expect(getSaudiNetworkProvider(number)).toBe('Mobily');
      });
    });

    it('should identify Zain numbers correctly', () => {
      const zainNumbers = ['581234567', '591234567'];
      zainNumbers.forEach(number => {
        expect(getSaudiNetworkProvider(number)).toBe('Zain');
      });
    });

    it('should return Unknown for invalid numbers', () => {
      expect(getSaudiNetworkProvider('')).toBe('Unknown');
      expect(getSaudiNetworkProvider('123')).toBe('Unknown');
      expect(getSaudiNetworkProvider('401234567')).toBe('Unknown');
    });
  });
});
