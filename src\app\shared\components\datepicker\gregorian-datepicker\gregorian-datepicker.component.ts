import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Output, OnInit, Input } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  NgbDateStruct,
  NgbCalendar,
  NgbCalendarGregorian,
  NgbDatepickerModule,
  NgbModule,
} from '@ng-bootstrap/ng-bootstrap';

@Component({
  standalone: true,
  selector: 'arabdt-gregorian-datepicker',
  templateUrl: './gregorian-datepicker.component.html',
  styleUrls: ['./gregorian-datepicker.component.scss'],
  imports: [NgbDatepickerModule, NgbModule, FormsModule,ReactiveFormsModule,CommonModule],
  providers: [{ provide: NgbCalendar, useClass: NgbCalendarGregorian }],
})
export class GregorianDatepickerComponent {
  @Output() dateChange = new EventEmitter<NgbDateStruct>();
  @Output() onBlur = new EventEmitter();
  @Input() placeholder!: string;
  @Input() disabled: boolean | undefined;
  @Input() model: NgbDateStruct | undefined;
  @Input() minDate!: NgbDateStruct;
  @Input() maxDate!: NgbDateStruct;
  @Input() isInvalid: boolean | undefined;

  constructor() {}
  date: { year: number; month: number; } | undefined;

  onDateSelect() {
    this.dateChange.emit(this.model);
    // Hide the datepicker after selection
    if (this.model) {
      setTimeout(() => {
        // Use setTimeout to ensure the value is emitted before hiding
        const event = new Event('click', { bubbles: true });
        document.dispatchEvent(event);
      }, 100);
    }
  }
  
  onDateSelectBlur() {
    this.onBlur.emit();
  }
}
