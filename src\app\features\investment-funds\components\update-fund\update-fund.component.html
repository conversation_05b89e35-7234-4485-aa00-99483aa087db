<div class="create-fund-page">

  <app-breadcrumb (onClickEvent)="onBreadcrumbClicked($event)" [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium" divider=">"></app-breadcrumb>
  <div class="mt-3">

    <app-page-header [title]="title | translate">
    </app-page-header>
  </div>

  <div class="form-container mt-3">
    <app-form-builder [formControls]="formControls" [formGroup]="formGroup" [isFormSubmitted]="isValidationFire"
      (dateSelected)="dateSelected($event)" (formSubmit)="onSubmit($event)" (fileUploaded)="onFileUpload($event)"
      (removeSelection)="onRemoveSelectedItem($event)" (dropdownChanged)="dropdownChanged($event)"
      [customTemplate]="customTemplate">
      <ng-template #customTemplate let-element="element">

        <div class="status-badge" *ngIf="fund" [ngClass]="getStatusClass(fund.statusId)">
          <span class="dot"></span>
          {{fund.status }}
        </div>
      </ng-template>
    </app-form-builder>

    <div class="actions">
      <app-custom-button [btnName]="'COMMON.CANCEL' | translate" (click)="router.navigate(['/admin/investment-funds'])"
        [buttonType]="buttonEnum.Secondary" [iconName]="IconEnum.cancel">
      </app-custom-button>

      <app-custom-button [btnName]="'COMMON.SAVE' | translate" [buttonType]="buttonEnum.Primary"
        [iconName]="IconEnum.verify" (click)="onSubmit(formGroup.value)">
      </app-custom-button>
    </div>
  </div>
</div>