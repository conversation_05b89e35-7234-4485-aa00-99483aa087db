// Create User Component Styles - Using shared form container pattern
@import '../../../shared/styles/form-container';
@import "../../../../assets/scss/variables";

// Custom styling for switch component in form
::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
  background-color: $navy-blue !important;
}

::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
  background-color: rgba(0, 32, 90, 0.54) !important; // $navy-blue with opacity
}

::ng-deep .mat-slide-toggle .mat-slide-toggle-bar {
  background-color: rgba(0, 0, 0, 0.38) !important;
}

::ng-deep .mat-slide-toggle .mat-slide-toggle-thumb {
  background-color: #fafafa !important;
}

// Align form controls in same line for account settings
.form-container {
  .form-group {
    &.col-md-4 {
      display: flex;
      align-items: center;

      .field-switch,
      .field-checkbox {
        margin-bottom: 0;

        .mat-slide-toggle,
        .mat-checkbox {
          margin-top: 0;
        }
      }
    }
  }
}

// Component-specific styles can be added here if needed
// The form-container styles are imported from the shared file
