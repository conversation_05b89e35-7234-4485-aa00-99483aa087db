# Enhanced User Activation/Deactivation Error Handling Guide

## Overview

This guide documents the enhanced error handling mechanism for user activation and deactivation operations in the user-management component. The implementation extracts specific error messages from backend API responses and displays them using SweetAlert with proper multi-language support.

## Key Features

### 1. **Backend Error Message Extraction**
- Extracts specific error messages from various backend response formats
- Supports ApiException, ProblemDetails, BaseResponse, and simple message formats
- Falls back to contextual error messages when backend messages are unavailable

### 2. **Multi-language Support**
- Full Arabic and English support with proper RTL/LTR text direction
- Dynamic message interpolation with user names
- Contextual error titles and messages

### 3. **Status Code Specific Handling**
- **409 Conflict**: Warning icon for role conflicts and system constraints
- **404 Not Found**: Info icon for user not found scenarios
- **422 Validation**: Warning icon for validation errors
- **500+ Server Errors**: Toast notifications for server issues
- **Other 4xx**: Modal dialogs for client errors

## Implementation Details

### Enhanced UserManagementErrorHandlerService

The service provides a specialized method for handling activation/deactivation errors:

```typescript
handleActivationError(error: HttpErrorResponse, context: UserManagementErrorContext): void
```

#### Backend Error Extraction Process

1. **ApiException Handling**:
   ```typescript
   if (ApiException.isApiException(error.error)) {
     return this.extractFromApiException(error.error as ApiException);
   }
   ```

2. **ProblemDetails Extraction**:
   ```typescript
   if (this.isProblemDetails(errorResponse)) {
     const problemDetails = errorResponse as ProblemDetails;
     return problemDetails.detail || problemDetails.title || null;
   }
   ```

3. **BaseResponse Extraction**:
   ```typescript
   if (this.isBaseResponse(errorResponse)) {
     return errorResponse.message || null;
   }
   ```

4. **Simple Message Extraction**:
   ```typescript
   if (errorResponse.message) {
     return errorResponse.message;
   }
   ```

### Component Integration

The user-management component uses the enhanced error handler:

```typescript
this.userManagementService
  .activateUser(activateUserBody)
  .pipe(
    catchError((error) => {
      // Use enhanced error handler to extract and display backend error message
      this.userErrorHandler.handleActivationError(error, {
        operation: newStatus ? 'activate' : 'deactivate',
        userId: user.id,
        userName: user.fullName
      });
      
      return of(null);
    })
  )
  .subscribe(/* success handling */);
```

## Error Message Hierarchy

The system follows a specific hierarchy for error message selection:

1. **Backend Error Message**: Extracted from API response (highest priority)
2. **Specific Operation + Status Code**: `USER_MANAGEMENT.ERRORS.ACTIVATE_409`
3. **General Operation Error**: `USER_MANAGEMENT.ERRORS.ACTIVATE_FAILED`
4. **Fallback Error**: `USER_MANAGEMENT.ERRORS.STATUS_UPDATE_FAILED`

## Supported Error Formats

### 1. **ApiException with ProblemDetails**
```json
{
  "message": "Validation Error",
  "status": 422,
  "result": {
    "type": "https://api.jadwa.com/errors/validation",
    "title": "Validation Error",
    "detail": "User activation request contains invalid data.",
    "instance": "/api/users/activate"
  }
}
```

### 2. **Direct ProblemDetails**
```json
{
  "type": "https://api.jadwa.com/errors/user-activation-conflict",
  "title": "User Activation Conflict",
  "status": 409,
  "detail": "Cannot activate user due to role conflicts.",
  "instance": "/api/users/activate/123"
}
```

### 3. **BaseResponse Format**
```json
{
  "statusCode": 400,
  "successed": false,
  "message": "User activation failed due to incomplete profile information.",
  "data": null,
  "errors": []
}
```

### 4. **Simple Message Format**
```json
{
  "message": "User activation failed: The user account is already active."
}
```

## Translation Keys

### Error Messages (English)
```json
{
  "USER_MANAGEMENT": {
    "ERRORS": {
      "ACTIVATE_FAILED": "Failed to activate user {{name}}. Please try again.",
      "DEACTIVATE_FAILED": "Failed to deactivate user {{name}}. Please try again.",
      "ACTIVATE_409": "Cannot activate user {{name}} due to role conflicts.",
      "DEACTIVATE_409": "Cannot deactivate user {{name}} due to system constraints.",
      "ACTIVATE_404": "User {{name}} not found. Cannot activate.",
      "DEACTIVATE_404": "User {{name}} not found. Cannot deactivate."
    },
    "ERROR_TITLES": {
      "ACTIVATE": "User Activation Error",
      "DEACTIVATE": "User Deactivation Error"
    },
    "SUCCESS": {
      "ACTIVATE_SUCCESS": "User {{name}} activated successfully.",
      "DEACTIVATE_SUCCESS": "User {{name}} deactivated successfully."
    }
  }
}
```

### Error Messages (Arabic)
```json
{
  "USER_MANAGEMENT": {
    "ERRORS": {
      "ACTIVATE_FAILED": "فشل في تفعيل المستخدم {{name}}. يرجى المحاولة مرة أخرى.",
      "DEACTIVATE_FAILED": "فشل في إلغاء تفعيل المستخدم {{name}}. يرجى المحاولة مرة أخرى.",
      "ACTIVATE_409": "لا يمكن تفعيل المستخدم {{name}} بسبب تعارض في الأدوار.",
      "DEACTIVATE_409": "لا يمكن إلغاء تفعيل المستخدم {{name}} بسبب قيود النظام.",
      "ACTIVATE_404": "المستخدم {{name}} غير موجود. لا يمكن التفعيل.",
      "DEACTIVATE_404": "المستخدم {{name}} غير موجود. لا يمكن إلغاء التفعيل."
    },
    "ERROR_TITLES": {
      "ACTIVATE": "خطأ في تفعيل المستخدم",
      "DEACTIVATE": "خطأ في إلغاء تفعيل المستخدم"
    },
    "SUCCESS": {
      "ACTIVATE_SUCCESS": "تم تفعيل المستخدم {{name}} بنجاح.",
      "DEACTIVATE_SUCCESS": "تم إلغاء تفعيل المستخدم {{name}} بنجاح."
    }
  }
}
```

## Error Display Examples

### Backend Error Message (Priority 1)
**Backend Response**: "Cannot activate user due to role conflicts with existing active users."
**Display**: Shows the exact backend message with proper language direction

### Contextual Error Message (Priority 2)
**No Backend Message + 409 Status**: Uses `ACTIVATE_409` translation
**English**: "Cannot activate user أحمد محمد due to role conflicts."
**Arabic**: "لا يمكن تفعيل المستخدم أحمد محمد بسبب تعارض في الأدوار."

### Fallback Error Message (Priority 3)
**No Backend Message + Unknown Status**: Uses `ACTIVATE_FAILED` translation
**English**: "Failed to activate user أحمد محمد. Please try again."
**Arabic**: "فشل في تفعيل المستخدم أحمد محمد. يرجى المحاولة مرة أخرى."

## Testing

Use the provided test component to verify functionality:

```typescript
import { UserActivationErrorTestComponent } from './components/user-activation-error-test.component';
```

The test component provides comprehensive testing scenarios for:
- Backend error message extraction
- Different response format handling
- Status code specific behavior
- Language switching
- Success message display

## Benefits

1. **User-Friendly**: Shows specific backend error messages instead of generic ones
2. **Contextual**: Falls back to operation-specific messages when needed
3. **Multi-language**: Full Arabic/English support with proper text direction
4. **Consistent**: Standardized error handling across all activation/deactivation operations
5. **Informative**: Clear indication of which user the operation failed for
6. **Accessible**: Proper RTL/LTR support and appropriate icons for different error types

## Best Practices

1. **Always Provide Context**: Include user ID and name in error context
2. **Handle All Response Formats**: Support ApiException, ProblemDetails, BaseResponse, and simple messages
3. **Test Both Languages**: Verify error messages work correctly in Arabic and English
4. **Use Appropriate Icons**: Warning for conflicts, info for not found, error for others
5. **Provide Fallbacks**: Always have contextual error messages as fallbacks
6. **Log for Debugging**: Console log errors while showing user-friendly messages

This enhanced error handling provides a superior user experience by showing specific, contextual error messages that help users understand exactly what went wrong during user activation and deactivation operations.
