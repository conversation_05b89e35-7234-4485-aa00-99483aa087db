import { Component, Input, Inject, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { CustomButtonComponent } from "../../../../shared/components/custom-button/custom-button.component";
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { MatDialogRef} from '@angular/material/dialog';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
@Component({
  selector: 'app-conflicts-popup',
  standalone: true,
  imports: [TranslateModule, CommonModule, CustomButtonComponent],
  templateUrl: './conflicts-popup.component.html',
  styleUrl: './conflicts-popup.component.scss'
})
export class ConflictsPopupComponent implements OnInit {

constructor(private translateService: TranslateService,
  public dialogRef: MatDialogRef<ConflictsPopupComponent>,
  @Inject(MAT_DIALOG_DATA) public names: any
  ){}
  createButtonIcon = IconEnum;
  ButtonTypeEnum = ButtonTypeEnum;


  ngOnInit() {
    console.log(this.names);

  }



  removeName(name: string) {
    const index = this.names.indexOf(name);
    if (index !== -1) {
      this.names.splice(index, 1);
    }
  }

  back(){
    this.dialogRef.close();

  }
}
