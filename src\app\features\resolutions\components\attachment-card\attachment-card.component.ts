import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { environment } from '../../../../../environments/environment.prod';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-attachment-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './attachment-card.component.html',
  styleUrl: './attachment-card.component.scss'
})
export class AttachmentCardComponent {
  private toastr = inject(ToastrService);
  private translate = inject(TranslateService);

  @Input() attachment: any; // can be object or array

  get attachments(): any[] {
    if (Array.isArray(this.attachment)) {
      return this.attachment;
    } else if (this.attachment && typeof this.attachment === 'object') {
      return [this.attachment];
    }
    return [];
  }

  /**
   * Format file size in bytes to human readable format
   */
  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Check if the file type is supported for download
   */
  private isSupportedFileType(fileName: string): boolean {
    if (!fileName) return false;

    const supportedExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.png', '.jpg', '.jpeg'];
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));

    return supportedExtensions.includes(extension);
  }



  downloadFile(attachment: any): void {
    if (!attachment) {
      this.toastr.error(
        this.translate.instant('COMMON.NO_ATTACHMENT_PROVIDED') || 'No attachment provided',
        this.translate.instant('COMMON.ERROR') || 'Error'
      );
      return;
    }

    // Check if we have a file path
    if (!attachment.filePath) {
      this.toastr.error(
        this.translate.instant('COMMON.NO_FILE_PATH') || 'No file path provided',
        this.translate.instant('COMMON.ERROR') || 'Error'
      );
      return;
    }

    // Check if file type is supported (optional warning)
    if (!this.isSupportedFileType(attachment.fileName)) {
      this.toastr.warning(
        this.translate.instant('COMMON.UNSUPPORTED_FILE_TYPE') || 'File type may not be supported',
        this.translate.instant('COMMON.WARNING') || 'Warning'
      );
    }

    try {
      // Show loading toast
      this.toastr.info(
        this.translate.instant('COMMON.DOWNLOADING_FILE') || 'Downloading file...',
        this.translate.instant('COMMON.PLEASE_WAIT') || 'Please wait'
      );

      // Construct the full URL for the file
      const fullUrl = attachment.filePath;

      // Create a temporary link element
      const link = document.createElement('a');
      link.href = fullUrl;
      link.target = '_blank'; // Open in new tab as fallback
      link.download = attachment.fileName || 'download'; // Set download filename

      // Add to DOM temporarily to trigger download
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);

      // Show success message
      this.toastr.success(
        this.translate.instant('COMMON.DOWNLOAD_STARTED') || 'Download started successfully',
        this.translate.instant('COMMON.SUCCESS') || 'Success'
      );

      console.log('Download initiated for:', attachment.fileName);
    } catch (error: any) {
      console.error('Error downloading file:', error);

      this.toastr.error(
        this.translate.instant('COMMON.DOWNLOAD_FAILED') || 'Download failed',
        this.translate.instant('COMMON.ERROR') || 'Error'
      );

      // Fallback: try to open in new window
      try {
        const fullUrl = attachment.filePath;
        window.open(fullUrl, '_blank');

        this.toastr.info(
          this.translate.instant('COMMON.OPENED_IN_NEW_TAB') || 'File opened in new tab',
          this.translate.instant('COMMON.INFO') || 'Info'
        );
      } catch (fallbackError: any) {
        console.error('Fallback download also failed:', fallbackError);
        this.toastr.error(
          this.translate.instant('COMMON.UNABLE_TO_DOWNLOAD') || 'Unable to download or open file',
          this.translate.instant('COMMON.ERROR') || 'Error'
        );
      }
    }
  }

}
