import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

// Shared components
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// API and services
import {
  DocumentServiceProxy,
  MinIOFileServiceProxy,
  DocumentDto,
  MinIOPreviewCommand,
  ModuleEnum
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

export interface DocumentViewerData {
  document: DocumentDto;
  bucketName: string;
}

@Component({
  selector: 'app-document-viewer',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    CustomButtonComponent
  ],
  templateUrl: './document-viewer.component.html',
  styleUrls: ['./document-viewer.component.scss']
})
export class DocumentViewerComponent implements OnInit {
  isLoading = true;
  previewUrl: SafeResourceUrl | null = null;
  errorMessage = '';
  
  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  constructor(
    private dialogRef: MatDialogRef<DocumentViewerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DocumentViewerData,
    private documentProxy: DocumentServiceProxy,
    private minioProxy: MinIOFileServiceProxy,
    private errorModalService: ErrorModalService,
    private translateService: TranslateService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.loadDocumentPreview();
  }

  loadDocumentPreview(): void {
    if (!this.data.document || !this.data.bucketName) {
      this.errorMessage = 'DOCUMENTS.PREVIEW_ERROR';
      this.isLoading = false;
      return;
    }

    // Use document's previewUrl if available, otherwise use MinIO preview
    if (this.data.document.previewUrl) {
      this.previewUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.data.document.previewUrl);
      this.isLoading = false;
    } else {
      // Use NSwag-generated MinIO proxy for preview
      const previewCommand = new MinIOPreviewCommand({
        id: this.data.document.id,
        bucketName: this.data.bucketName,
        expiryInMinutes: 60
      });

      this.minioProxy.preview(previewCommand).subscribe({
        next: (response: any) => {
          // The response should contain the preview URL
          if (response) {
            this.previewUrl = this.sanitizer.bypassSecurityTrustResourceUrl(response);
          } else {
            this.errorMessage = 'DOCUMENTS.PREVIEW_FAILED';
          }
          this.isLoading = false;
        },
        error: (error: any) => {
          console.error('Preview error:', error);
          this.errorMessage = 'DOCUMENTS.PREVIEW_FAILED';
          this.errorModalService.showError(this.translateService.instant('DOCUMENTS.ERRORS.PREVIEW_FAILED'));
          this.isLoading = false;
        }
      });
    }
  }

  onDownload(): void {
    // Use document's downloadUrl if available, otherwise use MinIO download
    if (this.data.document.downloadUrl) {
      // Direct download using the provided URL
      const link = document.createElement('a');
      link.href = this.data.document.downloadUrl;
      link.download = this.data.document.name || 'document';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      // Use NSwag-generated MinIO proxy for download
      this.minioProxy.downloadFile(this.data.document.id, this.data.bucketName).subscribe({
        next: (response: any) => {
          // Handle the download response
          if (response) {
            const link = document.createElement('a');
            link.href = response;
            link.download = this.data.document.name || 'document';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        },
        error: (error: any) => {
          console.error('Download error:', error);
          this.errorModalService.showError(this.translateService.instant('DOCUMENTS.ERRORS.DOWNLOAD_FAILED'));
        }
      });
    }
  }

  onClose(): void {
    this.dialogRef.close();
  }

  getFileExtension(): string {
    if (!this.data.document?.name) return '';
    return this.data.document.name.split('.').pop()?.toLowerCase() || '';
  }

  isPreviewSupported(): boolean {
    const extension = this.getFileExtension();
    return ['pdf', 'jpg', 'jpeg', 'png', 'gif'].includes(extension);
  }

  getFileIcon(): string {
    const extension = this.getFileExtension();

    switch (extension) {
      case 'pdf':
        return 'picture_as_pdf';
      case 'doc':
      case 'docx':
        return 'description';
      case 'xls':
      case 'xlsx':
        return 'table_chart';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'image';
      default:
        return 'insert_drive_file';
    }
  }

  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
