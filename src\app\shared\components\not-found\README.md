# NotFoundComponent (404 Page)

A standalone Angular component that displays a user-friendly 404 "Page Not Found" error page with navigation options.

## Features

- **Responsive Design**: Works on all screen sizes (desktop, tablet, mobile)
- **Internationalization**: Supports both Arabic and English languages
- **Navigation Options**: 
  - Back button (uses browser history)
  - Home button (navigates to dashboard)
- **Accessible**: Proper semantic HTML and ARIA labels
- **Animated**: Smooth fade-in animation and subtle pulse effect

## Usage

The component is automatically loaded when users navigate to non-existent routes. It's configured in the main app routing:

```typescript
// app.routes.ts
{
  path: 'not-found',
  loadComponent: () => import('./shared/components/not-found/not-found.component').then(m => m.NotFoundComponent)
},
{ path: '**', redirectTo: 'not-found' }
```

## Manual Navigation

You can also programmatically navigate to the 404 page:

```typescript
// In any component
constructor(private router: Router) {}

navigateTo404() {
  this.router.navigate(['/not-found']);
}
```

## Customization

### Styling
Modify `not-found.component.scss` to change colors, fonts, or layout.

### Content
Update translation files to change text content:
- `src/assets/i18n/ar.json` (Arabic)
- `src/assets/i18n/en.json` (English)

### Navigation Behavior
Modify the `goBack()` and `goHome()` methods in the component to change navigation behavior.

## Translation Keys

```json
{
  "NOT_FOUND": {
    "TITLE": "Page Not Found",
    "DESCRIPTION": "Sorry, the page you are looking for does not exist or has been moved to another location.",
    "GO_BACK": "Go Back",
    "GO_HOME": "Go Home",
    "HELP_TEXT": "If you believe this is an error, please contact our technical support team."
  }
}
```

## Dependencies

- `@angular/common` (Location service)
- `@angular/router` (Router service)
- `@ngx-translate/core` (Internationalization)
- `CustomButtonComponent` (Custom button styling)

## Browser Support

Works in all modern browsers that support Angular 17+.
