import { NgModule } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { InjectionToken } from '@angular/core';

export const API_BASE_URL = new InjectionToken<string>('API_BASE_URL');

@NgModule({
  imports: [HttpClientModule],
  providers: [
    { provide: API_BASE_URL, useValue: environment.apiUrl }
  ]
})
export class ApiModule { } 