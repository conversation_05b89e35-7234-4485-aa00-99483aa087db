# 🛠️ JDWA-511: Implementation Guide - Create Resolution Feature

## 📋 Overview

This guide provides step-by-step instructions for implementing the "Create a Resolution - Fund Manager" feature following the established JadwaUI architectural patterns.

## ✅ Final Implementation Status

**COMPLETED:** The resolution feature has been successfully implemented following the correct architectural patterns:

### 🏗️ Architecture Decisions Applied:
- ✅ **Direct API Integration**: Uses `ResolutionsServiceProxy` directly (no custom service layer)
- ✅ **Generated Models**: Uses models from `@core/api/api.generated.ts` (no custom models)
- ✅ **Standalone Create Page**: Create operation is a full page, not a dialog
- ✅ **Dynamic Forms**: Uses `app-form-builder` component for form implementation
- ✅ **Fund Details Integration**: Added navigation card in fund details page
- ✅ **Read-only List**: Main resolutions page only displays data (no create button)

### 🔧 Updated Development Patterns (Sprint 2):
- ✅ **Enhanced Form Validation**: Comprehensive validation with custom validators
- ✅ **Fixed Duplicate Submission**: Removed conflicting form submission events
- ✅ **Improved File Upload**: Enhanced validation and localization support
- ✅ **Submit Button Behavior**: Always enabled with validation on click
- ✅ **Localization**: Complete translation support for all form elements

### 📁 Final File Structure:
```
src/app/features/resolutions/
├── components/
│   └── create-resolution/
│       ├── create-resolution.component.ts
│       ├── create-resolution.component.html
│       └── create-resolution.component.scss
├── resolutions.component.ts          # Read-only list view
├── resolutions.component.html
├── resolutions.component.scss
└── resolutions.routes.ts
```

## 🚀 Getting Started

### Prerequisites
- Angular CLI installed and configured
- Project dependencies installed (`npm install`)
- Understanding of the strategy module implementation pattern
- Access to the backend API documentation

### Development Environment Setup
```bash
# Ensure you're in the project root
cd c:\Work\Projects\JadwaUI

# Install dependencies if not already done
npm install

# Start development server
ng serve

# In a separate terminal, start the backend API (if running locally)
# Backend should be available at https://localhost:7010
```

## 📁 Step 1: Create Module Structure

### 1.1 Create Directory Structure
```bash
# Navigate to features directory
cd src/app/features

# Create resolution module structure
mkdir resolutions
cd resolutions
mkdir components services models

# Create component files
touch resolutions.component.ts
touch resolutions.component.html
touch resolutions.component.scss
touch resolutions.routes.ts

# Create dialog component
cd components
touch resolution-dialog.component.ts
touch resolution-dialog.component.html
touch resolution-dialog.component.scss

# Create service and models
cd ../services
touch resolution.service.ts
cd ../models
touch resolution.models.ts
```

### 1.2 Create Data Models
Create `models/resolution.models.ts`:

```typescript
// Resolution Data Transfer Objects
export interface ResolutionDto {
  id: number;
  code: string;
  fundId: number;
  fundName: string;
  resolutionDate: string;
  description?: string;
  typeId: number;
  typeName: string;
  customTypeName?: string;
  attachmentId: number;
  attachmentName: string;
  votingMethodologyId: number;
  votingResultCalculationId: number;
  statusId: number;
  statusName: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  createdByRole: string;
}

export interface CreateResolutionCommand {
  fundId: number;
  resolutionDate: string;
  description?: string;
  typeId: number;
  customTypeName?: string;
  attachmentId: number;
  votingMethodologyId: number;
  votingResultCalculationId: number;
  saveAsDraft: boolean;
}

export interface ResolutionType {
  id: number;
  nameAr: string;
  nameEn: string;
  isActive: boolean;
}

export interface VotingMethodology {
  id: number;
  nameAr: string;
  nameEn: string;
  isDefault: boolean;
}

// API Response Models
export interface ResolutionListResponse {
  statusCode: number;
  successed: boolean;
  message: string;
  data: ResolutionDto[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  errors: string[];
}

export interface ResolutionCreateResponse {
  statusCode: number;
  successed: boolean;
  message: string;
  data: {
    id: number;
    code: string;
    statusId: number;
  };
  errors: string[];
}
```

## 🔧 Step 2: Implement Service Layer

### 2.1 Create Resolution Service
Create `services/resolution.service.ts`:

```typescript
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@environments/environment';
import { 
  ResolutionDto, 
  CreateResolutionCommand, 
  ResolutionListResponse,
  ResolutionCreateResponse,
  ResolutionType,
  VotingMethodology 
} from '../models/resolution.models';

@Injectable({
  providedIn: 'root'
})
export class ResolutionService {
  private baseUrl = `${environment.apiUrl}/api/Resolutions`;

  constructor(private http: HttpClient) {}

  createResolution(command: CreateResolutionCommand): Observable<ResolutionCreateResponse> {
    return this.http.post<ResolutionCreateResponse>(`${this.baseUrl}/Create`, command);
  }

  updateResolution(id: number, command: CreateResolutionCommand): Observable<any> {
    return this.http.put(`${this.baseUrl}/Update/${id}`, command);
  }

  getResolutionById(id: number): Observable<ResolutionDto> {
    return this.http.get<ResolutionDto>(`${this.baseUrl}/GetById/${id}`);
  }

  getResolutionsByFund(fundId: number, params: {
    pageNo?: number;
    pageSize?: number;
    search?: string;
    statusId?: number;
    orderBy?: string;
  }): Observable<ResolutionListResponse> {
    let httpParams = new HttpParams();
    httpParams = httpParams.set('fundId', fundId.toString());
    
    Object.keys(params).forEach(key => {
      const value = params[key as keyof typeof params];
      if (value !== null && value !== undefined && value !== '') {
        httpParams = httpParams.set(key, value.toString());
      }
    });

    return this.http.get<ResolutionListResponse>(`${this.baseUrl}/GetByFund`, { params: httpParams });
  }

  cancelResolution(id: number): Observable<any> {
    return this.http.put(`${this.baseUrl}/Cancel/${id}`, {});
  }

  // Lookup data methods
  getResolutionTypes(): Observable<ResolutionType[]> {
    return this.http.get<ResolutionType[]>(`${this.baseUrl}/GetTypes`);
  }

  getVotingMethodologies(): Observable<VotingMethodology[]> {
    return this.http.get<VotingMethodology[]>(`${this.baseUrl}/GetVotingMethodologies`);
  }
}
```

## 🎨 Step 3: Create Main Component

### 3.1 Implement Resolutions Component
Create `resolutions.component.ts`:

```typescript
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';

// Shared components
import { TableComponent } from '@shared/components/table/table.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';

// Core interfaces and enums
import { ITableColumn, TableActionEvent } from '@core/gl-interfaces/I-table/i-table';
import { ColumnTypeEnum } from '@core/enums/column-type';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';

// Feature-specific imports
import { ResolutionService } from './services/resolution.service';
import { ResolutionDialogComponent } from './components/resolution-dialog.component';
import { ResolutionDto, ResolutionListResponse } from './models/resolution.models';
import { TokenService } from '@features/auth/services/token.service';

@Component({
  selector: 'app-resolutions',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    PageHeaderComponent,
    BreadcrumbComponent,
    TranslateModule
  ],
  templateUrl: './resolutions.component.html',
  styleUrl: './resolutions.component.scss'
})
export class ResolutionsComponent implements OnInit {
  // Data properties
  ELEMENT_DATA: ResolutionDto[] = [];
  tableDataSource = new MatTableDataSource<ResolutionDto>();
  totalCount = 0;
  currentFundId = 0;
  
  // UI state
  isDialogOpen = false;
  breadcrumbSizeEnum = SizeEnum;
  
  // Table configuration
  displayedColumns: string[] = ['code', 'resolutionDate', 'typeName', 'statusName', 'actions'];
  columns: ITableColumn[] = [
    {
      columnDef: 'code',
      header: 'RESOLUTIONS.CODE',
      columnType: ColumnTypeEnum.Text,
      cell: (element: ResolutionDto) => element.code
    },
    {
      columnDef: 'resolutionDate',
      header: 'RESOLUTIONS.DATE',
      columnType: ColumnTypeEnum.Date,
      cell: (element: ResolutionDto) => element.resolutionDate
    },
    {
      columnDef: 'typeName',
      header: 'RESOLUTIONS.TYPE',
      columnType: ColumnTypeEnum.Text,
      cell: (element: ResolutionDto) => element.typeName
    },
    {
      columnDef: 'statusName',
      header: 'RESOLUTIONS.STATUS',
      columnType: ColumnTypeEnum.Status,
      cell: (element: ResolutionDto) => element.statusName
    },
    {
      columnDef: 'actions',
      header: 'RESOLUTIONS.ACTIONS',
      columnType: ColumnTypeEnum.Actions,
      cell: () => ''
    }
  ];
  
  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', routerLink: '/admin/dashboard' },
    { label: 'BREADCRUMB.FUNDS', routerLink: '/admin/investment-funds' },
    { label: 'RESOLUTIONS.TITLE', routerLink: '' }
  ];

  constructor(
    private resolutionService: ResolutionService,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private translateService: TranslateService,
    public tokenService: TokenService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.currentFundId = +params['fundId'];
      this.loadResolutions();
    });
  }

  loadResolutions(): void {
    this.resolutionService.getResolutionsByFund(this.currentFundId, {
      pageNo: 0,
      pageSize: 0,
      orderBy: 'CreatedAt desc'
    }).subscribe((response: ResolutionListResponse) => {
      this.ELEMENT_DATA = response.data;
      this.tableDataSource.data = response.data;
      this.totalCount = response.totalCount;
    });
  }

  onClickAction(actionData: TableActionEvent): void {
    this.edit(actionData.row);
  }

  edit(row: ResolutionDto): void {
    this.resolutionService.getResolutionById(row.id).subscribe((resolution: ResolutionDto) => {
      const dialogRef = this.dialog.open(ResolutionDialogComponent, {
        width: '800px',
        data: {
          isEdit: true,
          resolution: resolution,
          fundId: this.currentFundId,
          existingResolutions: this.tableDataSource
        }
      });

      dialogRef.afterClosed().subscribe((result: any) => {
        if (result) {
          this.resolutionService.updateResolution(row.id, result).subscribe((response: any) => {
            if (response.successed) {
              this.showSuccessMessage('RESOLUTIONS.UPDATED_SUCCESSFULLY');
              this.loadResolutions();
            }
          });
        }
      });
    });
  }

  onCreate(): void {
    if (this.isDialogOpen) return;
    this.isDialogOpen = true;

    const dialogRef = this.dialog.open(ResolutionDialogComponent, {
      width: '800px',
      data: { 
        isEdit: false, 
        fundId: this.currentFundId,
        existingResolutions: this.tableDataSource 
      }
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      this.isDialogOpen = false;
      if (result) {
        this.resolutionService.createResolution(result).subscribe((response: any) => {
          if (response.successed) {
            this.showSuccessMessage('RESOLUTIONS.CREATED_SUCCESSFULLY');
            this.loadResolutions();
          }
        });
      }
    });
  }

  private showSuccessMessage(messageKey: string): void {
    Swal.fire({
      icon: 'success',
      title: this.translateService.instant(messageKey),
      showConfirmButton: false,
      timer: 1500
    });
  }
}
```

### 3.2 Create Component Template
Create `resolutions.component.html`:

```html
<div class="resolutions">
  <app-breadcrumb 
    [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Medium" 
    divider=">">
  </app-breadcrumb>
  
  <div class="mb-5">
    <app-page-header
      title="RESOLUTIONS.TITLE"
      [showCreateButton]="tokenService.hasPermission('Resolution.Create')"
      createButtonText="RESOLUTIONS.CREATE_NEW"
      (create)="onCreate()">
    </app-page-header>
  </div>

  <div class="table-container">
    <app-table
      [columns]="columns"
      [displayedColumns]="displayedColumns"
      [dataSource]="tableDataSource"
      [totalItems]="totalCount"
      (onClickAction)="onClickAction($event)">
    </app-table>
  </div>
</div>
```

### 3.3 Add Component Styles
Create `resolutions.component.scss`:

```scss
@import "../../../../assets/scss/variables";

.resolutions {
  padding: 24px;

  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .mb-5 {
    margin-bottom: 2rem;
  }
}
```

## 🎭 Step 4: Create Dialog Component

### 4.1 Implement Resolution Dialog Component
Create `components/resolution-dialog.component.ts`:

```typescript
import { Component, Inject, ViewChild, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgModel } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

// Shared components
import { FileUploadComponent } from '@shared/components/file-upload/file-upload.component';

// Feature imports
import { ResolutionService } from '../services/resolution.service';
import { CreateResolutionCommand, ResolutionType, VotingMethodology } from '../models/resolution.models';

@Component({
  selector: 'app-resolution-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatDatepickerModule,
    MatRadioModule,
    TranslateModule,
    FileUploadComponent
  ],
  templateUrl: './resolution-dialog.component.html',
  styleUrl: './resolution-dialog.component.scss'
})
export class ResolutionDialogComponent implements OnInit {
  @ViewChild('resolutionDateRef') resolutionDateRef!: NgModel;
  @ViewChild('typeRef') typeRef!: NgModel;
  @ViewChild('customTypeRef') customTypeRef!: NgModel;
  @ViewChild('fileRef') fileRef!: NgModel;

  formData: CreateResolutionCommand = {
    fundId: 0,
    resolutionDate: '',
    description: '',
    typeId: 0,
    customTypeName: '',
    attachmentId: 0,
    votingMethodologyId: 1,
    votingResultCalculationId: 1,
    saveAsDraft: false
  };

  // Lookup data
  resolutionTypes: ResolutionType[] = [];
  votingMethodologies: VotingMethodology[] = [];

  // UI state
  showCustomType = false;
  uploadedFile: { attachmentId: number; fileName: string } | null = null;

  // Validation
  minDate: Date;
  maxDate: Date = new Date();

  constructor(
    public dialogRef: MatDialogRef<ResolutionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private resolutionService: ResolutionService,
    private translateService: TranslateService
  ) {
    // Set minimum date to fund initiation date (from data or default)
    this.minDate = this.data.fundInitiationDate ? new Date(this.data.fundInitiationDate) : new Date('2020-01-01');
  }

  ngOnInit(): void {
    this.formData.fundId = this.data.fundId;
    this.loadLookupData();

    if (this.data.isEdit && this.data.resolution) {
      this.populateFormData();
    }
  }

  private loadLookupData(): void {
    this.resolutionService.getResolutionTypes().subscribe(types => {
      this.resolutionTypes = types;
    });

    this.resolutionService.getVotingMethodologies().subscribe(methodologies => {
      this.votingMethodologies = methodologies;
    });
  }

  private populateFormData(): void {
    const resolution = this.data.resolution;
    this.formData = {
      fundId: resolution.fundId,
      resolutionDate: resolution.resolutionDate,
      description: resolution.description,
      typeId: resolution.typeId,
      customTypeName: resolution.customTypeName,
      attachmentId: resolution.attachmentId,
      votingMethodologyId: resolution.votingMethodologyId,
      votingResultCalculationId: resolution.votingResultCalculationId,
      saveAsDraft: false
    };

    this.showCustomType = resolution.customTypeName ? true : false;
    this.uploadedFile = {
      attachmentId: resolution.attachmentId,
      fileName: resolution.attachmentName
    };
  }

  onTypeChange(): void {
    const selectedType = this.resolutionTypes.find(t => t.id === this.formData.typeId);
    this.showCustomType = selectedType?.nameEn === 'Other' || selectedType?.nameAr === 'أخرى';

    if (!this.showCustomType) {
      this.formData.customTypeName = '';
    }
  }

  onFileUploaded(fileData: { attachmentId: number; fileName: string }): void {
    this.uploadedFile = fileData;
    this.formData.attachmentId = fileData.attachmentId;
  }

  onSubmit(saveAsDraft: boolean = false): void {
    this.formData.saveAsDraft = saveAsDraft;

    if (!this.validateForm()) {
      return;
    }

    this.dialogRef.close(this.formData);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  private validateForm(): boolean {
    let isValid = true;

    // Required field validation
    if (!this.formData.resolutionDate) {
      this.resolutionDateRef?.control.setErrors({ required: true });
      this.resolutionDateRef?.control.markAsTouched();
      isValid = false;
    }

    if (!this.formData.typeId) {
      this.typeRef?.control.setErrors({ required: true });
      this.typeRef?.control.markAsTouched();
      isValid = false;
    }

    if (this.showCustomType && !this.formData.customTypeName?.trim()) {
      this.customTypeRef?.control.setErrors({ required: true });
      this.customTypeRef?.control.markAsTouched();
      isValid = false;
    }

    if (!this.formData.attachmentId) {
      this.fileRef?.control.setErrors({ required: true });
      this.fileRef?.control.markAsTouched();
      isValid = false;
    }

    return isValid;
  }

  isFormValid(): boolean {
    return !!(
      this.formData.resolutionDate &&
      this.formData.typeId &&
      (!this.showCustomType || this.formData.customTypeName?.trim()) &&
      this.formData.attachmentId
    );
  }

  hasChanges(): boolean {
    if (!this.data.isEdit) return true;

    const original = this.data.resolution;
    return (
      this.formData.resolutionDate !== original.resolutionDate ||
      this.formData.description !== original.description ||
      this.formData.typeId !== original.typeId ||
      this.formData.customTypeName !== original.customTypeName ||
      this.formData.attachmentId !== original.attachmentId ||
      this.formData.votingMethodologyId !== original.votingMethodologyId ||
      this.formData.votingResultCalculationId !== original.votingResultCalculationId
    );
  }
}
```

### 4.2 Create Dialog Template
Create `components/resolution-dialog.component.html`:

```html
<div class="resolution-dialog">
  <h2 mat-dialog-title>
    {{ data.isEdit ? ('RESOLUTIONS.EDIT_TITLE' | translate) : ('RESOLUTIONS.CREATE_TITLE' | translate) }}
  </h2>

  <mat-dialog-content>
    <div class="form-container">
      <!-- Resolution Date -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>{{ 'RESOLUTIONS.DATE' | translate }}</mat-label>
        <input matInput
               [matDatepicker]="picker"
               [(ngModel)]="formData.resolutionDate"
               #resolutionDateRef="ngModel"
               [min]="minDate"
               [max]="maxDate"
               required>
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        <mat-error *ngIf="resolutionDateRef.invalid && resolutionDateRef.touched">
          <div *ngIf="resolutionDateRef.errors?.['required']">
            {{ 'FORM.ERROR_REQUIRED' | translate }}
          </div>
        </mat-error>
      </mat-form-field>

      <!-- Description -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>{{ 'RESOLUTIONS.DESCRIPTION' | translate }}</mat-label>
        <textarea matInput
                  [(ngModel)]="formData.description"
                  [placeholder]="'RESOLUTIONS.DESCRIPTION_PLACEHOLDER' | translate"
                  rows="3"
                  maxlength="500">
        </textarea>
        <mat-hint>{{ formData.description?.length || 0 }}/500</mat-hint>
      </mat-form-field>

      <!-- Resolution Type -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>{{ 'RESOLUTIONS.TYPE' | translate }}</mat-label>
        <mat-select [(ngModel)]="formData.typeId"
                    #typeRef="ngModel"
                    (selectionChange)="onTypeChange()"
                    required>
          <mat-option *ngFor="let type of resolutionTypes" [value]="type.id">
            {{ type.nameAr }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="typeRef.invalid && typeRef.touched">
          <div *ngIf="typeRef.errors?.['required']">
            {{ 'FORM.ERROR_REQUIRED' | translate }}
          </div>
        </mat-error>
      </mat-form-field>

      <!-- Custom Type (shown when "Other" is selected) -->
      <mat-form-field appearance="outline" class="full-width" *ngIf="showCustomType">
        <mat-label>{{ 'RESOLUTIONS.CUSTOM_TYPE' | translate }}</mat-label>
        <input matInput
               [(ngModel)]="formData.customTypeName"
               #customTypeRef="ngModel"
               [placeholder]="'RESOLUTIONS.CUSTOM_TYPE_PLACEHOLDER' | translate"
               maxlength="100"
               required>
        <mat-error *ngIf="customTypeRef.invalid && customTypeRef.touched">
          <div *ngIf="customTypeRef.errors?.['required']">
            {{ 'FORM.ERROR_REQUIRED' | translate }}
          </div>
        </mat-error>
      </mat-form-field>

      <!-- File Upload -->
      <div class="file-upload-section">
        <label class="file-label">{{ 'RESOLUTIONS.FILE' | translate }} *</label>
        <app-file-upload
          [acceptedTypes]="['.pdf']"
          [maxSize]="10485760"
          [existingFile]="uploadedFile"
          (fileUploaded)="onFileUploaded($event)"
          #fileRef="ngModel">
        </app-file-upload>
        <div class="file-requirements">
          {{ 'RESOLUTIONS.FILE_REQUIREMENTS' | translate }}
        </div>
      </div>

      <!-- Voting Methodology -->
      <div class="voting-section">
        <label class="section-label">{{ 'RESOLUTIONS.VOTING_METHODOLOGY' | translate }}</label>
        <mat-radio-group [(ngModel)]="formData.votingMethodologyId" class="voting-options">
          <mat-radio-button *ngFor="let method of votingMethodologies"
                            [value]="method.id"
                            class="voting-option">
            {{ method.nameAr }}
          </mat-radio-button>
        </mat-radio-group>
      </div>

      <!-- Voting Result Calculation -->
      <div class="voting-section">
        <label class="section-label">{{ 'RESOLUTIONS.VOTING_RESULT' | translate }}</label>
        <mat-radio-group [(ngModel)]="formData.votingResultCalculationId" class="voting-options">
          <mat-radio-button [value]="1" class="voting-option">
            {{ 'RESOLUTIONS.ALL_ITEMS' | translate }}
          </mat-radio-button>
          <mat-radio-button [value]="2" class="voting-option">
            {{ 'RESOLUTIONS.MAJORITY_ITEMS' | translate }}
          </mat-radio-button>
        </mat-radio-group>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()">
      {{ 'COMMON.CANCEL' | translate }}
    </button>

    <!-- Save as Draft button (only for new resolutions or drafts) -->
    <button mat-button
            color="accent"
            (click)="onSubmit(true)"
            [disabled]="!isFormValid()"
            *ngIf="!data.isEdit || data.resolution?.statusId === 1">
      {{ 'RESOLUTIONS.SAVE_AS_DRAFT' | translate }}
    </button>

    <!-- Send button -->
    <button mat-raised-button
            color="primary"
            (click)="onSubmit(false)"
            [disabled]="!isFormValid() || !hasChanges()">
      {{ 'RESOLUTIONS.SEND' | translate }}
    </button>
  </mat-dialog-actions>
</div>
```

### 4.3 Add Dialog Styles
Create `components/resolution-dialog.component.scss`:

```scss
.resolution-dialog {
  padding: 24px;
  min-width: 600px;
  max-width: 800px;

  .form-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin: 20px 0;
  }

  .full-width {
    width: 100%;
  }

  .file-upload-section {
    .file-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #333;

      &::after {
        content: ' *';
        color: #f44336;
      }
    }

    .file-requirements {
      font-size: 12px;
      color: #666;
      margin-top: 8px;
    }
  }

  .voting-section {
    .section-label {
      display: block;
      margin-bottom: 12px;
      font-weight: 500;
      color: #333;
    }

    .voting-options {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .voting-option {
        margin-bottom: 8px;
      }
    }
  }

  mat-dialog-actions {
    padding: 16px 0;
    gap: 12px;
  }
}
```

## 🛣️ Step 5: Configure Routing

### 5.1 Create Feature Routes
Create `resolutions.routes.ts`:

```typescript
import { Routes } from '@angular/router';
import { AuthGuard } from '@core/guards/auth.guard';

export const RESOLUTION_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./resolutions.component').then(m => m.ResolutionsComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'details/:id',
    loadComponent: () =>
      import('./components/resolution-details.component')
        .then(m => m.ResolutionDetailsComponent),
    canActivate: [AuthGuard]
  }
];
```

### 5.2 Update Main App Routing
Add to `app.routes.ts`:

```typescript
// Add this route to the admin children array
{
  path: 'funds/:fundId/resolutions',
  loadChildren: () =>
    import('./features/resolutions/resolutions.routes')
      .then(m => m.RESOLUTION_ROUTES),
  canActivate: [AuthGuard]
}
```

## 🌐 Step 6: Add Internationalization

### 6.1 Add Translation Keys
Add to `src/assets/i18n/ar.json`:

```json
{
  "RESOLUTIONS": {
    "TITLE": "القرارات",
    "CREATE_NEW": "إضافة قرار جديد",
    "CREATE_TITLE": "إضافة قرار جديد",
    "EDIT_TITLE": "تعديل القرار",
    "CODE": "كود القرار",
    "DATE": "تاريخ القرار",
    "DESCRIPTION": "وصف القرار",
    "DESCRIPTION_PLACEHOLDER": "أدخل وصف القرار (اختياري)",
    "TYPE": "نوع القرار",
    "CUSTOM_TYPE": "نوع القرار المضاف",
    "CUSTOM_TYPE_PLACEHOLDER": "أدخل نوع القرار",
    "FILE": "ملف القرار",
    "FILE_REQUIREMENTS": "نوع الملف: PDF، الحد الأقصى: 10 ميجابايت",
    "VOTING_METHODOLOGY": "آلية التصويت للقرار",
    "VOTING_RESULT": "احتساب نتيجة التصويت للأعضاء",
    "ALL_ITEMS": "جميع البنود",
    "MAJORITY_ITEMS": "أغلبية البنود",
    "STATUS": "الحالة",
    "ACTIONS": "الإجراءات",
    "SAVE_AS_DRAFT": "حفظ كمسودة",
    "SEND": "إرسال",
    "CREATED_SUCCESSFULLY": "تم إنشاء القرار بنجاح",
    "UPDATED_SUCCESSFULLY": "تم تحديث القرار بنجاح"
  }
}
```

Add to `src/assets/i18n/en.json`:

```json
{
  "RESOLUTIONS": {
    "TITLE": "Resolutions",
    "CREATE_NEW": "Create New Resolution",
    "CREATE_TITLE": "Create New Resolution",
    "EDIT_TITLE": "Edit Resolution",
    "CODE": "Resolution Code",
    "DATE": "Resolution Date",
    "DESCRIPTION": "Description",
    "DESCRIPTION_PLACEHOLDER": "Enter resolution description (optional)",
    "TYPE": "Resolution Type",
    "CUSTOM_TYPE": "Custom Type",
    "CUSTOM_TYPE_PLACEHOLDER": "Enter custom resolution type",
    "FILE": "Resolution File",
    "FILE_REQUIREMENTS": "File type: PDF, Maximum size: 10 MB",
    "VOTING_METHODOLOGY": "Voting Methodology",
    "VOTING_RESULT": "Members Voting Result",
    "ALL_ITEMS": "All Items",
    "MAJORITY_ITEMS": "Majority of Items",
    "STATUS": "Status",
    "ACTIONS": "Actions",
    "SAVE_AS_DRAFT": "Save as Draft",
    "SEND": "Send",
    "CREATED_SUCCESSFULLY": "Resolution created successfully",
    "UPDATED_SUCCESSFULLY": "Resolution updated successfully"
  }
}
```

## ✅ Step 7: Testing & Validation

### 7.1 Unit Test Template
Create `resolutions.component.spec.ts`:

```typescript
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MatDialogModule } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';

import { ResolutionsComponent } from './resolutions.component';
import { ResolutionService } from './services/resolution.service';

describe('ResolutionsComponent', () => {
  let component: ResolutionsComponent;
  let fixture: ComponentFixture<ResolutionsComponent>;
  let mockResolutionService: jasmine.SpyObj<ResolutionService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('ResolutionService', ['getResolutionsByFund', 'createResolution']);

    await TestBed.configureTestingModule({
      imports: [
        ResolutionsComponent,
        HttpClientTestingModule,
        MatDialogModule,
        TranslateModule.forRoot()
      ],
      providers: [
        { provide: ResolutionService, useValue: spy },
        {
          provide: ActivatedRoute,
          useValue: { params: of({ fundId: '1' }) }
        }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ResolutionsComponent);
    component = fixture.componentInstance;
    mockResolutionService = TestBed.inject(ResolutionService) as jasmine.SpyObj<ResolutionService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load resolutions on init', () => {
    const mockResponse = {
      data: [{ id: 1, code: 'RES-001', fundId: 1 }],
      totalCount: 1,
      successed: true
    };
    mockResolutionService.getResolutionsByFund.and.returnValue(of(mockResponse));

    component.ngOnInit();

    expect(mockResolutionService.getResolutionsByFund).toHaveBeenCalledWith(1, jasmine.any(Object));
    expect(component.ELEMENT_DATA).toEqual(mockResponse.data);
  });
});
```

### 7.2 Manual Testing Checklist
- [ ] Component loads without errors
- [ ] Table displays resolution data correctly
- [ ] Create button opens dialog
- [ ] Form validation works properly
- [ ] File upload functionality works
- [ ] Save as draft creates draft resolution
- [ ] Send creates pending resolution
- [ ] Edit functionality works for existing resolutions
- [ ] Internationalization works (Arabic/English)
- [ ] Responsive design works on different screen sizes

---

**🎉 Congratulations!** You have successfully implemented the Create Resolution feature following the established JadwaUI architectural patterns. The implementation includes all required functionality with proper validation, internationalization, and integration with existing services.

## 🔄 Sprint 2 Implementation Updates

### ✅ Additional Features Implemented:
- **Card-based UI Design**: Implemented Figma-compliant card layout for resolutions list
- **Advanced Search Modal**: Created comprehensive search dialog with multiple filter options
- **Translation Consolidation**: Merged duplicate RESOLUTIONS sections in translation files
- **Enhanced Testing**: Added comprehensive unit tests for all components

### 🎨 New UI Patterns Discovered:

#### 1. Card-Based List Pattern
```scss
.resolutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;

  .resolution-card {
    background: white;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
  }
}
```

#### 2. Advanced Search Dialog Pattern
```typescript
// Reusable filter interface
export interface SearchFilters {
  search?: string;
  status?: string;
  type?: number;
  fromDate?: string;
  toDate?: string;
  createdBy?: string;
}

// Clean filter data before closing
applyFilters(): void {
  const cleanFilters = Object.keys(filters).reduce((acc: any, key) => {
    if (filters[key] !== '' && filters[key] !== null) {
      acc[key] = filters[key];
    }
    return acc;
  }, {});
  this.dialogRef.close(cleanFilters);
}
```

#### 3. Translation Best Practices
```json
// ✅ Consolidated approach
{
  "INVESTMENT_FUNDS": {
    "RESOLUTIONS": {
      // All resolution-related translations in one place
      "TITLE": "القرارات",
      "SEARCH_PLACEHOLDER": "البحث برقم القرار...",
      "ADVANCED_SEARCH": "بحث متقدم"
    }
  }
}
```

### 🚀 Performance Optimizations:
- **Debounced Search**: Implemented 300ms debounce for real-time search
- **Lazy Loading**: Components use lazy loading for better performance
- **Responsive Design**: Mobile-first approach with proper breakpoints

### 📋 Final Implementation Checklist:
- [x] Card-based UI matching Figma design
- [x] Advanced search modal component
- [x] Arabic and English translations merged and updated
- [x] Responsive design implementation
- [x] Search and filtering functionality
- [x] Unit tests for all components
- [x] Integration with existing architecture
- [x] RTL support for Arabic interface
- [x] Permission-based access control
- [x] Empty state handling
- [x] Status indicators with proper color coding
- [x] Edit/Delete action buttons
- [x] Mock data for demonstration

### 📚 Documentation Created:
- `resolutions-implementation-summary.md` - Comprehensive implementation overview
- Updated translation files with new keys
- Enhanced component documentation with discovered patterns

---

**📝 Final Note**: This implementation provides a solid foundation for the resolutions feature while maintaining consistency with JadwaUI architectural patterns. The new patterns discovered can be reused across other features in the application.
