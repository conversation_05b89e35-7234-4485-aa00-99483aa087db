import { TranslateService } from '@ngx-translate/core';

/**
 * Date formatting utility that formats dates based on the current language
 */
export class DateFormatterUtil {
  
  /**
   * Formats a date based on the current language setting
   * @param date - The date to format
   * @param translateService - The translate service to get current language
   * @param options - Optional Intl.DateTimeFormatOptions to customize formatting
   * @returns Formatted date string
   */
  static formatDate(
    date: Date | string | null | undefined,
    translateService: TranslateService,
    options?: Intl.DateTimeFormatOptions
  ): string {
    if (!date) return '';
    
    // Get current language from TranslateService
    const currentLang = translateService.currentLang || translateService.defaultLang || 'en';
    
    // Set locale based on current language
    const locale = currentLang === 'ar' ? 'ar-SA' : 'en-US';
    
    // Default formatting options
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    };
    
    // Merge with custom options if provided
    const formatOptions = { ...defaultOptions, ...options };
    
    return new Intl.DateTimeFormat(locale, formatOptions).format(new Date(date));
  }

  /**
   * Formats a date for display in tables (date only, no time)
   * @param date - The date to format
   * @param translateService - The translate service to get current language
   * @returns Formatted date string (date only)
   */
  static formatDateOnly(
    date: Date | string | null | undefined,
    translateService: TranslateService
  ): string {
    return this.formatDate(date, translateService, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  }

  /**
   * Formats a date for display with time
   * @param date - The date to format
   * @param translateService - The translate service to get current language
   * @returns Formatted date string with time
   */
  static formatDateTime(
    date: Date | string | null | undefined,
    translateService: TranslateService
  ): string {
    return this.formatDate(date, translateService, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }

  /**
   * Formats a date for display in a short format
   * @param date - The date to format
   * @param translateService - The translate service to get current language
   * @returns Formatted date string in short format
   */
  static formatDateShort(
    date: Date | string | null | undefined,
    translateService: TranslateService
  ): string {
    return this.formatDate(date, translateService, {
      year: '2-digit',
      month: 'short',
      day: 'numeric',
    });
  }

  /**
   * Formats a date for display in a long format
   * @param date - The date to format
   * @param translateService - The translate service to get current language
   * @returns Formatted date string in long format
   */
  static formatDateLong(
    date: Date | string | null | undefined,
    translateService: TranslateService
  ): string {
    return this.formatDate(date, translateService, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  /**
   * Formats time only (no date)
   * @param date - The date to format
   * @param translateService - The translate service to get current language
   * @returns Formatted time string
   */
  static formatTimeOnly(
    date: Date | string | null | undefined,
    translateService: TranslateService
  ): string {
    return this.formatDate(date, translateService, {
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  /**
   * Gets the current locale based on the translate service
   * @param translateService - The translate service to get current language
   * @returns The locale string (ar-SA or en-US)
   */
  static getCurrentLocale(translateService: TranslateService): string {
    const currentLang = translateService.currentLang || translateService.defaultLang || 'en';
    return currentLang === 'ar' ? 'ar-SA' : 'en-US';
  }
}
