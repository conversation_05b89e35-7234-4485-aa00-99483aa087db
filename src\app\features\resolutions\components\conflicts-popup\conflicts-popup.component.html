<div class="conflicts-popup">
  <P class="title">
    {{'INVESTMENT_FUNDS.RESOLUTIONS.MEMBERS_HAVE_CONFLICT' | translate }}
  </P>
  <hr>
  <div class="tag-container">
    <div class="tag" *ngFor="let name of names">
      {{name |json}}
      <span class="name-txt">{{ name.boardMemberName }}</span>
      <!-- <span class="remove-icon" >×</span> -->
      <!-- <img src="assets/icons/cancel-icon.png" class="remove-icon mx-2"  (click)="removeName(name)" alt="cancel"> -->


    </div>
  </div>
  <hr>
  <div class="btn-container">
    <app-custom-button  class="mt-3 w-25" [btnName]="'INVESTMENT_FUNDS.RESOLUTIONS.BACK' | translate"
    [buttonType]="ButtonTypeEnum.OutLine"
    [iconName]="createButtonIcon.arrowNavyRight" (click)="back()"></app-custom-button>

  </div>
</div>
