import { Pipe, PipeTransform } from '@angular/core';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { LanguageService } from '@core/gl-services/language-services/language.service';

@Pipe({
  name: 'arabdtNumberConverter',
  standalone: true,
  pure: false,
})
export class ArabdtNumberConverterPipe implements PipeTransform {

  constructor(private languageService: LanguageService) {}

  transform(value: number | string): string {
    if (value == null) return '';

    const currentLang = JSON.parse(localStorage.getItem('lang') || LanguageEnum.ar);

    return currentLang === 'ar' ? this.convertToArabic(value.toString()) : value.toString();
  }

  private convertToArabic(input: string): string {
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return input.replace(/\d/g, (digit) => arabicNumerals[+digit]);
  }

}
