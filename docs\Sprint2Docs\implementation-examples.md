# 💡 Implementation Examples

## 📋 Table of Contents
- [Complete CRUD Implementation](#complete-crud-implementation)
- [Form Validation Examples](#form-validation-examples)
- [API Integration Patterns](#api-integration-patterns)
- [Error Handling Examples](#error-handling-examples)
- [Configuration Patterns](#configuration-patterns)
- [Testing Examples](#testing-examples)

## 🔄 Complete CRUD Implementation

### Dialog Component Example

```typescript
// feature-dialog.component.ts
import { Component, Inject, ViewChild, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgModel } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-feature-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    TranslateModule
  ],
  template: `
    <div class="dialog-container">
      <h2 mat-dialog-title>
        {{ data.isEdit ? ('FEATURE.EDIT_TITLE' | translate) : ('FEATURE.CREATE_TITLE' | translate) }}
      </h2>
      
      <mat-dialog-content>
        <div class="form-fields">
          <!-- Name Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>{{ 'FEATURE.NAME' | translate }}</mat-label>
            <input matInput
                   [(ngModel)]="formData.name"
                   #nameRef="ngModel"
                   [placeholder]="'FEATURE.NAME_PLACEHOLDER' | translate"
                   required
                   maxlength="100"
                   pattern="^[a-zA-Z\u0600-\u06FF\s]{2,100}$">
            <mat-error *ngIf="nameRef.invalid && nameRef.touched">
              <div *ngIf="nameRef.errors?.['required']">
                {{ 'FORM.ERROR_REQUIRED' | translate }}
              </div>
              <div *ngIf="nameRef.errors?.['pattern']">
                {{ 'FORM.ERROR_PATTERN' | translate }}
              </div>
              <div *ngIf="nameRef.errors?.['duplicate']">
                {{ 'FORM.ERROR_DUPLICATION' | translate }}
              </div>
            </mat-error>
          </mat-form-field>

          <!-- Description Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>{{ 'FEATURE.DESCRIPTION' | translate }}</mat-label>
            <textarea matInput
                      [(ngModel)]="formData.description"
                      #descriptionRef="ngModel"
                      [placeholder]="'FEATURE.DESCRIPTION_PLACEHOLDER' | translate"
                      rows="3"
                      maxlength="500">
            </textarea>
          </mat-form-field>
        </div>
      </mat-dialog-content>

      <mat-dialog-actions align="end">
        <button mat-button (click)="onCancel()">
          {{ 'COMMON.CANCEL' | translate }}
        </button>
        <button mat-raised-button 
                color="primary" 
                (click)="onSubmit()"
                [disabled]="!isFormValid()">
          {{ data.isEdit ? ('COMMON.UPDATE' | translate) : ('COMMON.CREATE' | translate) }}
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .dialog-container {
      padding: 24px;
      min-width: 400px;
    }
    .form-fields {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin: 16px 0;
    }
    .full-width {
      width: 100%;
    }
  `]
})
export class FeatureDialogComponent implements OnInit {
  @ViewChild('nameRef') nameRef!: NgModel;
  @ViewChild('descriptionRef') descriptionRef!: NgModel;

  formData = {
    id: 0,
    name: '',
    description: '',
    isActive: true
  };

  initialData: any = {};

  constructor(
    public dialogRef: MatDialogRef<FeatureDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    if (this.data.isEdit && this.data.item) {
      this.formData = { ...this.data.item };
      this.initialData = { ...this.data.item };
    }
  }

  onSubmit(): void {
    if (!this.validateForm()) {
      return;
    }

    this.dialogRef.close(this.formData);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  isFormValid(): boolean {
    return this.nameRef?.valid && this.hasChanges();
  }

  hasChanges(): boolean {
    if (!this.data.isEdit) return true;
    
    return JSON.stringify(this.formData) !== JSON.stringify(this.initialData);
  }

  private validateForm(): boolean {
    // Check for duplicates
    const isDuplicate = this.data.existingItems?.filteredData?.some((item: any) => {
      const sameName = item.name.toLowerCase() === this.formData.name.toLowerCase();
      const isSameId = this.data.isEdit && item.id === this.formData.id;
      return sameName && !isSameId;
    });

    if (isDuplicate) {
      this.nameRef.control.setErrors({ duplicate: true });
      this.nameRef.control.markAsTouched();
      return false;
    }

    // Mark all fields as touched to show validation errors
    this.nameRef.control.markAsTouched();
    this.descriptionRef?.control.markAsTouched();

    return this.nameRef.valid;
  }
}
```

## ✅ Form Validation Examples

### Custom Validators

```typescript
// custom-validators.ts
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export class CustomValidators {
  static noWhitespace(control: AbstractControl): ValidationErrors | null {
    const isWhitespace = (control.value || '').trim().length === 0;
    const isValid = !isWhitespace;
    return isValid ? null : { whitespace: true };
  }

  static arabicText(control: AbstractControl): ValidationErrors | null {
    const arabicPattern = /^[\u0600-\u06FF\s]+$/;
    const isValid = arabicPattern.test(control.value);
    return isValid ? null : { arabicText: true };
  }

  static englishText(control: AbstractControl): ValidationErrors | null {
    const englishPattern = /^[a-zA-Z\s]+$/;
    const isValid = englishPattern.test(control.value);
    return isValid ? null : { englishText: true };
  }

  static dateRange(startDateKey: string, endDateKey: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const startDate = control.get(startDateKey)?.value;
      const endDate = control.get(endDateKey)?.value;
      
      if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
        return { dateRange: true };
      }
      return null;
    };
  }

  static uniqueInArray(existingValues: string[]): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value?.toLowerCase();
      const isDuplicate = existingValues.some(existing => 
        existing.toLowerCase() === value
      );
      return isDuplicate ? { duplicate: true } : null;
    };
  }
}
```

### Reactive Form Implementation

```typescript
// reactive-form.component.ts
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CustomValidators } from './custom-validators';

@Component({
  selector: 'app-reactive-form',
  standalone: true,
  imports: [ReactiveFormsModule],
  template: `
    <form [formGroup]="featureForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="nameAr">Arabic Name *</label>
        <input id="nameAr" 
               type="text" 
               formControlName="nameAr"
               class="form-control"
               [class.is-invalid]="isFieldInvalid('nameAr')">
        <div class="invalid-feedback" *ngIf="isFieldInvalid('nameAr')">
          <div *ngIf="featureForm.get('nameAr')?.errors?.['required']">
            Arabic name is required
          </div>
          <div *ngIf="featureForm.get('nameAr')?.errors?.['arabicText']">
            Please enter valid Arabic text
          </div>
          <div *ngIf="featureForm.get('nameAr')?.errors?.['duplicate']">
            This name already exists
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="nameEn">English Name *</label>
        <input id="nameEn" 
               type="text" 
               formControlName="nameEn"
               class="form-control"
               [class.is-invalid]="isFieldInvalid('nameEn')">
        <div class="invalid-feedback" *ngIf="isFieldInvalid('nameEn')">
          <div *ngIf="featureForm.get('nameEn')?.errors?.['required']">
            English name is required
          </div>
          <div *ngIf="featureForm.get('nameEn')?.errors?.['englishText']">
            Please enter valid English text
          </div>
        </div>
      </div>

      <button type="submit" 
              class="btn btn-primary"
              [disabled]="featureForm.invalid">
        Submit
      </button>
    </form>
  `
})
export class ReactiveFormComponent implements OnInit {
  featureForm!: FormGroup;
  existingNames: string[] = [];

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.featureForm = this.fb.group({
      nameAr: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        CustomValidators.arabicText,
        CustomValidators.uniqueInArray(this.existingNames)
      ]],
      nameEn: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        CustomValidators.englishText
      ]],
      description: ['', [Validators.maxLength(500)]],
      isActive: [true]
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.featureForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  onSubmit(): void {
    if (this.featureForm.valid) {
      const formData = this.featureForm.value;
      console.log('Form submitted:', formData);
      // Handle form submission
    } else {
      this.markAllFieldsAsTouched();
    }
  }

  private markAllFieldsAsTouched(): void {
    Object.keys(this.featureForm.controls).forEach(key => {
      this.featureForm.get(key)?.markAsTouched();
    });
  }
}
```

## 🌐 API Integration Patterns

### Service with Error Handling

```typescript
// enhanced-feature.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '@environments/environment';

export interface ApiResponse<T> {
  statusCode: number;
  successed: boolean;
  message: string;
  data: T;
  errors: string[];
  totalCount?: number;
}

@Injectable({
  providedIn: 'root'
})
export class EnhancedFeatureService {
  private baseUrl = `${environment.apiUrl}/api/Feature`;
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  constructor(private http: HttpClient) {}

  create(item: any): Observable<ApiResponse<any>> {
    this.setLoading(true);
    return this.http.post<ApiResponse<any>>(`${this.baseUrl}/Create`, item)
      .pipe(
        tap(() => this.setLoading(false)),
        catchError(this.handleError.bind(this))
      );
  }

  update(item: any): Observable<ApiResponse<any>> {
    this.setLoading(true);
    return this.http.put<ApiResponse<any>>(`${this.baseUrl}/Update`, item)
      .pipe(
        tap(() => this.setLoading(false)),
        catchError(this.handleError.bind(this))
      );
  }

  getById(id: number): Observable<any> {
    this.setLoading(true);
    return this.http.get<ApiResponse<any>>(`${this.baseUrl}/GetById/${id}`)
      .pipe(
        map(response => response.data),
        tap(() => this.setLoading(false)),
        catchError(this.handleError.bind(this))
      );
  }

  getList(params: {
    pageNo?: number;
    pageSize?: number;
    search?: string;
    orderBy?: string;
    filters?: any;
  }): Observable<ApiResponse<any[]>> {
    this.setLoading(true);
    
    let httpParams = new HttpParams();
    Object.keys(params).forEach(key => {
      const value = params[key as keyof typeof params];
      if (value !== null && value !== undefined && value !== '') {
        httpParams = httpParams.set(key, value.toString());
      }
    });

    return this.http.get<ApiResponse<any[]>>(`${this.baseUrl}/List`, { params: httpParams })
      .pipe(
        tap(() => this.setLoading(false)),
        catchError(this.handleError.bind(this))
      );
  }

  delete(id: number): Observable<ApiResponse<any>> {
    this.setLoading(true);
    return this.http.delete<ApiResponse<any>>(`${this.baseUrl}/Delete/${id}`)
      .pipe(
        tap(() => this.setLoading(false)),
        catchError(this.handleError.bind(this))
      );
  }

  private setLoading(loading: boolean): void {
    this.loadingSubject.next(loading);
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    this.setLoading(false);
    
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Server Error: ${error.status} - ${error.message}`;
      
      // Handle specific status codes
      switch (error.status) {
        case 400:
          errorMessage = 'Bad Request: Please check your input';
          break;
        case 401:
          errorMessage = 'Unauthorized: Please login again';
          break;
        case 403:
          errorMessage = 'Forbidden: You do not have permission';
          break;
        case 404:
          errorMessage = 'Not Found: The requested resource was not found';
          break;
        case 500:
          errorMessage = 'Internal Server Error: Please try again later';
          break;
      }
    }

    console.error('API Error:', errorMessage, error);
    return throwError(() => new Error(errorMessage));
  }
}
```

## ⚙️ Configuration Patterns

### Environment Configuration

```typescript
// environment.ts
export const environment = {
  production: false,
  apiUrl: 'https://localhost:7010',
  name: 'local',
  features: {
    enableNotifications: true,
    enableAnalytics: false,
    maxFileSize: 5242880, // 5MB
    supportedFileTypes: ['.pdf', '.doc', '.docx', '.jpg', '.png']
  },
  api: {
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000
  }
};
```

### Feature Toggle Service

```typescript
// feature-toggle.service.ts
@Injectable({
  providedIn: 'root'
})
export class FeatureToggleService {
  private features = environment.features;

  isFeatureEnabled(featureName: string): boolean {
    return this.features[featureName as keyof typeof this.features] || false;
  }

  getFeatureConfig(featureName: string): any {
    return this.features[featureName as keyof typeof this.features];
  }
}
```

## 🧪 Testing Examples

### Component Testing

```typescript
// feature.component.spec.ts
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MatDialogModule } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';

import { FeatureComponent } from './feature.component';
import { FeatureService } from './services/feature.service';

describe('FeatureComponent', () => {
  let component: FeatureComponent;
  let fixture: ComponentFixture<FeatureComponent>;
  let mockFeatureService: jasmine.SpyObj<FeatureService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('FeatureService', ['getList', 'create', 'update', 'delete']);

    await TestBed.configureTestingModule({
      imports: [
        FeatureComponent,
        HttpClientTestingModule,
        MatDialogModule,
        TranslateModule.forRoot()
      ],
      providers: [
        { provide: FeatureService, useValue: spy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(FeatureComponent);
    component = fixture.componentInstance;
    mockFeatureService = TestBed.inject(FeatureService) as jasmine.SpyObj<FeatureService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load data on init', () => {
    const mockData = {
      data: [{ id: 1, name: 'Test', description: 'Test Description' }],
      totalCount: 1
    };
    mockFeatureService.getList.and.returnValue(of(mockData));

    component.ngOnInit();

    expect(mockFeatureService.getList).toHaveBeenCalled();
    expect(component.ELEMENT_DATA).toEqual(mockData.data);
    expect(component.totalCount).toBe(1);
  });
});
```

### Service Testing

```typescript
// feature.service.spec.ts
import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { FeatureService } from './feature.service';
import { environment } from '@environments/environment';

describe('FeatureService', () => {
  let service: FeatureService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [FeatureService]
    });
    service = TestBed.inject(FeatureService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create item successfully', () => {
    const mockItem = { name: 'Test', description: 'Test Description' };
    const mockResponse = { successed: true, data: { id: 1, ...mockItem } };

    service.create(mockItem).subscribe(response => {
      expect(response.successed).toBe(true);
      expect(response.data.name).toBe('Test');
    });

    const req = httpMock.expectOne(`${environment.apiUrl}/api/Feature/Create`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(mockItem);
    req.flush(mockResponse);
  });

  it('should handle error responses', () => {
    const mockItem = { name: 'Test' };

    service.create(mockItem).subscribe({
      next: () => fail('Should have failed'),
      error: (error) => {
        expect(error.message).toContain('Server Error');
      }
    });

    const req = httpMock.expectOne(`${environment.apiUrl}/api/Feature/Create`);
    req.flush('Server Error', { status: 500, statusText: 'Internal Server Error' });
  });
});
```

## 🎨 UI Component Examples

### Reusable Confirmation Dialog

```typescript
// confirmation-dialog.component.ts
@Component({
  selector: 'app-confirmation-dialog',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatButtonModule, TranslateModule],
  template: `
    <div class="confirmation-dialog">
      <h2 mat-dialog-title>{{ data.title | translate }}</h2>
      <mat-dialog-content>
        <p>{{ data.message | translate }}</p>
        <div *ngIf="data.details" class="details">
          <strong>{{ data.details }}</strong>
        </div>
      </mat-dialog-content>
      <mat-dialog-actions align="end">
        <button mat-button (click)="onCancel()">
          {{ 'COMMON.CANCEL' | translate }}
        </button>
        <button mat-raised-button
                [color]="data.confirmButtonColor || 'warn'"
                (click)="onConfirm()">
          {{ data.confirmButtonText | translate }}
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .confirmation-dialog {
      padding: 16px;
      min-width: 300px;
    }
    .details {
      margin-top: 16px;
      padding: 12px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
  `]
})
export class ConfirmationDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ConfirmationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: {
      title: string;
      message: string;
      details?: string;
      confirmButtonText: string;
      confirmButtonColor?: string;
    }
  ) {}

  onConfirm(): void {
    this.dialogRef.close(true);
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
```

---

*These examples demonstrate the standard patterns used throughout the application for consistent implementation.*
