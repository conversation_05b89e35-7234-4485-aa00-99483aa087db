# 🏗️ Jadwa Investment Web Application
## 📚 Documentation Hub

> **Comprehensive architectural documentation for a modern Angular-based investment fund management system**

---

## 🌟 Overview

This documentation provides **comprehensive architectural information** for the Jadwa Investment Web Application, a modern Angular-based investment fund management system designed for scalability, security, and performance.

### 🎯 What You'll Find Here
- **🏛️ System Architecture**: Complete system design and patterns
- **⚙️ Service Layer**: API integration and business logic
- **🎨 Component Structure**: Frontend architecture and UI patterns
- **🔐 Security Model**: Authentication and authorization
- **🚀 Deployment Strategy**: Environment configuration and optimization

---

## 📖 Documentation Structure

### 🏛️ [Architecture Overview](./architecture.md)
**📋 Main architectural document covering:**

| Section | Content | Key Features |
|---------|---------|--------------|
| **🏗️ High-Level Architecture** | System overview and principles | Modular design, separation of concerns |
| **🛠️ Technology Stack** | Frameworks and tools | Angular 18+, TypeScript, RxJS |
| **🔄 System Overview** | Application flow and modules | Interactive SVG diagrams |
| **🔧 Low-Level Architecture** | Component structure | Feature-based organization |
| **🔐 Security Architecture** | Authentication and protection | JWT tokens, route guards |
| **🚀 Deployment Architecture** | Environment and deployment | Docker, Nginx, multi-stage builds |

### ⚙️ [Service Architecture](./service-architecture.md)
**🔧 Detailed service layer documentation covering:**

| Topic | Description | Implementation |
|-------|-------------|----------------|
| **🔌 API Service Layer** | NSwag generated clients | Type-safe API communication |
| **🏗️ Business Logic Services** | Core functionality | Authentication, funds, strategies |
| **🔧 Utility Services** | Helper services | Language, file upload, date conversion |
| **🔄 Communication Patterns** | Service interaction | Observables, dependency injection |
| **⚠️ Error Handling** | Error management | Global interceptors, user feedback |
| **📋 Data Models** | Type definitions | DTOs, interfaces, business entities |

### 🎨 [Component Architecture](./component-architecture.md)
**🎨 Frontend component structure documentation covering:**

| Area | Focus | Benefits |
|------|-------|----------|
| **🏗️ Component Hierarchy** | Structure and classification | Smart/dumb components, reusability |
| **🏠 Layout Components** | Application shell | Navigation, headers, responsive design |
| **🎯 Feature Components** | Business functionality | Domain-specific, lazy-loaded modules |
| **🔧 Shared Components** | Reusable UI elements | Form controls, tables, consistent design |
| **🔄 Communication Patterns** | Component interaction | Input/output, services, event handling |
| **💾 State Management** | Data flow strategies | Local state, shared state, observables |
| **🎨 UI/UX Patterns** | Design guidelines | Responsive design, themes, accessibility |

---

## 🚀 Quick Reference

### 🛠️ Technology Stack

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| **🎨 Frontend** | Angular | 18+ | Core framework with standalone components |
| **📝 Language** | TypeScript | 5.4+ | Type-safe development |
| **🎨 Styling** | SCSS + Bootstrap + Material | 5.x | Responsive design and components |
| **🔄 State Management** | RxJS Observables | Latest | Reactive programming patterns |
| **🔌 API Integration** | NSwag TypeScript clients | Latest | Type-safe API communication |
| **🔐 Authentication** | JWT with HTTP interceptors | - | Secure user authentication |
| **🌍 Internationalization** | ngx-translate | Latest | Arabic (RTL) and English (LTR) |
| **🚀 Build & Deploy** | Docker + Nginx | Latest | Containerized deployment |

### ✨ Key Features

| Feature | Description | Technology |
|---------|-------------|------------|
| **🔐 Secure Authentication** | JWT-based with automatic token management | Angular Guards + Interceptors |
| **🌍 Multi-language Support** | Arabic (RTL) and English (LTR) | ngx-translate + CSS RTL |
| **📱 Responsive Design** | Mobile-first approach | Bootstrap 5 + CSS Grid |
| **🔔 Real-time Notifications** | Push notifications and updates | Firebase Cloud Messaging |
| **💰 Investment Fund Management** | Complete CRUD operations | Angular Forms + API Integration |
| **📈 Strategy Management** | Fund strategy configuration | Dynamic forms + validation |
| **🗳️ Voting System** | Employee voting functionality | Real-time updates + security |
| **📄 Document Management** | File upload and management | Drag & drop + cloud storage |

### 🏗️ Architecture Highlights

```mermaid
graph TB
    subgraph "Frontend (Angular)"
        A[Components]
        B[Services]
        C[Guards & Interceptors]
    end
    
    subgraph "Backend Integration"
        D[Generated API Clients]
        E[HTTP Client]
        F[Error Handling]
    end
    
    subgraph "External Services"
        G[.NET Core API]
        H[Firebase FCM]
        I[File Storage]
    end
    
    A --> B
    B --> D
    C --> E
    D --> E
    E --> G
    A --> H
    B --> I
```

### 📁 Project Structure

```
📁 src/app/
├── 🏗️ core/                    # Core functionality & infrastructure
│   ├── 🔌 api/                # Generated API clients (NSwag)
│   ├── 🛡️ guards/             # Route guards & access control
│   ├── 🔄 interceptors/       # HTTP interceptors (auth, error)
│   ├── 🎨 layout/             # Layout components & shells
│   └── ⚙️ services/           # Core business services
├── 🎯 features/               # Feature modules (lazy-loaded)
│   ├── 🔐 auth/               # Authentication & user management
│   ├── 📊 dashboard/          # Main dashboard & analytics
│   ├── 💰 investment-funds/   # Fund management & operations
│   ├── 📈 fund-strategies/    # Strategy configuration
│   └── 🗳️ voting/             # Voting system & governance
├── 🔧 shared/                 # Shared utilities & components
│   ├── 🎨 components/         # Reusable UI components
│   ├── 🔄 pipes/              # Custom transformation pipes
│   ├── 📐 directives/         # Custom Angular directives
│   └── ⚙️ services/           # Utility & helper services
└── 🎭 assets/                 # Static assets & resources
```

#### 📂 Directory Benefits
- **🏗️ Core**: Application foundation and infrastructure
- **🎯 Features**: Business functionality with lazy loading
- **🔧 Shared**: Reusable components and utilities
- **🎭 Assets**: Static resources and configurations

---

## 🚀 Getting Started

### 📋 Prerequisites

| Requirement | Version | Purpose |
|-------------|---------|---------|
| **Node.js** | 18+ | JavaScript runtime environment |
| **Angular CLI** | 18+ | Development and build tools |
| **Docker** | Latest | Containerized deployment (optional) |
| **Git** | Latest | Version control |

### 🔧 Development Setup

```bash
# 📦 Install dependencies
npm install

# 🚀 Start development server
npm run start

# 🏗️ Build for production
npm run build:prod

# 🧪 Run tests
npm run test

# 🔍 Lint code
npm run lint

# 📊 Analyze bundle
npm run analyze
```

### 🌍 Environment Configuration

The application supports **multiple environments** with specific configurations:

| Environment | Purpose | API Endpoint | Features |
|-------------|---------|--------------|----------|
| **🔧 Local** | Development | `localhost:7010` | Hot reload, debugging, dev tools |
| **🧪 Test** | QA Testing | `test-api.jadwa.com` | Staging data, test configurations |
| **🚀 Production** | Live System | `api.jadwa.com` | Optimized builds, monitoring |

### 🔌 API Integration

The application uses **NSwag** to generate TypeScript clients from the backend OpenAPI specification:

```bash
# 🔄 Regenerate API clients
npm run nswag

# 🔍 Validate API schema
npm run api:validate

# 📋 Generate API documentation
npm run api:docs
```

## Key Architectural Decisions

### 1. Standalone Components
- **Decision**: Use Angular standalone components instead of NgModules
- **Rationale**: Simplified dependency management and better tree-shaking
- **Impact**: Reduced bundle size and improved performance

### 2. Service-Oriented Architecture
- **Decision**: Separate business logic into dedicated services
- **Rationale**: Better testability, reusability, and maintainability
- **Impact**: Clear separation of concerns and easier unit testing

### 3. Generated API Clients
- **Decision**: Use NSwag for automatic TypeScript client generation
- **Rationale**: Type safety and consistency with backend API
- **Impact**: Reduced development time and fewer runtime errors

### 4. Reactive Programming
- **Decision**: Use RxJS Observables throughout the application
- **Rationale**: Better handling of asynchronous operations and data streams
- **Impact**: More predictable state management and easier error handling

### 5. Multi-language Support
- **Decision**: Implement comprehensive i18n with RTL support
- **Rationale**: Support for Arabic and English markets
- **Impact**: Broader user base and better user experience

## Security Considerations

### Authentication Flow
1. User credentials sent to backend API
2. JWT tokens received and stored securely
3. Automatic token attachment via HTTP interceptors
4. Token expiry handling with automatic logout
5. Route protection via guards

### Security Features
- **JWT Token Management**: Secure storage and automatic refresh
- **HTTP Interceptors**: Automatic authentication header injection
- **Route Guards**: Protection of admin routes
- **Error Handling**: Secure error messages without sensitive data exposure
- **HTTPS**: Enforced secure communication

## Performance Optimizations

### Frontend Optimizations
- **Lazy Loading**: Feature modules loaded on demand
- **OnPush Change Detection**: Optimized component updates
- **Tree Shaking**: Unused code elimination
- **Code Splitting**: Separate vendor and application bundles
- **Service Workers**: PWA capabilities for offline support

### Build Optimizations
- **Angular CLI**: Optimized production builds
- **Docker Multi-stage**: Efficient container builds
- **Nginx**: Static file serving with compression
- **Asset Optimization**: Image and font optimization

## Monitoring and Maintenance

### Error Tracking
- Global error interceptor for HTTP errors
- User-friendly error messages
- Error logging for debugging

### Performance Monitoring
- Bundle size analysis
- Runtime performance metrics
- User experience tracking

### Maintenance Tasks
- Regular dependency updates
- Security vulnerability scanning
- Code quality checks
- Documentation updates

## Contributing

### Development Guidelines
1. Follow Angular style guide
2. Use TypeScript strict mode
3. Write unit tests for new features
4. Update documentation for architectural changes
5. Follow semantic versioning

### Code Review Process
1. Feature branch development
2. Pull request creation
3. Code review and approval
4. Automated testing
5. Deployment to staging
6. Production deployment

---

## Support and Resources

### Internal Resources
- [Architecture Overview](./architecture.md) - Complete system architecture
- [Service Documentation](./service-architecture.md) - Service layer details
- [Component Guide](./component-architecture.md) - Frontend component structure

### External Resources
- [Angular Documentation](https://angular.dev/)
- [RxJS Documentation](https://rxjs.dev/)
- [Angular Material](https://material.angular.io/)
- [Bootstrap Documentation](https://getbootstrap.com/)

### Contact Information
For questions about the architecture or implementation details, please contact the development team.

---

*Last Updated: 2025-06-17*
*Version: 1.0.0*
