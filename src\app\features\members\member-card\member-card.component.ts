import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { BoardMemberResponse, BoardMemberType } from '@core/api/api.generated';
import { TokenService } from '../../auth/services/token.service';
import { DateHijriConverterPipe } from '../../../shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { DateTime } from 'luxon';
import moment from 'moment';

@Component({
  selector: 'app-member-card',
  standalone: true,
  imports: [CommonModule, TranslateModule, DateHijriConverterPipe],
  templateUrl: './member-card.component.html',
  styleUrl: './member-card.component.scss',
})
export class MemberCardComponent {
  @Input() member!: BoardMemberResponse;
  @Input() canEdit: boolean = false;
  @Input() canDelete: boolean = false;
  @Output() edit = new EventEmitter<BoardMemberResponse>();
  @Output() delete = new EventEmitter<BoardMemberResponse>();

  constructor(public tokenService: TokenService) {}

  get memberTypeDisplay(): string {
    if (this.member.memberTypeDisplay) {
      return this.member.memberTypeDisplay;
    }
    // Fallback to enum-based display
    return this.member.memberType === BoardMemberType._1
      ? 'INVESTMENT_FUNDS.MEMBERS.INDEPENDENT'
      : 'INVESTMENT_FUNDS.MEMBERS.DEPENDENT';
  }

  get statusDisplay(): string {
    if (this.member.statusDisplay) {
      return this.member.statusDisplay;
    }
    // Fallback to boolean-based display
    return this.member.isActive
      ? 'INVESTMENT_FUNDS.MEMBERS.ACTIVE'
      : 'INVESTMENT_FUNDS.MEMBERS.INACTIVE';
  }

  get roleDisplay(): string {
    if (this.member.roleDisplay) {
      return this.member.roleDisplay;
    }
    // Fallback to chairman check
    return this.memberTypeDisplay;
  }
  formatDateToString(dateTime: any): string {
    return moment(dateTime.toJSDate()).format('YYYY-MM-DD');
  }

  onEdit(): void {
    if (this.canEdit) {
      this.edit.emit(this.member);
    }
  }

  onDelete(): void {
    if (this.canDelete) {
      this.delete.emit(this.member);
    }
  }

  getStatusClass(status: any): string {
    if (status === true) {
      return 'active';
    } else if (status === false) {
      return 'in-active';
    }
    return '';
  }
}
