<div class="not-found-container">
  <div class="not-found-content">
    <!-- 404 Icon/Illustration -->
    <div class="error-icon">
      <svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="100" cy="100" r="80" stroke="#e0e0e0" stroke-width="2" fill="none"/>
        <text x="100" y="110" text-anchor="middle" font-size="48" font-weight="bold" fill="#666">404</text>
      </svg>
    </div>

    <!-- Error Message -->
    <div class="error-message">
      <h1 class="error-title">{{ 'NOT_FOUND.TITLE' | translate }}</h1>
      <p class="error-description">{{ 'NOT_FOUND.DESCRIPTION' | translate }}</p>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <app-custom-button
        [btnName]="'NOT_FOUND.GO_BACK' | translate"
        [buttonType]="ButtonTypeEnum.Secondary"
        (click)="goBack()"
        class="back-button">
      </app-custom-button>

      <app-custom-button
        [btnName]="'NOT_FOUND.GO_HOME' | translate"
        [buttonType]="ButtonTypeEnum.Primary"
        (click)="goHome()"
        class="home-button">
      </app-custom-button>
    </div>

    <!-- Additional Help Text -->
    <div class="help-text">
      <p>{{ 'NOT_FOUND.HELP_TEXT' | translate }}</p>
    </div>
  </div>
</div>
