# 📈 Strategy Module Deep Dive

## 📋 Table of Contents
- [Module Overview](#module-overview)
- [Component Architecture](#component-architecture)
- [Service Layer](#service-layer)
- [Data Models](#data-models)
- [CRUD Operations](#crud-operations)
- [Form Handling](#form-handling)
- [API Integration](#api-integration)

## 🎯 Module Overview

The Strategy module (`fund-strategies`) demonstrates the project's standard patterns for implementing CRUD operations, form handling, and data management. It serves as the reference implementation for creating new modules.

### Key Features
- ✅ **List View**: Paginated table with search and sorting
- ✅ **Create Operation**: Modal dialog for adding new strategies
- ✅ **Edit Operation**: Modal dialog for updating existing strategies
- ✅ **Delete Operation**: Confirmation dialog with soft delete
- ✅ **Validation**: Client-side form validation with error messages
- ✅ **Internationalization**: Multi-language support (Arabic/English)
- ✅ **Permission-based Access**: Role-based feature access control

## 🏗️ Component Architecture

### Main Component Structure

```typescript
// fund-strategies.component.ts
@Component({
  selector: 'app-fund-strategies',
  standalone: true,
  imports: [
    CommonModule, 
    TableComponent, 
    PageHeaderComponent, 
    TranslateModule, 
    BreadcrumbComponent
  ],
  templateUrl: './fund-strategies.component.html',
  styleUrl: './fund-strategies.component.scss',
  providers: [DatePipe]
})
export class FundStrategiesComponent implements OnInit {
  // Data properties
  ELEMENT_DATA: PeriodicElement[] = [];
  tableDataSource = new MatTableDataSource<PeriodicElement>();
  totalCount = 0;
  
  // UI state
  isDialogOpen = false;
  
  // Table configuration
  displayedColumns: string[] = ['nameAr', 'nameEn', 'updatedAt', 'actions'];
  columns: ITableColumn[] = [
    // Column definitions...
  ];
}
```

### Component Dependencies

| Dependency | Purpose | Usage |
|------------|---------|-------|
| `TableComponent` | Reusable data table | Display strategy list with pagination |
| `PageHeaderComponent` | Page title and actions | Header with create button |
| `BreadcrumbComponent` | Navigation breadcrumb | Show current page location |
| `StrategyDialogComponent` | Modal dialog | Create/edit strategy forms |
| `StrategyService` | Business logic | API communication |
| `TokenService` | Permission checking | Role-based access control |

## 🔧 Service Layer

### Strategy Service Implementation

```typescript
// strategy.service.ts
@Injectable({
  providedIn: 'root'
})
export class StrategyService {
  baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  // CRUD Operations
  createStrategy(strategy: StrategyDto): Observable<any> {
    const url = `${this.baseUrl}/api/Strategies/CreateStrategy`;
    return this.http.post(url, strategy);
  }

  updateStrategy(strategy: StrategyDto): Observable<any> {
    const url = `${this.baseUrl}/api/Strategies/UpdateStrategy`;
    return this.http.put(url, strategy);
  }

  getStrategyById(id: number): Observable<StrategyDto> {
    const url = `${this.baseUrl}/api/Strategies/GetStrategyById?id=${id}`;
    return this.http.get<StrategyDto>(url);
  }

  strategyList(pageNo: number, pageSize: number, search: string, orderBy: string): Observable<any> {
    const url = `${this.baseUrl}/api/Strategies/StrategyList`;
    return this.http.get(url, {
      params: { pageNo: pageNo.toString(), pageSize: pageSize.toString(), search, orderBy }
    });
  }

  deleteStrategy(id: number): Observable<any> {
    const url = `${this.baseUrl}/api/Strategies/DeleteStrategy?id=${id}`;
    return this.http.delete(url);
  }
}
```

### Service Patterns

1. **Environment Configuration**: Uses `environment.apiUrl` for base URL
2. **HTTP Client**: Leverages Angular's HttpClient for API calls
3. **Observable Pattern**: Returns observables for reactive programming
4. **Type Safety**: Uses TypeScript interfaces for request/response types
5. **Error Handling**: Relies on global HTTP interceptors

## 📊 Data Models

### Core Data Interface

```typescript
// Strategy Data Transfer Object
export interface StrategyDto {
  id: number;
  nameAr: string;      // Arabic name
  nameEn: string;      // English name
  updatedAt: string;   // Last update timestamp
}

// Component-specific interface
export interface PeriodicElement {
  id: number;
  nameAr: string;
  nameEn: string;
  updatedAt: string;
}
```

### API Response Structure

```typescript
// Standard API response format
interface ApiResponse<T> {
  statusCode: number;
  successed: boolean;
  message: string;
  data: T;
  errors: string[];
  totalCount?: number;  // For paginated responses
}
```

## 🔄 CRUD Operations

### Create Operation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Component
    participant D as Dialog
    participant S as Service
    participant A as API

    U->>C: Click "Create" button
    C->>D: Open StrategyDialogComponent
    U->>D: Fill form and submit
    D->>D: Validate form data
    D->>C: Return form data
    C->>S: Call createStrategy()
    S->>A: POST /api/Strategies/CreateStrategy
    A->>S: Return success response
    S->>C: Observable response
    C->>C: Show success message
    C->>C: Refresh data list
```

### Edit Operation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Component
    participant S as Service
    participant D as Dialog
    participant A as API

    U->>C: Click "Edit" action
    C->>S: Call getStrategyById()
    S->>A: GET /api/Strategies/GetStrategyById
    A->>S: Return strategy data
    S->>C: Observable response
    C->>D: Open dialog with data
    U->>D: Modify and submit
    D->>C: Return updated data
    C->>S: Call updateStrategy()
    S->>A: PUT /api/Strategies/UpdateStrategy
    A->>S: Return success response
    C->>C: Refresh data list
```

### List Operation Implementation

```typescript
getStrategyList(pageNo: number, pageSize: number, search: string, orderBy: string) {
  this.strategyService.strategyList(pageNo, pageSize, search, orderBy)
    .subscribe((res: any) => {
      this.ELEMENT_DATA = res.data;
      this.tableDataSource.data = res.data;
      this.totalCount = res.totalCount;
    });
}
```

## 📝 Form Handling

### Dialog Component Structure

```typescript
// strategy-dialog.component.ts
export class StrategyDialogComponent {
  // Form fields
  strategyName: string = '';
  arabicName: string = '';
  
  // Validation references
  @ViewChild('arabicNameRef') arabicNameRef!: NgModel;
  @ViewChild('strategyNameRef') strategyNameRef!: NgModel;
  
  // Form submission
  onSubmit(): void {
    const isArabicNameValid = this.arabicNameRef?.valid;
    const isStrategyNameValid = this.strategyNameRef?.valid;
    
    // Custom validation logic
    const arabicInput = this.arabicName?.trim().toLowerCase();
    const englishInput = this.strategyName?.trim().toLowerCase();
    
    // Check for duplicates
    const isArabicDuplicate = this.data.strategysNames.filteredData
      .some((strategy: any) => {
        const sameArabic = strategy.nameAr.trim().toLowerCase() === arabicInput;
        const isSameId = this.data?.isEdit && strategy.id === this.data.id;
        return sameArabic && !isSameId;
      });
    
    if (isArabicDuplicate || isEnglishDuplicate) {
      // Set custom validation errors
      if (isArabicDuplicate) {
        this.arabicNameRef.control.setErrors({ duplicate: true });
        this.arabicNameRef.control.markAsTouched();
      }
      return;
    }
    
    if (isArabicNameValid && isStrategyNameValid) {
      this.dialogRef.close({
        strategyName: this.strategyName,
        arabicName: this.arabicName,
      });
    }
  }
}
```

### Validation Patterns

1. **Template-driven Forms**: Uses NgModel for two-way binding
2. **Custom Validation**: Implements business logic validation
3. **Real-time Feedback**: Shows errors as user types
4. **Duplicate Prevention**: Checks existing data for duplicates
5. **Internationalized Messages**: Error messages support multiple languages

## 🌐 API Integration

### HTTP Interceptor Integration

The module leverages global HTTP interceptors for:

- **Authentication**: Automatic JWT token injection
- **Language Headers**: Automatic language preference headers
- **Error Handling**: Global error response handling
- **Loading States**: Automatic spinner management

### Environment Configuration

```typescript
// environment.ts
export const environment = {
  production: false,
  apiUrl: 'https://localhost:7010',  // Development API
  name: 'local'
};

// environment.prod.ts
export const environment = {
  production: true,
  apiUrl: 'http://************:44301/',  // Production API
  name: 'production'
};
```

---

*This deep dive demonstrates the standard patterns used throughout the application. Use this as a reference when implementing new modules.*
